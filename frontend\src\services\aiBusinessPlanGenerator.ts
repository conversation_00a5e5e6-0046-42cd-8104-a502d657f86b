/**
 * AI-Powered Business Plan Generator
 * Intelligent business plan creation with Arabic support and MENA market focus
 */

import { OpenAI } from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Types and Interfaces
export interface BusinessPlanInput {
  businessIdea: string;
  industry: 'fintech' | 'healthtech' | 'edtech' | 'other';
  targetMarket: string;
  region: 'saudi' | 'uae' | 'egypt' | 'jordan' | 'lebanon' | 'other';
  language: 'ar' | 'en' | 'both';
  fundingGoal: number;
  timeframe: number; // months
  teamSize: number;
  experience: 'first-time' | 'experienced' | 'serial';
  culturalConsiderations: string[];
}

export interface BusinessPlan {
  executiveSummary: {
    ar?: string;
    en?: string;
  };
  marketAnalysis: {
    marketSize: number;
    targetSegment: string;
    competitorAnalysis: Competitor[];
    marketTrends: string[];
    opportunities: string[];
    threats: string[];
  };
  businessModel: {
    valueProposition: string;
    revenueStreams: RevenueStream[];
    costStructure: CostItem[];
    keyPartners: string[];
    keyActivities: string[];
  };
  financialProjections: {
    revenue: MonthlyProjection[];
    expenses: MonthlyProjection[];
    profitability: MonthlyProjection[];
    fundingRequirements: FundingRequirement[];
    breakEvenAnalysis: BreakEvenAnalysis;
  };
  marketingStrategy: {
    channels: MarketingChannel[];
    budget: number;
    timeline: MarketingMilestone[];
    culturalAdaptations: string[];
  };
  operationalPlan: {
    milestones: Milestone[];
    team: TeamRequirement[];
    technology: TechRequirement[];
    compliance: ComplianceRequirement[];
  };
  riskAnalysis: {
    risks: Risk[];
    mitigationStrategies: string[];
    contingencyPlans: string[];
  };
  appendices: {
    marketResearch: any[];
    financialDetails: any[];
    legalConsiderations: any[];
  };
}

interface Competitor {
  name: string;
  marketShare: number;
  strengths: string[];
  weaknesses: string[];
  differentiators: string[];
}

interface RevenueStream {
  name: string;
  type: 'subscription' | 'transaction' | 'advertising' | 'licensing' | 'other';
  projectedRevenue: number;
  timeline: string;
}

interface MonthlyProjection {
  month: number;
  amount: number;
  assumptions: string[];
}

interface Risk {
  category: 'market' | 'financial' | 'operational' | 'regulatory' | 'cultural';
  description: string;
  probability: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  mitigation: string;
}

class AIBusinessPlanGenerator {
  private openai: OpenAI;
  private gemini: GoogleGenerativeAI;
  private menaMarketData: any; // MENA-specific market intelligence

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.REACT_APP_OPENAI_API_KEY,
    });
    this.gemini = new GoogleGenerativeAI(process.env.REACT_APP_GEMINI_API_KEY!);
    this.loadMenaMarketData();
  }

  private async loadMenaMarketData() {
    // Load MENA-specific market data, regulations, cultural factors
    this.menaMarketData = {
      saudi: {
        marketSize: { fintech: 2.4e9, healthtech: 1.8e9, edtech: 1.2e9 },
        regulations: ['SAMA', 'SFDA', 'MOE'],
        culturalFactors: ['islamic_finance', 'gender_considerations', 'ramadan_impact'],
        keyPlayers: { fintech: ['STC Pay', 'Tamara'], healthtech: ['Vezeeta'], edtech: ['Noon Academy'] }
      },
      uae: {
        marketSize: { fintech: 3.2e9, healthtech: 2.1e9, edtech: 1.5e9 },
        regulations: ['CBUAE', 'DHA', 'KHDA'],
        culturalFactors: ['multicultural', 'business_friendly', 'innovation_hub'],
        keyPlayers: { fintech: ['Liv', 'CBD Now'], healthtech: ['Okadoc'], edtech: ['Alef Education'] }
      },
      egypt: {
        marketSize: { fintech: 1.8e9, healthtech: 1.2e9, edtech: 0.8e9 },
        regulations: ['CBE', 'MOH', 'MOE'],
        culturalFactors: ['large_population', 'price_sensitive', 'arabic_first'],
        keyPlayers: { fintech: ['Fawry', 'ValU'], healthtech: ['Vezeeta'], edtech: ['Nafham'] }
      }
    };
  }

  /**
   * Generate comprehensive business plan using AI
   */
  async generateBusinessPlan(input: BusinessPlanInput): Promise<BusinessPlan> {
    try {
      // 1. Market Analysis
      const marketAnalysis = await this.generateMarketAnalysis(input);
      
      // 2. Business Model
      const businessModel = await this.generateBusinessModel(input);
      
      // 3. Financial Projections
      const financialProjections = await this.generateFinancialProjections(input, businessModel);
      
      // 4. Marketing Strategy
      const marketingStrategy = await this.generateMarketingStrategy(input);
      
      // 5. Operational Plan
      const operationalPlan = await this.generateOperationalPlan(input);
      
      // 6. Risk Analysis
      const riskAnalysis = await this.generateRiskAnalysis(input);
      
      // 7. Executive Summary
      const executiveSummary = await this.generateExecutiveSummary(input, {
        marketAnalysis,
        businessModel,
        financialProjections,
        marketingStrategy
      });

      return {
        executiveSummary,
        marketAnalysis,
        businessModel,
        financialProjections,
        marketingStrategy,
        operationalPlan,
        riskAnalysis,
        appendices: {
          marketResearch: [],
          financialDetails: [],
          legalConsiderations: []
        }
      };
      
    } catch (error) {
      console.error('Error generating business plan:', error);
      throw new Error('Failed to generate business plan');
    }
  }

  /**
   * Generate market analysis with MENA focus
   */
  private async generateMarketAnalysis(input: BusinessPlanInput): Promise<any> {
    const regionData = this.menaMarketData[input.region] || this.menaMarketData.uae;
    
    const prompt = `
    Generate comprehensive market analysis for:
    
    BUSINESS: ${input.businessIdea}
    INDUSTRY: ${input.industry}
    REGION: ${input.region}
    TARGET MARKET: ${input.targetMarket}
    
    REGIONAL CONTEXT:
    - Market Size: $${regionData.marketSize[input.industry] || 1e9}
    - Key Regulations: ${regionData.regulations.join(', ')}
    - Cultural Factors: ${regionData.culturalFactors.join(', ')}
    - Key Players: ${JSON.stringify(regionData.keyPlayers[input.industry] || [])}
    
    Provide detailed analysis including:
    1. Total Addressable Market (TAM)
    2. Serviceable Addressable Market (SAM)
    3. Serviceable Obtainable Market (SOM)
    4. Competitor landscape with MENA focus
    5. Market trends and opportunities
    6. Cultural and regulatory considerations
    
    ${input.language === 'ar' ? 'Provide analysis in Arabic with English financial terms.' : ''}
    
    Format as structured JSON.
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.3,
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  }

  /**
   * Generate business model with revenue projections
   */
  private async generateBusinessModel(input: BusinessPlanInput): Promise<any> {
    const prompt = `
    Design optimal business model for:
    
    BUSINESS: ${input.businessIdea}
    INDUSTRY: ${input.industry}
    REGION: ${input.region}
    FUNDING GOAL: $${input.fundingGoal}
    
    Consider:
    1. Value proposition for MENA market
    2. Revenue streams suitable for ${input.region}
    3. Cost structure optimization
    4. Key partnerships in MENA ecosystem
    5. Cultural adaptations needed
    
    ${input.industry === 'fintech' ? 'Include Islamic finance compliance considerations.' : ''}
    ${input.industry === 'healthtech' ? 'Include healthcare regulations and cultural sensitivities.' : ''}
    ${input.industry === 'edtech' ? 'Include Arabic language learning and cultural education factors.' : ''}
    
    Format as structured JSON with detailed reasoning.
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.4,
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  }

  /**
   * Generate financial projections with MENA market considerations
   */
  private async generateFinancialProjections(input: BusinessPlanInput, businessModel: any): Promise<any> {
    const prompt = `
    Create detailed financial projections for ${input.timeframe} months:
    
    BUSINESS MODEL: ${JSON.stringify(businessModel)}
    FUNDING GOAL: $${input.fundingGoal}
    REGION: ${input.region}
    TEAM SIZE: ${input.teamSize}
    
    Generate:
    1. Monthly revenue projections with growth assumptions
    2. Detailed expense breakdown (salaries, marketing, operations)
    3. Cash flow analysis
    4. Break-even analysis
    5. Funding requirements timeline
    6. ROI projections for investors
    
    Consider MENA-specific factors:
    - Local salary ranges
    - Marketing costs in Arabic markets
    - Regulatory compliance costs
    - Cultural event impacts (Ramadan, Eid)
    
    Format as structured JSON with month-by-month breakdown.
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.2,
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  }

  /**
   * Generate culturally-adapted marketing strategy
   */
  private async generateMarketingStrategy(input: BusinessPlanInput): Promise<any> {
    const prompt = `
    Design marketing strategy for MENA market:
    
    BUSINESS: ${input.businessIdea}
    TARGET: ${input.targetMarket}
    REGION: ${input.region}
    LANGUAGE: ${input.language}
    CULTURAL CONSIDERATIONS: ${input.culturalConsiderations.join(', ')}
    
    Include:
    1. Channel strategy (digital-first for MENA)
    2. Content strategy (Arabic/English mix)
    3. Cultural adaptation requirements
    4. Influencer marketing approach
    5. Social media strategy for MENA
    6. Budget allocation
    7. Timeline and milestones
    
    Consider:
    - Ramadan marketing opportunities
    - Gender-specific considerations
    - Local social media preferences
    - Arabic content creation needs
    
    Format as structured JSON with actionable tactics.
    `;

    const response = await this.openai.chat.completions.create({
      model: "gpt-4",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.4,
    });

    return JSON.parse(response.choices[0].message.content || '{}');
  }

  /**
   * Generate operational plan with MENA considerations
   */
  private async generateOperationalPlan(input: BusinessPlanInput): Promise<any> {
    // Implementation for operational planning
    return {};
  }

  /**
   * Generate risk analysis with regional focus
   */
  private async generateRiskAnalysis(input: BusinessPlanInput): Promise<any> {
    // Implementation for risk analysis
    return {};
  }

  /**
   * Generate executive summary in requested language(s)
   */
  private async generateExecutiveSummary(input: BusinessPlanInput, sections: any): Promise<any> {
    // Implementation for executive summary
    return {};
  }
}

// Export singleton instance
export const aiBusinessPlanGenerator = new AIBusinessPlanGenerator();

// React Hook for easy integration
export const useAIBusinessPlan = () => {
  const generatePlan = async (input: BusinessPlanInput) => {
    return await aiBusinessPlanGenerator.generateBusinessPlan(input);
  };

  return { generatePlan };
};
