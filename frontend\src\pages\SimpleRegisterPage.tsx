import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import { ArrowLeft, ArrowRight, CheckCircle, User, Users, FileText } from 'lucide-react';
import { api } from '../services/api';

interface FormData {
  // Step 1: Basic Information
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  passwordConfirm: string;
  phone: string;
  location: string;
  company: string;
  jobTitle: string;
  bio: string;
  
  // Step 2: Role Selection
  selectedRole: string;
  roleAdditionalInfo: Record<string, any>;
  
  // Step 3: Terms
  agreeToTerms: boolean;
  agreeToPrivacy: boolean;
}

const SimpleRegisterPage: React.FC = () => {
  const { language } = useLanguage();
  const navigate = useNavigate();
  const isRTL = language === 'ar';
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    password: '',
    passwordConfirm: '',
    phone: '',
    location: '',
    company: '',
    jobTitle: '',
    bio: '',
    selectedRole: '',
    roleAdditionalInfo: {},
    agreeToTerms: false,
    agreeToPrivacy: false,
  });

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  const roleOptions = [
    { 
      key: 'entrepreneur', 
      title: language === 'ar' ? 'رائد أعمال' : 'Entrepreneur',
      description: language === 'ar' ? 'لديك فكرة مشروع أو شركة ناشئة' : 'You have a business idea or startup'
    },
    { 
      key: 'mentor', 
      title: language === 'ar' ? 'مرشد' : 'Mentor',
      description: language === 'ar' ? 'تريد مساعدة رواد الأعمال الآخرين' : 'You want to help other entrepreneurs'
    },
    { 
      key: 'investor', 
      title: language === 'ar' ? 'مستثمر' : 'Investor',
      description: language === 'ar' ? 'تبحث عن فرص استثمارية' : 'You are looking for investment opportunities'
    },
    { 
      key: 'user', 
      title: language === 'ar' ? 'مستخدم عادي' : 'Regular User',
      description: language === 'ar' ? 'تريد الاستفادة من المنصة' : 'You want to benefit from the platform'
    }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1:
        if (!formData.firstName.trim()) {
          newErrors.firstName = language === 'ar' ? 'الاسم الأول مطلوب' : 'First name is required';
        }
        if (!formData.lastName.trim()) {
          newErrors.lastName = language === 'ar' ? 'الاسم الأخير مطلوب' : 'Last name is required';
        }
        if (!formData.username.trim()) {
          newErrors.username = language === 'ar' ? 'اسم المستخدم مطلوب' : 'Username is required';
        }
        if (!formData.email.trim()) {
          newErrors.email = language === 'ar' ? 'البريد الإلكتروني مطلوب' : 'Email is required';
        }
        if (!formData.password) {
          newErrors.password = language === 'ar' ? 'كلمة المرور مطلوبة' : 'Password is required';
        }
        if (formData.password !== formData.passwordConfirm) {
          newErrors.passwordConfirm = language === 'ar' ? 'كلمات المرور غير متطابقة' : 'Passwords do not match';
        }
        // Make location optional to match backend serializer
        // if (!formData.location.trim()) {
        //   newErrors.location = language === 'ar' ? 'الموقع مطلوب' : 'Location is required';
        // }
        break;

      case 2:
        if (!formData.selectedRole) {
          newErrors.selectedRole = language === 'ar' ? 'يجب اختيار دور' : 'Please select a role';
        }
        break;

      case 3:
        if (!formData.agreeToTerms) {
          newErrors.agreeToTerms = language === 'ar' ? 'يجب الموافقة على الشروط والأحكام' : 'You must agree to the terms and conditions';
        }
        if (!formData.agreeToPrivacy) {
          newErrors.agreeToPrivacy = language === 'ar' ? 'يجب الموافقة على سياسة الخصوصية' : 'You must agree to the privacy policy';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setLoading(true);
    try {
      const registrationData = {
        username: formData.username,
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        password: formData.password,
        password_confirm: formData.passwordConfirm,
        phone: formData.phone,
        location: formData.location,
        bio: formData.bio,
        company: formData.company,
        job_title: formData.jobTitle,
        language: language,
        selected_role: formData.selectedRole,
        role_additional_info: formData.roleAdditionalInfo
      };

      const response = await api.post('/auth/register-enhanced/', registrationData);
      
      if (response.status === 201) {
        alert(language === 'ar' 
          ? 'تم إنشاء الحساب بنجاح! سيتم مراجعة طلبك قريباً.' 
          : 'Account created successfully! Your application will be reviewed soon.'
        );
        navigate('/login');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      setErrors({ 
        submit: error.response?.data?.message || 
                (language === 'ar' ? 'حدث خطأ أثناء التسجيل' : 'Registration failed') 
      });
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4">
                <User size={32} className="text-purple-400" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">
                {language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'}
              </h3>
              <p className="text-gray-300">
                {language === 'ar' ? 'أدخل معلوماتك الشخصية' : 'Enter your personal information'}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الاسم الأول' : 'First Name'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل الاسم الأول' : 'Enter first name'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.firstName && <p className="text-red-400 text-sm mt-1">{errors.firstName}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'الاسم الأخير' : 'Last Name'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل الاسم الأخير' : 'Enter last name'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.lastName && <p className="text-red-400 text-sm mt-1">{errors.lastName}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'اسم المستخدم' : 'Username'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل اسم المستخدم' : 'Enter username'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.username && <p className="text-red-400 text-sm mt-1">{errors.username}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  {language === 'ar' ? 'البريد الإلكتروني' : 'Email'} <span className="text-red-400">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                  placeholder={language === 'ar' ? 'أدخل البريد الإلكتروني' : 'Enter email'}
                  dir={language === 'ar' ? 'rtl' : 'ltr'}
                />
                {errors.email && <p className="text-red-400 text-sm mt-1">{errors.email}</p>}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4">
                <Users size={32} className="text-purple-400" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">
                {language === 'ar' ? 'اختر دورك' : 'Choose Your Role'}
              </h3>
              <p className="text-gray-300">
                {language === 'ar' ? 'اختر الدور الذي يناسبك' : 'Select the role that suits you'}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {roleOptions.map((role) => (
                <div
                  key={role.key}
                  onClick={() => handleInputChange('selectedRole', role.key)}
                  className={`p-6 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                    formData.selectedRole === role.key
                      ? 'border-purple-500 bg-purple-500/20'
                      : 'border-white/30 bg-white/10 hover:border-purple-400'
                  }`}
                >
                  <h4 className="text-lg font-semibold text-white mb-2">{role.title}</h4>
                  <p className="text-gray-300 text-sm">{role.description}</p>
                </div>
              ))}
            </div>
            {errors.selectedRole && <p className="text-red-400 text-sm mt-1">{errors.selectedRole}</p>}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-600/20 mb-4">
                <CheckCircle size={32} className="text-purple-400" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">
                {language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions'}
              </h3>
              <p className="text-gray-300">
                {language === 'ar' ? 'راجع واقبل الشروط لإكمال التسجيل' : 'Review and accept terms to complete registration'}
              </p>
            </div>

            <div className="space-y-4">
              <div className="border border-white/30 rounded-lg p-4">
                <label className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={formData.agreeToTerms}
                    onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                    className={`mt-1 ${isRTL ? 'ml-3' : 'mr-3'}`}
                  />
                  <span className="text-white">
                    {language === 'ar' ? 'أوافق على الشروط والأحكام' : 'I agree to the Terms and Conditions'}
                  </span>
                </label>
                {errors.agreeToTerms && <p className="text-red-400 text-sm mt-1">{errors.agreeToTerms}</p>}
              </div>

              <div className="border border-white/30 rounded-lg p-4">
                <label className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <input
                    type="checkbox"
                    checked={formData.agreeToPrivacy}
                    onChange={(e) => handleInputChange('agreeToPrivacy', e.target.checked)}
                    className={`mt-1 ${isRTL ? 'ml-3' : 'mr-3'}`}
                  />
                  <span className="text-white">
                    {language === 'ar' ? 'أوافق على سياسة الخصوصية' : 'I agree to the Privacy Policy'}
                  </span>
                </label>
                {errors.agreeToPrivacy && <p className="text-red-400 text-sm mt-1">{errors.agreeToPrivacy}</p>}
              </div>
            </div>

            {errors.submit && (
              <div className="bg-red-500/20 border border-red-500 rounded-lg p-4">
                <p className="text-red-400">{errors.submit}</p>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <Link to="/" className="inline-block mb-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
              Yasmeen AI
            </h1>
          </Link>
          <h2 className="text-2xl font-bold text-white mb-2">
            {language === 'ar' ? 'إنشاء حساب جديد' : 'Create Your Account'}
          </h2>
          <p className="text-gray-300">
            {language === 'ar' ? 'انضم إلى منصة ريادة الأعمال الرائدة في المنطقة' : 'Join the leading entrepreneurship platform in the region'}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="bg-black/30 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-300">
                {language === 'ar' ? 'الخطوة' : 'Step'} {currentStep} {language === 'ar' ? 'من' : 'of'} {totalSteps}
              </span>
              <span className="text-sm text-purple-400">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-white/20 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-purple-600 to-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={(e) => e.preventDefault()} className="bg-black/30 backdrop-blur-sm rounded-lg p-8 shadow-lg border border-white/20 mb-8">
          {renderStep()}
        </form>

        {/* Navigation */}
        <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className={`px-6 py-3 bg-white/20 border border-white/30 rounded-lg font-medium text-white hover:bg-white/30 transition-all duration-300 flex items-center ${
              currentStep === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-glow'
            } ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            {isRTL ? <ArrowRight className="w-4 h-4 ml-2" /> : <ArrowLeft className="w-4 h-4 mr-2" />}
            {language === 'ar' ? 'السابق' : 'Previous'}
          </button>

          {currentStep === totalSteps ? (
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium text-white hover:shadow-glow transition-all duration-300 flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {language === 'ar' ? 'جاري التسجيل...' : 'Creating Account...'}
                </>
              ) : (
                <>
                  <CheckCircle className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {language === 'ar' ? 'إنشاء الحساب' : 'Create Account'}
                </>
              )}
            </button>
          ) : (
            <button
              onClick={handleNext}
              className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium text-white hover:shadow-glow transition-all duration-300 flex items-center"
            >
              {language === 'ar' ? 'التالي' : 'Next'}
              {isRTL ? <ArrowLeft className="w-4 h-4 ml-2" /> : <ArrowRight className="w-4 h-4 mr-2" />}
            </button>
          )}
        </div>

        {/* Login Link */}
        <div className="text-center mt-8">
          <p className="text-gray-300">
            {language === 'ar' ? 'لديك حساب بالفعل؟' : 'Already have an account?'}{' '}
            <Link to="/login" className="text-purple-400 hover:text-purple-300 transition-colors font-semibold">
              {language === 'ar' ? 'تسجيل الدخول' : 'Sign In'}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default SimpleRegisterPage;
