describe('Diagnostic Test - Find Issues', () => {
  it('should diagnose authentication and sidebar issues', () => {
    // Step 1: Test basic page load
    cy.visit('/')
    cy.log('✅ Homepage loads')
    
    // Step 2: Check if login page exists
    cy.visit('/login')
    cy.get('body').should('not.be.empty')
    cy.log('✅ Login page exists')
    
    // Step 3: Test authentication setup
    cy.window().then((win) => {
      // Set up mock authentication
      win.localStorage.setItem('access_token', 'test-token')
      win.localStorage.setItem('user', JSON.stringify({
        id: 1,
        email: '<EMAIL>',
        role: 'user',
        first_name: 'Test',
        last_name: 'User'
      }))
    })
    cy.log('✅ Mock authentication set')
    
    // Step 4: Try to visit dashboard
    cy.visit('/dashboard')
    cy.wait(3000) // Give time for authentication to process
    
    // Step 5: Check what's actually on the page
    cy.get('body').then(($body) => {
      const bodyText = $body.text()
      cy.log('Page content preview:', bodyText.substring(0, 200))
      
      // Check for common elements
      if ($body.find('[data-testid="sidebar"]').length > 0) {
        cy.log('✅ Sidebar found with data-testid="sidebar"')
      } else {
        cy.log('❌ No sidebar with data-testid="sidebar"')
      }
      
      if ($body.find('.sidebar').length > 0) {
        cy.log('✅ Sidebar found with class="sidebar"')
      } else {
        cy.log('❌ No sidebar with class="sidebar"')
      }
      
      if ($body.find('nav').length > 0) {
        cy.log('✅ Nav element found')
      } else {
        cy.log('❌ No nav element found')
      }
      
      // Check for navigation links
      const links = $body.find('a[href^="/"]')
      cy.log(`Found ${links.length} internal links`)
      
      // List first few links
      links.slice(0, 5).each((index, link) => {
        const href = Cypress.$(link).attr('href')
        const text = Cypress.$(link).text().trim()
        cy.log(`Link ${index + 1}: "${text}" -> ${href}`)
      })
    })
    
    // Step 6: Check for specific navigation items
    const expectedNavItems = ['Dashboard', 'Business Plans', 'Profile', 'Settings']
    
    expectedNavItems.forEach(item => {
      cy.get('body').then(($body) => {
        if ($body.find(`a:contains("${item}")`).length > 0) {
          cy.log(`✅ Found navigation item: ${item}`)
        } else {
          cy.log(`❌ Missing navigation item: ${item}`)
        }
      })
    })
    
    // Step 7: Check for test IDs
    const expectedTestIds = [
      'sidebar',
      'sidebar-toggle', 
      'nav-dashboard',
      'nav-business-plans',
      'nav-profile',
      'nav-settings'
    ]
    
    expectedTestIds.forEach(testId => {
      cy.get('body').then(($body) => {
        if ($body.find(`[data-testid="${testId}"]`).length > 0) {
          cy.log(`✅ Found test ID: ${testId}`)
        } else {
          cy.log(`❌ Missing test ID: ${testId}`)
        }
      })
    })
    
    // Step 8: Check authentication state
    cy.window().then((win) => {
      const hasToken = win.localStorage.getItem('access_token')
      const hasUser = win.localStorage.getItem('user')
      
      cy.log(`Token in localStorage: ${hasToken ? 'Yes' : 'No'}`)
      cy.log(`User in localStorage: ${hasUser ? 'Yes' : 'No'}`)
      
      // Check Redux store if available
      const store = (win as any).__REDUX_STORE__ || (win as any).store
      if (store) {
        const state = store.getState()
        cy.log('Redux auth state:', JSON.stringify(state.auth || {}, null, 2))
      } else {
        cy.log('❌ No Redux store found')
      }
    })
    
    // Step 9: Try to interact with sidebar if it exists
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="sidebar"]').length > 0) {
        cy.log('Attempting to interact with sidebar...')
        
        // Try to find and click a navigation item
        cy.get('[data-testid="sidebar"]').within(() => {
          cy.get('a').first().then(($link) => {
            const href = $link.attr('href')
            const text = $link.text().trim()
            cy.log(`Clicking first link: "${text}" -> ${href}`)
            cy.wrap($link).click()
          })
        })
      } else {
        cy.log('❌ Cannot interact with sidebar - not found')
      }
    })
    
    // Step 10: Final status
    cy.log('🔍 Diagnostic test completed')
  })
  
  it('should test simplified authentication flow', () => {
    // Test the loginAs command directly
    cy.log('Testing loginAs command...')
    
    try {
      cy.loginAs('user')
      cy.log('✅ loginAs command executed')
      
      // Check current URL
      cy.url().then((url) => {
        cy.log(`Current URL after login: ${url}`)
      })
      
      // Check authentication state
      cy.window().then((win) => {
        const token = win.localStorage.getItem('token')
        cy.log(`Token after loginAs: ${token ? 'Present' : 'Missing'}`)
      })
      
    } catch (error) {
      cy.log(`❌ loginAs command failed: ${error}`)
    }
  })
})
