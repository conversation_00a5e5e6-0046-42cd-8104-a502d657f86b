/**
 * New Business Plan Page
 * Create comprehensive business plans with templates and AI assistance
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { 
  FileText, 
  Save, 
  ArrowLeft, 
  Bot, 
  Target, 
  Users, 
  DollarSign,
  TrendingUp,
  Layout,
  Lightbulb
} from 'lucide-react';

interface BusinessPlanForm {
  title: string;
  executiveSummary: string;
  businessDescription: string;
  marketAnalysis: string;
  organizationManagement: string;
  serviceProducts: string;
  marketingStrategy: string;
  fundingRequest: string;
  financialProjections: string;
}

const NewBusinessPlanPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [formData, setFormData] = useState<BusinessPlanForm>({
    title: '',
    executiveSummary: '',
    businessDescription: '',
    marketAnalysis: '',
    organizationManagement: '',
    serviceProducts: '',
    marketingStrategy: '',
    fundingRequest: '',
    financialProjections: ''
  });

  const [currentSection, setCurrentSection] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const sections = [
    { key: 'title', title: 'Business Plan Title', icon: FileText },
    { key: 'executiveSummary', title: 'Executive Summary', icon: Target },
    { key: 'businessDescription', title: 'Business Description', icon: Lightbulb },
    { key: 'marketAnalysis', title: 'Market Analysis', icon: TrendingUp },
    { key: 'organizationManagement', title: 'Organization & Management', icon: Users },
    { key: 'serviceProducts', title: 'Service or Product Line', icon: Layout },
    { key: 'marketingStrategy', title: 'Marketing & Sales', icon: Target },
    { key: 'fundingRequest', title: 'Funding Request', icon: DollarSign },
    { key: 'financialProjections', title: 'Financial Projections', icon: TrendingUp }
  ];

  const handleInputChange = (field: keyof BusinessPlanForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // TODO: Implement API call to save business plan
      console.log('Submitting business plan:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate back to business plans list
      navigate('/dashboard/business-plans');
    } catch (error) {
      console.error('Error submitting business plan:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextSection = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(currentSection + 1);
    }
  };

  const prevSection = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const currentSectionData = sections[currentSection];
  const IconComponent = currentSectionData.icon;

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/dashboard/business-plans')}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('businessPlans.backToList', 'Back to Business Plans')}
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FileText className={`text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={32} />
                {t('businessPlans.new.title', 'Create New Business Plan')}
              </h1>
              <p className="text-gray-600 mt-2">
                {t('businessPlans.new.subtitle', 'Build a comprehensive business plan step by step')}
              </p>
            </div>
            
            <button className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
              <Bot size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('ai.getHelp', 'AI Assistant')}
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              {t('businessPlans.progress', 'Progress')}: {currentSection + 1} / {sections.length}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(((currentSection + 1) / sections.length) * 100)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentSection + 1) / sections.length) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit}>
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <IconComponent className={`text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={24} />
              {t(`businessPlans.sections.${currentSectionData.key}`, currentSectionData.title)}
            </h2>
            
            {currentSection === 0 ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessPlans.form.title', 'Business Plan Title')} *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('businessPlans.form.titlePlaceholder', 'Enter your business plan title')}
                  required
                />
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t(`businessPlans.form.${currentSectionData.key}`, currentSectionData.title)}
                </label>
                <textarea
                  value={formData[currentSectionData.key as keyof BusinessPlanForm]}
                  onChange={(e) => handleInputChange(currentSectionData.key as keyof BusinessPlanForm, e.target.value)}
                  rows={12}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t(`businessPlans.form.${currentSectionData.key}Placeholder`, `Enter ${currentSectionData.title.toLowerCase()} details`)}
                />
              </div>
            )}
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <button
              type="button"
              onClick={prevSection}
              disabled={currentSection === 0}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('common.previous', 'Previous')}
            </button>
            
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => navigate('/dashboard/business-plans')}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                {t('common.cancel', 'Cancel')}
              </button>
              
              {currentSection === sections.length - 1 ? (
                <button
                  type="submit"
                  disabled={isSubmitting || !formData.title}
                  className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      {t('common.saving', 'Saving...')}
                    </>
                  ) : (
                    <>
                      <Save size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                      {t('businessPlans.form.submit', 'Create Business Plan')}
                    </>
                  )}
                </button>
              ) : (
                <button
                  type="button"
                  onClick={nextSection}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  {t('common.next', 'Next')}
                </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewBusinessPlanPage;
