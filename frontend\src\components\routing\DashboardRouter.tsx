import React from 'react';
import { UnifiedDashboard } from '../dashboard/unified';

/**
 * ✅ SIMPLIFIED DASHBOARD ROUTER
 *
 * Now uses UnifiedDashboard which automatically handles role-based rendering.
 * Eliminates complex routing logic and dashboard duplication.
 *
 * The UnifiedDashboard component automatically:
 * - Detects user role from auth state
 * - Renders appropriate sections for each role
 * - Provides consistent UI/UX across all roles
 * - Maintains role-specific functionality
 */
const DashboardRouter: React.FC = () => {
  // UnifiedDashboard automatically detects user role and renders appropriate content
  return <UnifiedDashboard />;
};

export default DashboardRouter;
