#!/usr/bin/env node

/**
 * Comprehensive Application Startup and Testing Script
 * Starts backend, frontend, runs tests, and opens browser
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Full Application Test Suite...\n');

// Configuration
const config = {
  backendPort: 8000,
  frontendPort: 3000,
  backendDir: './backend',
  frontendDir: './frontend',
  testTimeout: 30000
};

// Process tracking
let backendProcess = null;
let frontendProcess = null;
let testResults = {
  backend: { status: 'pending', message: '' },
  frontend: { status: 'pending', message: '' },
  integration: { status: 'pending', message: '' }
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
  console.log(`[${timestamp}] ${prefix} ${message}`);
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Check if directories exist
function checkDirectories() {
  log('Checking project structure...', 'info');
  
  if (!fs.existsSync(config.backendDir)) {
    log(`Backend directory not found: ${config.backendDir}`, 'error');
    return false;
  }
  
  if (!fs.existsSync(config.frontendDir)) {
    log(`Frontend directory not found: ${config.frontendDir}`, 'error');
    return false;
  }
  
  if (!fs.existsSync(path.join(config.backendDir, 'manage.py'))) {
    log('Django manage.py not found in backend directory', 'error');
    return false;
  }
  
  if (!fs.existsSync(path.join(config.frontendDir, 'package.json'))) {
    log('package.json not found in frontend directory', 'error');
    return false;
  }
  
  log('Project structure verified ✅', 'success');
  return true;
}

// Start backend server
function startBackend() {
  return new Promise((resolve, reject) => {
    log('Starting Django backend server...', 'info');
    
    const pythonCmd = process.platform === 'win32' ? 'python' : 'python3';
    backendProcess = spawn(pythonCmd, ['manage.py', 'runserver', config.backendPort.toString()], {
      cwd: config.backendDir,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    
    backendProcess.stdout.on('data', (data) => {
      output += data.toString();
      if (output.includes('Starting development server')) {
        log(`Backend server started on port ${config.backendPort} ✅`, 'success');
        testResults.backend = { status: 'success', message: 'Backend server running' };
        resolve();
      }
    });
    
    backendProcess.stderr.on('data', (data) => {
      const error = data.toString();
      if (error.includes('Error') || error.includes('Exception')) {
        log(`Backend error: ${error}`, 'error');
        testResults.backend = { status: 'error', message: error };
        reject(new Error(error));
      }
    });
    
    backendProcess.on('error', (error) => {
      log(`Failed to start backend: ${error.message}`, 'error');
      testResults.backend = { status: 'error', message: error.message };
      reject(error);
    });
    
    // Timeout after 15 seconds
    setTimeout(() => {
      if (testResults.backend.status === 'pending') {
        log('Backend startup timeout', 'warning');
        testResults.backend = { status: 'warning', message: 'Startup timeout, but may be running' };
        resolve(); // Continue anyway
      }
    }, 15000);
  });
}

// Start frontend server
function startFrontend() {
  return new Promise((resolve, reject) => {
    log('Starting React frontend server...', 'info');
    
    const npmCmd = process.platform === 'win32' ? 'npm.cmd' : 'npm';
    frontendProcess = spawn(npmCmd, ['run', 'dev'], {
      cwd: config.frontendDir,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    
    frontendProcess.stdout.on('data', (data) => {
      output += data.toString();
      if (output.includes('Local:') || output.includes('localhost')) {
        log(`Frontend server started on port ${config.frontendPort} ✅`, 'success');
        testResults.frontend = { status: 'success', message: 'Frontend server running' };
        resolve();
      }
    });
    
    frontendProcess.stderr.on('data', (data) => {
      const error = data.toString();
      if (error.includes('Error') && !error.includes('warning')) {
        log(`Frontend error: ${error}`, 'error');
        testResults.frontend = { status: 'error', message: error };
        reject(new Error(error));
      }
    });
    
    frontendProcess.on('error', (error) => {
      log(`Failed to start frontend: ${error.message}`, 'error');
      testResults.frontend = { status: 'error', message: error.message };
      reject(error);
    });
    
    // Timeout after 20 seconds
    setTimeout(() => {
      if (testResults.frontend.status === 'pending') {
        log('Frontend startup timeout', 'warning');
        testResults.frontend = { status: 'warning', message: 'Startup timeout, but may be running' };
        resolve(); // Continue anyway
      }
    }, 20000);
  });
}

// Test backend API
async function testBackendAPI() {
  log('Testing backend API endpoints...', 'info');
  
  try {
    // Test health endpoint
    const response = await fetch(`http://localhost:${config.backendPort}/api/health/`);
    if (response.ok) {
      log('Backend API health check passed ✅', 'success');
      return true;
    } else {
      log(`Backend API health check failed: ${response.status}`, 'warning');
      return false;
    }
  } catch (error) {
    log(`Backend API test failed: ${error.message}`, 'error');
    return false;
  }
}

// Test frontend
async function testFrontend() {
  log('Testing frontend accessibility...', 'info');
  
  try {
    const response = await fetch(`http://localhost:${config.frontendPort}`);
    if (response.ok) {
      log('Frontend accessibility test passed ✅', 'success');
      return true;
    } else {
      log(`Frontend test failed: ${response.status}`, 'warning');
      return false;
    }
  } catch (error) {
    log(`Frontend test failed: ${error.message}`, 'error');
    return false;
  }
}

// Open browser
function openBrowser() {
  log('Opening browser...', 'info');
  
  const urls = [
    `http://localhost:${config.frontendPort}`,
    `http://localhost:${config.frontendPort}/test-validation.html`,
    `http://localhost:${config.backendPort}/admin/`
  ];
  
  urls.forEach(url => {
    const command = process.platform === 'win32' ? 'start' : 
                   process.platform === 'darwin' ? 'open' : 'xdg-open';
    
    exec(`${command} ${url}`, (error) => {
      if (error) {
        log(`Failed to open ${url}: ${error.message}`, 'warning');
      } else {
        log(`Opened ${url} ✅`, 'success');
      }
    });
  });
}

// Generate test report
function generateReport() {
  log('\n' + '='.repeat(60), 'info');
  log('📊 APPLICATION STARTUP REPORT', 'info');
  log('='.repeat(60), 'info');
  
  console.log('\n📋 Service Status:');
  Object.entries(testResults).forEach(([service, result]) => {
    const status = result.status === 'success' ? '✅' : 
                  result.status === 'error' ? '❌' : 
                  result.status === 'warning' ? '⚠️' : '⏳';
    console.log(`  ${status} ${service.toUpperCase()}: ${result.message}`);
  });
  
  const successCount = Object.values(testResults).filter(r => r.status === 'success').length;
  const totalCount = Object.keys(testResults).length;
  const successRate = Math.round((successCount / totalCount) * 100);
  
  console.log(`\n📈 Success Rate: ${successRate}% (${successCount}/${totalCount})`);
  
  if (successRate >= 80) {
    log('\n🎉 Application is ready for testing!', 'success');
    log('🌐 Frontend: http://localhost:3000', 'info');
    log('🔧 Backend API: http://localhost:8000', 'info');
    log('🧪 Test Validation: http://localhost:3000/test-validation.html', 'info');
    log('👑 Admin Panel: http://localhost:8000/admin/', 'info');
  } else {
    log('\n⚠️ Some services failed to start properly', 'warning');
    log('Check the error messages above and try manual startup', 'info');
  }
  
  log('\n🛑 Press Ctrl+C to stop all servers', 'info');
}

// Cleanup function
function cleanup() {
  log('\nShutting down servers...', 'info');
  
  if (backendProcess) {
    backendProcess.kill();
    log('Backend server stopped', 'info');
  }
  
  if (frontendProcess) {
    frontendProcess.kill();
    log('Frontend server stopped', 'info');
  }
  
  process.exit(0);
}

// Main execution
async function main() {
  try {
    // Check project structure
    if (!checkDirectories()) {
      process.exit(1);
    }
    
    // Start servers
    log('Starting servers in parallel...', 'info');
    await Promise.allSettled([
      startBackend(),
      startFrontend()
    ]);
    
    // Wait for servers to fully initialize
    log('Waiting for servers to initialize...', 'info');
    await sleep(5000);
    
    // Test services
    log('Running integration tests...', 'info');
    const backendTest = await testBackendAPI();
    const frontendTest = await testFrontend();
    
    testResults.integration = {
      status: (backendTest && frontendTest) ? 'success' : 'warning',
      message: `Backend: ${backendTest ? 'OK' : 'FAIL'}, Frontend: ${frontendTest ? 'OK' : 'FAIL'}`
    };
    
    // Open browser
    openBrowser();
    
    // Generate report
    generateReport();
    
    // Keep process alive
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    
  } catch (error) {
    log(`Startup failed: ${error.message}`, 'error');
    cleanup();
  }
}

// Run the application
main();
