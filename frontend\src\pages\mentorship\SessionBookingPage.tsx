/**
 * Session Booking Page
 * Book mentorship sessions with calendar integration and payment processing
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicInput } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Calendar, 
  Clock, 
  Video, 
  Phone, 
  Users, 
  DollarSign,
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  MessageSquare,
  FileText
} from 'lucide-react';

interface TimeSlot {
  id: string;
  date: string;
  time: string;
  duration: number;
  available: boolean;
  price: number;
}

interface SessionType {
  id: string;
  name: string;
  nameAr: string;
  icon: React.ReactNode;
  description: string;
  descriptionAr: string;
  duration: number[];
  priceMultiplier: number;
}

interface BookingData {
  mentorId: string;
  sessionType: string;
  date: string;
  time: string;
  duration: number;
  objectives: string;
  questions: string;
  totalPrice: number;
}

const SessionBookingPage: React.FC = () => {
  const { mentorId } = useParams();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [sessionTypes, setSessionTypes] = useState<SessionType[]>([]);
  const [bookingData, setBookingData] = useState<BookingData>({
    mentorId: mentorId || '',
    sessionType: '',
    date: '',
    time: '',
    duration: 60,
    objectives: '',
    questions: '',
    totalPrice: 0
  });
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  const totalSteps = 4;

  useEffect(() => {
    loadBookingData();
  }, [mentorId]);

  const loadBookingData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockSessionTypes: SessionType[] = [
        {
          id: 'video',
          name: 'Video Call',
          nameAr: 'مكالمة فيديو',
          icon: <Video className="w-5 h-5" />,
          description: 'Face-to-face video session via Zoom or Google Meet',
          descriptionAr: 'جلسة فيديو وجهاً لوجه عبر زوم أو جوجل ميت',
          duration: [30, 45, 60, 90],
          priceMultiplier: 1.0
        },
        {
          id: 'phone',
          name: 'Phone Call',
          nameAr: 'مكالمة هاتفية',
          icon: <Phone className="w-5 h-5" />,
          description: 'Voice-only session via phone or WhatsApp',
          descriptionAr: 'جلسة صوتية عبر الهاتف أو واتساب',
          duration: [30, 45, 60],
          priceMultiplier: 0.8
        },
        {
          id: 'in_person',
          name: 'In-Person',
          nameAr: 'لقاء شخصي',
          icon: <Users className="w-5 h-5" />,
          description: 'Face-to-face meeting at agreed location',
          descriptionAr: 'لقاء وجهاً لوجه في موقع متفق عليه',
          duration: [60, 90, 120],
          priceMultiplier: 1.5
        }
      ];

      const mockSlots: TimeSlot[] = [
        {
          id: '1',
          date: '2024-01-25',
          time: '09:00',
          duration: 60,
          available: true,
          price: 150
        },
        {
          id: '2',
          date: '2024-01-25',
          time: '14:00',
          duration: 60,
          available: true,
          price: 150
        },
        {
          id: '3',
          date: '2024-01-26',
          time: '10:00',
          duration: 60,
          available: true,
          price: 150
        },
        {
          id: '4',
          date: '2024-01-26',
          time: '16:00',
          duration: 60,
          available: false,
          price: 150
        },
        {
          id: '5',
          date: '2024-01-27',
          time: '11:00',
          duration: 60,
          available: true,
          price: 150
        }
      ];

      setSessionTypes(mockSessionTypes);
      setAvailableSlots(mockSlots);
    } catch (error) {
      console.error('Error loading booking data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmitBooking = async () => {
    setSubmitting(true);
    try {
      // Submit booking - replace with actual API call
      console.log('Submitting booking:', bookingData);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate to confirmation page
      navigate(`/dashboard/mentorship/booking/confirmation/${bookingData.mentorId}`);
    } catch (error) {
      console.error('Error submitting booking:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const calculatePrice = () => {
    const selectedType = sessionTypes.find(type => type.id === bookingData.sessionType);
    if (!selectedType) return 0;
    
    const basePrice = 150; // Base hourly rate
    const durationMultiplier = bookingData.duration / 60;
    const typeMultiplier = selectedType.priceMultiplier;
    
    return Math.round(basePrice * durationMultiplier * typeMultiplier);
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
                {language === 'ar' ? 'اختر نوع الجلسة' : 'Choose Session Type'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'حدد كيف تريد التواصل مع الموجه' : 'Select how you want to connect with your mentor'}
              </ArabicTypography>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {sessionTypes.map((type) => (
                <div
                  key={type.id}
                  onClick={() => setBookingData({...bookingData, sessionType: type.id})}
                  className={`p-6 border-2 rounded-lg cursor-pointer transition-all ${
                    bookingData.sessionType === type.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div className={`w-12 h-12 mx-auto mb-4 rounded-full flex items-center justify-center ${
                      bookingData.sessionType === type.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {type.icon}
                    </div>
                    <ArabicTypography variant="h5" className="font-semibold mb-2">
                      {language === 'ar' ? type.nameAr : type.name}
                    </ArabicTypography>
                    <ArabicTypography variant="body2" color="secondary" className="mb-4">
                      {language === 'ar' ? type.descriptionAr : type.description}
                    </ArabicTypography>
                    <div className="text-sm text-gray-600 font-arabic">
                      {language === 'ar' ? 'المدة المتاحة:' : 'Available durations:'} {type.duration.join(', ')} {language === 'ar' ? 'دقيقة' : 'min'}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {bookingData.sessionType && (
              <div className="mt-6">
                <ArabicTypography variant="h5" className="font-semibold mb-4">
                  {language === 'ar' ? 'اختر مدة الجلسة' : 'Select Session Duration'}
                </ArabicTypography>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {sessionTypes.find(type => type.id === bookingData.sessionType)?.duration.map((duration) => (
                    <button
                      key={duration}
                      onClick={() => setBookingData({...bookingData, duration})}
                      className={`p-3 border rounded-lg text-center transition-all font-arabic ${
                        bookingData.duration === duration
                          ? 'border-blue-500 bg-blue-50 text-blue-600'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {duration} {language === 'ar' ? 'دقيقة' : 'min'}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Calendar className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
                {language === 'ar' ? 'اختر التاريخ والوقت' : 'Choose Date & Time'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'حدد الوقت المناسب لجلستك' : 'Select a convenient time for your session'}
              </ArabicTypography>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Calendar would go here - simplified for demo */}
              <div>
                <ArabicTypography variant="h5" className="font-semibold mb-4">
                  {language === 'ar' ? 'المواعيد المتاحة' : 'Available Slots'}
                </ArabicTypography>
                <div className="space-y-3">
                  {availableSlots.filter(slot => slot.available).map((slot) => (
                    <div
                      key={slot.id}
                      onClick={() => setBookingData({
                        ...bookingData,
                        date: slot.date,
                        time: slot.time
                      })}
                      className={`p-4 border rounded-lg cursor-pointer transition-all ${
                        bookingData.date === slot.date && bookingData.time === slot.time
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div>
                          <ArabicTypography variant="body1" className="font-semibold">
                            {new Date(slot.date).toLocaleDateString()}
                          </ArabicTypography>
                          <ArabicTypography variant="body2" color="secondary">
                            {slot.time} • {slot.duration} {language === 'ar' ? 'دقيقة' : 'min'}
                          </ArabicTypography>
                        </div>
                        <div className="text-right">
                          <ArabicTypography variant="body1" className="font-semibold">
                            ${slot.price}
                          </ArabicTypography>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Selected slot summary */}
              {bookingData.date && bookingData.time && (
                <div>
                  <ArabicTypography variant="h5" className="font-semibold mb-4">
                    {language === 'ar' ? 'ملخص الحجز' : 'Booking Summary'}
                  </ArabicTypography>
                  <ArabicCard>
                    <div className="space-y-3">
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-600 font-arabic">
                          {language === 'ar' ? 'نوع الجلسة:' : 'Session Type:'}
                        </span>
                        <span className="font-semibold font-arabic">
                          {sessionTypes.find(type => type.id === bookingData.sessionType)?.[language === 'ar' ? 'nameAr' : 'name']}
                        </span>
                      </div>
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-600 font-arabic">
                          {language === 'ar' ? 'التاريخ:' : 'Date:'}
                        </span>
                        <span className="font-semibold font-arabic">
                          {new Date(bookingData.date).toLocaleDateString()}
                        </span>
                      </div>
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-600 font-arabic">
                          {language === 'ar' ? 'الوقت:' : 'Time:'}
                        </span>
                        <span className="font-semibold font-arabic">
                          {bookingData.time}
                        </span>
                      </div>
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-600 font-arabic">
                          {language === 'ar' ? 'المدة:' : 'Duration:'}
                        </span>
                        <span className="font-semibold font-arabic">
                          {bookingData.duration} {language === 'ar' ? 'دقيقة' : 'min'}
                        </span>
                      </div>
                      <hr />
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className="text-gray-900 font-semibold font-arabic">
                          {language === 'ar' ? 'المجموع:' : 'Total:'}
                        </span>
                        <span className="text-lg font-bold text-blue-600 font-arabic">
                          ${calculatePrice()}
                        </span>
                      </div>
                    </div>
                  </ArabicCard>
                </div>
              )}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <FileText className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
                {language === 'ar' ? 'أهداف الجلسة' : 'Session Objectives'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'ساعد الموجه على فهم احتياجاتك بشكل أفضل' : 'Help your mentor understand your needs better'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'ما هي أهدافك من هذه الجلسة؟' : 'What are your objectives for this session?'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={bookingData.objectives}
                  onChange={(e) => setBookingData({...bookingData, objectives: e.target.value})}
                  placeholder={language === 'ar' ? 'مثال: أريد مناقشة استراتيجية التسويق لشركتي الناشئة...' : 'e.g., I want to discuss marketing strategy for my startup...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'أسئلة محددة تريد مناقشتها (اختياري)' : 'Specific questions you want to discuss (optional)'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={bookingData.questions}
                  onChange={(e) => setBookingData({...bookingData, questions: e.target.value})}
                  placeholder={language === 'ar' ? 'اكتب أي أسئلة محددة تريد طرحها...' : 'Write any specific questions you want to ask...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <AlertCircle className={`w-5 h-5 text-blue-600 ${isRTL ? 'ml-3' : 'mr-3'} mt-0.5`} />
                  <div>
                    <ArabicTypography variant="body2" className="text-blue-800 font-semibold mb-1">
                      {language === 'ar' ? 'نصيحة للحصول على أفضل النتائج:' : 'Tip for best results:'}
                    </ArabicTypography>
                    <ArabicTypography variant="body2" className="text-blue-700">
                      {language === 'ar' 
                        ? 'كن محدداً في أهدافك وأسئلتك. سيساعد هذا الموجه على تحضير جلسة أكثر فعالية وتخصيصاً لاحتياجاتك.'
                        : 'Be specific about your goals and questions. This will help your mentor prepare a more effective and tailored session for your needs.'
                      }
                    </ArabicTypography>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
                {language === 'ar' ? 'مراجعة وتأكيد الحجز' : 'Review & Confirm Booking'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'راجع تفاصيل حجزك قبل التأكيد' : 'Review your booking details before confirming'}
              </ArabicTypography>
            </div>

            <ArabicCard>
              <ArabicTypography variant="h5" className="font-semibold mb-4">
                {language === 'ar' ? 'تفاصيل الحجز' : 'Booking Details'}
              </ArabicTypography>
              
              <div className="space-y-4">
                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-gray-600 font-arabic">
                    {language === 'ar' ? 'نوع الجلسة:' : 'Session Type:'}
                  </span>
                  <span className="font-semibold font-arabic">
                    {sessionTypes.find(type => type.id === bookingData.sessionType)?.[language === 'ar' ? 'nameAr' : 'name']}
                  </span>
                </div>
                
                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-gray-600 font-arabic">
                    {language === 'ar' ? 'التاريخ والوقت:' : 'Date & Time:'}
                  </span>
                  <span className="font-semibold font-arabic">
                    {new Date(bookingData.date).toLocaleDateString()} • {bookingData.time}
                  </span>
                </div>
                
                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-gray-600 font-arabic">
                    {language === 'ar' ? 'المدة:' : 'Duration:'}
                  </span>
                  <span className="font-semibold font-arabic">
                    {bookingData.duration} {language === 'ar' ? 'دقيقة' : 'minutes'}
                  </span>
                </div>
                
                <hr />
                
                <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-lg font-semibold text-gray-900 font-arabic">
                    {language === 'ar' ? 'المجموع:' : 'Total Amount:'}
                  </span>
                  <span className="text-xl font-bold text-blue-600 font-arabic">
                    ${calculatePrice()}
                  </span>
                </div>
              </div>
            </ArabicCard>

            {bookingData.objectives && (
              <ArabicCard>
                <ArabicTypography variant="h5" className="font-semibold mb-2">
                  {language === 'ar' ? 'أهداف الجلسة' : 'Session Objectives'}
                </ArabicTypography>
                <ArabicTypography variant="body2" className="text-gray-700">
                  {bookingData.objectives}
                </ArabicTypography>
              </ArabicCard>
            )}

            {bookingData.questions && (
              <ArabicCard>
                <ArabicTypography variant="h5" className="font-semibold mb-2">
                  {language === 'ar' ? 'الأسئلة المحددة' : 'Specific Questions'}
                </ArabicTypography>
                <ArabicTypography variant="body2" className="text-gray-700">
                  {bookingData.questions}
                </ArabicTypography>
              </ArabicCard>
            )}

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <AlertCircle className={`w-5 h-5 text-yellow-600 ${isRTL ? 'ml-3' : 'mr-3'} mt-0.5`} />
                <div>
                  <ArabicTypography variant="body2" className="text-yellow-800 font-semibold mb-1">
                    {language === 'ar' ? 'سياسة الإلغاء:' : 'Cancellation Policy:'}
                  </ArabicTypography>
                  <ArabicTypography variant="body2" className="text-yellow-700">
                    {language === 'ar' 
                      ? 'يمكن إلغاء الجلسة مجاناً حتى 24 ساعة قبل الموعد المحدد. الإلغاء بعد ذلك سيؤدي إلى خصم 50% من المبلغ.'
                      : 'Free cancellation up to 24 hours before the scheduled session. Cancellations after that will incur a 50% charge.'
                    }
                  </ArabicTypography>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <ArabicButton
              variant="ghost"
              onClick={() => navigate(-1)}
              icon={<ArrowLeft className="w-4 h-4" />}
              className={isRTL ? 'ml-4' : 'mr-4'}
            >
              {language === 'ar' ? 'رجوع' : 'Back'}
            </ArabicButton>
            <ArabicTypography variant="h1" className="text-gray-900 font-bold">
              {language === 'ar' ? 'حجز جلسة إرشاد' : 'Book Mentorship Session'}
            </ArabicTypography>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            {Array.from({ length: totalSteps }, (_, i) => (
              <div key={i} className={`flex items-center ${i < totalSteps - 1 ? 'flex-1' : ''}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                  i + 1 <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                }`}>
                  {i + 1}
                </div>
                {i < totalSteps - 1 && (
                  <div className={`flex-1 h-1 mx-4 ${
                    i + 1 < currentStep ? 'bg-blue-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className={`flex justify-between mt-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span className="text-sm text-gray-600 font-arabic">
              {language === 'ar' ? 'نوع الجلسة' : 'Session Type'}
            </span>
            <span className="text-sm text-gray-600 font-arabic">
              {language === 'ar' ? 'التاريخ والوقت' : 'Date & Time'}
            </span>
            <span className="text-sm text-gray-600 font-arabic">
              {language === 'ar' ? 'الأهداف' : 'Objectives'}
            </span>
            <span className="text-sm text-gray-600 font-arabic">
              {language === 'ar' ? 'التأكيد' : 'Confirm'}
            </span>
          </div>
        </div>

        {/* Step Content */}
        <ArabicCard className="mb-8">
          {renderStep()}
        </ArabicCard>

        {/* Navigation Buttons */}
        <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicButton
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            {language === 'ar' ? 'السابق' : 'Previous'}
          </ArabicButton>

          {currentStep === totalSteps ? (
            <ArabicButton
              onClick={handleSubmitBooking}
              disabled={submitting || !bookingData.sessionType || !bookingData.date || !bookingData.time}
              icon={submitting ? undefined : <DollarSign className="w-4 h-4" />}
            >
              {submitting 
                ? (language === 'ar' ? 'جاري الحجز...' : 'Booking...')
                : (language === 'ar' ? `تأكيد الحجز - $${calculatePrice()}` : `Confirm Booking - $${calculatePrice()}`)
              }
            </ArabicButton>
          ) : (
            <ArabicButton
              onClick={handleNext}
              disabled={
                (currentStep === 1 && (!bookingData.sessionType || !bookingData.duration)) ||
                (currentStep === 2 && (!bookingData.date || !bookingData.time))
              }
            >
              {language === 'ar' ? 'التالي' : 'Next'}
            </ArabicButton>
          )}
        </div>
      </div>
    </div>
  );
};

export default SessionBookingPage;
