describe('Simple Sidebar Test', () => {
  beforeEach(() => {
    // Set up authentication
    cy.window().then((win) => {
      win.localStorage.setItem('access_token', 'test-token')
      win.localStorage.setItem('user', JSON.stringify({
        id: 1,
        email: '<EMAIL>',
        role: 'user',
        first_name: 'Test',
        last_name: 'User'
      }))
    })
    
    // Visit dashboard
    cy.visit('/dashboard')
    cy.wait(2000) // Give time for components to load
  })

  it('should display sidebar with navigation items', () => {
    // Check if sidebar exists
    cy.get('[data-testid="sidebar"]').should('exist').and('be.visible')
    
    // Check for navigation items (using more flexible selectors)
    cy.get('[data-testid="sidebar"]').within(() => {
      // Look for navigation links
      cy.get('a').should('have.length.greaterThan', 0)
      
      // Check for common navigation items
      cy.contains('Dashboard').should('exist')
      
      // Check if we can find navigation items by test ID
      cy.get('[data-testid^="nav-"]').should('have.length.greaterThan', 0)
    })
  })

  it('should allow navigation between pages', () => {
    // Test navigation using the actual navigation structure
    cy.get('[data-testid="sidebar"]').should('be.visible')
    
    // Try to click on a navigation item
    cy.get('[data-testid="sidebar"]').within(() => {
      // Find the first navigation link and click it
      cy.get('a[href^="/"]').first().then(($link) => {
        const href = $link.attr('href')
        const text = $link.text().trim()
        
        cy.log(`Clicking navigation item: "${text}" -> ${href}`)
        cy.wrap($link).click()
        
        // Verify navigation occurred
        cy.url().should('include', href)
      })
    })
  })

  it('should have working sidebar toggle', () => {
    // Check if sidebar toggle exists
    cy.get('[data-testid="sidebar-toggle"]').should('exist').and('be.visible')
    
    // Click the toggle
    cy.get('[data-testid="sidebar-toggle"]').click()
    
    // Wait for animation
    cy.wait(500)
    
    // Check if sidebar state changed (this might vary based on implementation)
    cy.get('[data-testid="sidebar"]').should('exist')
    
    // Click toggle again to restore
    cy.get('[data-testid="sidebar-toggle"]').click()
    cy.wait(500)
  })

  it('should display user information', () => {
    cy.get('[data-testid="sidebar"]').within(() => {
      // Look for user information (flexible approach)
      cy.get('body').then(($body) => {
        // Check for user name, email, or profile information
        const sidebarText = $body.find('[data-testid="sidebar"]').text()
        
        if (sidebarText.includes('Test') || sidebarText.includes('User') || sidebarText.includes('test@')) {
          cy.log('✅ User information found in sidebar')
        } else {
          cy.log('ℹ️ User information not visible in sidebar (may be in collapsed state)')
        }
      })
    })
  })

  it('should be responsive on mobile', () => {
    // Test mobile viewport
    cy.viewport(375, 667)
    cy.wait(500)
    
    // Check if sidebar adapts to mobile
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="mobile-menu-button"]').length > 0) {
        cy.log('✅ Mobile menu button found')
        cy.get('[data-testid="mobile-menu-button"]').should('be.visible')
      } else if ($body.find('[data-testid="sidebar"]').length > 0) {
        cy.log('ℹ️ Sidebar still visible on mobile (may be responsive design)')
        cy.get('[data-testid="sidebar"]').should('exist')
      } else {
        cy.log('⚠️ No mobile navigation found')
      }
    })
    
    // Reset to desktop
    cy.viewport(1280, 720)
  })

  it('should handle navigation errors gracefully', () => {
    // Test navigation to a non-existent page
    cy.visit('/non-existent-page', { failOnStatusCode: false })
    
    // Should either show 404 page or redirect
    cy.get('body').should('not.be.empty')
    
    // Go back to dashboard
    cy.visit('/dashboard')
    cy.get('[data-testid="sidebar"]').should('be.visible')
  })

  it('should maintain authentication state', () => {
    // Check that authentication persists during navigation
    cy.window().then((win) => {
      const token = win.localStorage.getItem('access_token')
      expect(token).to.equal('test-token')
    })
    
    // Navigate to different page and check auth state
    cy.visit('/profile', { failOnStatusCode: false })
    
    cy.window().then((win) => {
      const token = win.localStorage.getItem('access_token')
      expect(token).to.equal('test-token')
    })
  })

  it('should show appropriate navigation items for user role', () => {
    cy.get('[data-testid="sidebar"]').within(() => {
      // Check for user-specific navigation items
      cy.contains('Dashboard').should('exist')
      
      // User should NOT see admin items
      cy.get('body').then(($body) => {
        const sidebarText = $body.find('[data-testid="sidebar"]').text()
        
        if (sidebarText.includes('User Management') || sidebarText.includes('System Settings')) {
          cy.log('⚠️ User can see admin navigation items')
        } else {
          cy.log('✅ User navigation properly filtered')
        }
      })
    })
  })
})
