"""
Password Reset Models
Handles secure password reset tokens and functionality
"""

import uuid
import secrets
from datetime import timed<PERSON>ta
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.contrib.auth.hashers import make_password, check_password


class PasswordResetToken(models.Model):
    """
    Model to store secure password reset tokens
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='password_reset_tokens')
    token = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    secure_hash = models.CharField(max_length=128)  # Hashed version of the token for security
    email = models.EmailField()  # Email where reset was requested
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.Bo<PERSON>anField(default=False)
    used_at = models.DateTimeField(null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    class Meta:
        db_table = 'password_reset_tokens'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['user', 'is_used']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['email']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.expires_at:
            # Token expires in 1 hour for security
            self.expires_at = timezone.now() + timedelta(hours=1)
        
        # Generate secure hash if not provided
        if not self.secure_hash:
            # Generate a random secret for additional security
            secret = secrets.token_urlsafe(32)
            self.secure_hash = make_password(f"{self.token}:{secret}")
        
        super().save(*args, **kwargs)
    
    @property
    def is_expired(self):
        """Check if the token has expired"""
        return timezone.now() > self.expires_at
    
    @property
    def is_valid(self):
        """Check if the token is valid (not used and not expired)"""
        return not self.is_used and not self.is_expired
    
    def mark_as_used(self):
        """Mark the token as used"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save()
    
    def verify_token(self, token_string):
        """
        Verify the token against the stored hash
        """
        if not self.is_valid:
            return False
        
        # Check if the token matches our stored hash
        return check_password(f"{token_string}:secret", self.secure_hash)
    
    def send_reset_email(self, request=None):
        """
        Send password reset email to the user
        """
        try:
            # Build reset URL
            if request:
                domain = request.get_host()
                protocol = 'https' if request.is_secure() else 'http'
            else:
                domain = getattr(settings, 'FRONTEND_DOMAIN', 'localhost:3003')
                protocol = 'https' if 'localhost' not in domain else 'http'
            
            reset_url = f"{protocol}://{domain}/reset-password/{self.token}"
            
            # Prepare email context
            context = {
                'user': self.user,
                'reset_url': reset_url,
                'token': self.token,
                'expires_at': self.expires_at,
                'site_name': getattr(settings, 'SITE_NAME', 'Yasmeen AI'),
                'ip_address': self.ip_address,
                'user_agent': self.user_agent[:100] if self.user_agent else 'Unknown',
            }
            
            # Render email templates
            html_message = render_to_string('emails/password_reset.html', context)
            plain_message = strip_tags(html_message)
            
            # Send email
            send_mail(
                subject=f'Password Reset Request - {context["site_name"]}',
                message=plain_message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=[self.email],
                html_message=html_message,
                fail_silently=False,
            )
            
            return True
            
        except Exception as e:
            print(f"Failed to send password reset email: {e}")
            return False
    
    @classmethod
    def create_for_user(cls, user, email=None, ip_address=None, user_agent=None):
        """
        Create a new password reset token for a user
        """
        # Use user's email if not provided
        if not email:
            email = user.email
        
        # Invalidate any existing tokens for this user
        cls.objects.filter(
            user=user,
            is_used=False
        ).update(is_used=True, used_at=timezone.now())
        
        # Create new token
        token = cls.objects.create(
            user=user,
            email=email,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        return token
    
    @classmethod
    def reset_password(cls, token_uuid, new_password):
        """
        Reset password using a valid token
        """
        try:
            token = cls.objects.get(token=token_uuid)
            
            if not token.is_valid:
                return False, "Token is invalid or expired"
            
            # Update user's password
            user = token.user
            user.set_password(new_password)
            user.save()
            
            # Mark token as used
            token.mark_as_used()
            
            # Invalidate all other reset tokens for this user
            cls.objects.filter(
                user=user,
                is_used=False
            ).exclude(pk=token.pk).update(is_used=True, used_at=timezone.now())
            
            return True, "Password reset successfully"
            
        except cls.DoesNotExist:
            return False, "Invalid token"
        except Exception as e:
            return False, f"Password reset failed: {str(e)}"
    
    @classmethod
    def cleanup_expired_tokens(cls):
        """
        Clean up expired tokens (should be run periodically)
        """
        expired_count = cls.objects.filter(
            expires_at__lt=timezone.now(),
            is_used=False
        ).update(is_used=True, used_at=timezone.now())
        
        return expired_count
    
    def __str__(self):
        return f"Password reset for {self.user.username} ({self.email})"


class PasswordResetAttempt(models.Model):
    """
    Track password reset attempts for rate limiting and security monitoring
    """
    email = models.EmailField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    attempted_at = models.DateTimeField(auto_now_add=True)
    success = models.BooleanField(default=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='password_reset_attempts')
    
    class Meta:
        db_table = 'password_reset_attempts'
        ordering = ['-attempted_at']
        indexes = [
            models.Index(fields=['email', 'attempted_at']),
            models.Index(fields=['ip_address', 'attempted_at']),
            models.Index(fields=['user', 'attempted_at']),
        ]
    
    @classmethod
    def can_request_reset(cls, email, ip_address=None):
        """
        Check if a password reset can be requested
        Rate limiting: max 3 attempts per email per hour, max 5 per IP per hour
        """
        one_hour_ago = timezone.now() - timedelta(hours=1)
        
        # Check email-based rate limiting
        email_attempts = cls.objects.filter(
            email=email,
            attempted_at__gte=one_hour_ago
        ).count()
        
        if email_attempts >= 3:
            return False, "Too many password reset attempts for this email. Please try again in an hour."
        
        # Check IP-based rate limiting if IP is provided
        if ip_address:
            ip_attempts = cls.objects.filter(
                ip_address=ip_address,
                attempted_at__gte=one_hour_ago
            ).count()
            
            if ip_attempts >= 5:
                return False, "Too many password reset attempts from this IP address. Please try again in an hour."
        
        return True, "Can request password reset"
    
    @classmethod
    def record_attempt(cls, email, success=False, ip_address=None, user_agent=None, user=None):
        """
        Record a password reset attempt
        """
        return cls.objects.create(
            email=email,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            user=user
        )
    
    def __str__(self):
        return f"Password reset attempt for {self.email} at {self.attempted_at}"


class PasswordResetSettings(models.Model):
    """
    Global settings for password reset functionality
    """
    token_expiry_hours = models.PositiveIntegerField(
        default=1,
        help_text="Hours until password reset token expires"
    )
    max_attempts_per_email_per_hour = models.PositiveIntegerField(
        default=3,
        help_text="Maximum password reset attempts per email per hour"
    )
    max_attempts_per_ip_per_hour = models.PositiveIntegerField(
        default=5,
        help_text="Maximum password reset attempts per IP per hour"
    )
    require_email_verification = models.BooleanField(
        default=True,
        help_text="Require email to be verified before allowing password reset"
    )
    
    class Meta:
        db_table = 'password_reset_settings'
        verbose_name = "Password Reset Settings"
        verbose_name_plural = "Password Reset Settings"
    
    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and PasswordResetSettings.objects.exists():
            raise ValueError("Only one PasswordResetSettings instance is allowed")
        super().save(*args, **kwargs)
    
    @classmethod
    def get_settings(cls):
        """Get the current settings, creating default if none exist"""
        settings, created = cls.objects.get_or_create(
            pk=1,
            defaults={
                'token_expiry_hours': 1,
                'max_attempts_per_email_per_hour': 3,
                'max_attempts_per_ip_per_hour': 5,
                'require_email_verification': True,
            }
        )
        return settings
    
    def __str__(self):
        return "Password Reset Settings"
