/**
 * Component-Level Error Boundary
 * Provides localized error handling for specific components or sections
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle, RefreshCw, X } from 'lucide-react';
import { <PERSON>rror<PERSON>and<PERSON> } from '../../utils/errorHandling';

interface Props {
  children: ReactNode;
  componentName?: string;
  fallback?: ReactNode;
  showRetry?: boolean;
  showDismiss?: boolean;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  onRetry?: () => void;
  onDismiss?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  isDismissed: boolean;
}

class ComponentErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      isDismissed: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const componentName = this.props.componentName || 'UnknownComponent';
    
    // Log the error
    const appError = ErrorHandler.createAppError(error, componentName);
    ErrorHandler.logError(appError, componentName, {
      componentStack: errorInfo.componentStack,
      componentErrorBoundary: true
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      isDismissed: false
    });

    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  handleDismiss = () => {
    this.setState({
      isDismissed: true
    });

    if (this.props.onDismiss) {
      this.props.onDismiss();
    }
  };

  render() {
    if (this.state.hasError && !this.state.isDismissed) {
      // Custom fallback UI if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default component error UI
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 m-2" data-testid="component-error-boundary">
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-red-800 mb-1">
                Component Error
              </h3>
              <p className="text-sm text-red-700 mb-3">
                {this.props.componentName 
                  ? `The ${this.props.componentName} component encountered an error.`
                  : 'This component encountered an error.'
                }
              </p>
              
              {/* Error message in development */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="mb-3 p-2 bg-red-100 rounded text-xs">
                  <code className="text-red-800">
                    {this.state.error.message}
                  </code>
                </div>
              )}

              {/* Action buttons */}
              <div className="flex space-x-2">
                {this.props.showRetry !== false && (
                  <button
                    onClick={this.handleRetry}
                    className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-800 bg-red-100 hover:bg-red-200 rounded transition-colors"
                    data-testid="component-error-retry"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Retry
                  </button>
                )}
                
                {this.props.showDismiss && (
                  <button
                    onClick={this.handleDismiss}
                    className="inline-flex items-center px-3 py-1 text-xs font-medium text-red-800 bg-red-100 hover:bg-red-200 rounded transition-colors"
                    data-testid="component-error-dismiss"
                  >
                    <X className="w-3 h-3 mr-1" />
                    Dismiss
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ComponentErrorBoundary;

// Higher-order component for easy wrapping
export function withErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WithErrorBoundaryComponent = (props: P) => (
    <ComponentErrorBoundary {...errorBoundaryProps}>
      <WrappedComponent {...props} />
    </ComponentErrorBoundary>
  );

  WithErrorBoundaryComponent.displayName = 
    `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithErrorBoundaryComponent;
}

// Hook for error boundary context
export function useErrorBoundary() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const captureError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return {
    captureError,
    resetError
  };
}
