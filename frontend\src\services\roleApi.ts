/**
 * ROLE API SERVICE
 * Fetches roles directly from database instead of using hardcoded constants
 * Ensures frontend matches backend role data exactly
 */

import { apiRequest } from './api';

// ===== TYPES =====
export interface DatabaseRole {
  id: number;
  name: string;
  display_name: string;
  description: string;
  permission_level: string;
  requires_approval: boolean;
  is_active: boolean;
}

export interface UserRoleInfo {
  user_id: number;
  username: string;
  primary_role: string;
  all_roles: DatabaseRole[];
  is_staff: boolean;
  is_superuser: boolean;
}

export interface RoleApiResponse {
  success: boolean;
  roles: DatabaseRole[];
  total_count: number;
}

// ===== ROLE API FUNCTIONS =====

/**
 * Fetch all available roles from database
 */
export const getAvailableRoles = async (): Promise<DatabaseRole[]> => {
  try {
    const response: RoleApiResponse = await apiRequest('/users/roles/available/', 'GET');
    
    if (response.success) {
      return response.roles;
    } else {
      console.error('Failed to fetch roles:', response);
      return getDefaultRoles(); // Fallback to default roles
    }
  } catch (error) {
    console.error('Error fetching roles from database:', error);
    return getDefaultRoles(); // Fallback to default roles
  }
};

/**
 * Get current user's role information
 */
export const getCurrentUserRoles = async (): Promise<UserRoleInfo | null> => {
  try {
    const response = await apiRequest('/users/roles/current/', 'GET');
    
    if (response.success) {
      return response;
    } else {
      console.error('Failed to fetch user roles:', response);
      return null;
    }
  } catch (error) {
    console.error('Error fetching user roles:', error);
    return null;
  }
};

/**
 * Convert database roles to frontend role constants format
 */
export const convertDatabaseRolesToConstants = (dbRoles: DatabaseRole[]) => {
  const roleNames = dbRoles.map(role => role.name);
  
  return {
    ALL_ROLES: roleNames,
    BUSINESS_ROLES: roleNames.filter(role => 
      ['entrepreneur', 'mentor', 'investor'].includes(role)
    ),
    ADMIN_ROLES: roleNames.filter(role => 
      ['admin', 'super_admin'].includes(role)
    ),
    ELEVATED_ROLES: roleNames.filter(role => 
      ['admin', 'super_admin', 'moderator'].includes(role)
    ),
    BUSINESS_ADVISORS: roleNames.filter(role => 
      ['entrepreneur', 'mentor', 'investor'].includes(role)
    ),
    CONTENT_ACCESS_ROLES: roleNames.filter(role => 
      ['user', 'entrepreneur', 'mentor', 'investor'].includes(role)
    ),
    FUNDING_ACCESS_ROLES: roleNames.filter(role => 
      ['entrepreneur', 'mentor', 'investor', 'admin', 'super_admin'].includes(role)
    ),
    ANALYTICS_ACCESS_ROLES: roleNames.filter(role => 
      ['user', 'entrepreneur', 'mentor', 'investor', 'admin', 'moderator'].includes(role)
    )
  };
};

/**
 * Get role-specific AI routes based on database roles
 */
export const generateRoleAIRoutes = (dbRoles: DatabaseRole[]) => {
  const routes: Record<string, string> = {};
  
  dbRoles.forEach(role => {
    routes[role.name] = `/${role.name}/ai`;
  });
  
  return routes;
};

/**
 * Check if a role requires approval based on database data
 */
export const doesRoleRequireApproval = (roleName: string, dbRoles: DatabaseRole[]): boolean => {
  const role = dbRoles.find(r => r.name === roleName);
  return role ? role.requires_approval : true; // Default to requiring approval
};

/**
 * Get role display name from database
 */
export const getRoleDisplayName = (roleName: string, dbRoles: DatabaseRole[]): string => {
  const role = dbRoles.find(r => r.name === roleName);
  return role ? role.display_name : roleName;
};

/**
 * Get role permission level from database
 */
export const getRolePermissionLevel = (roleName: string, dbRoles: DatabaseRole[]): string => {
  const role = dbRoles.find(r => r.name === roleName);
  return role ? role.permission_level : 'read';
};

/**
 * Fallback default roles if database fetch fails
 */
const getDefaultRoles = (): DatabaseRole[] => {
  return [
    {
      id: 1,
      name: 'user',
      display_name: 'Community Member',
      description: 'Regular platform users and learners',
      permission_level: 'read',
      requires_approval: true,
      is_active: true
    },
    {
      id: 2,
      name: 'entrepreneur',
      display_name: 'Entrepreneur',
      description: 'Business founders and startup creators',
      permission_level: 'write',
      requires_approval: true,
      is_active: true
    },
    {
      id: 3,
      name: 'mentor',
      display_name: 'Mentor',
      description: 'Experienced professionals providing guidance',
      permission_level: 'write',
      requires_approval: true,
      is_active: true
    },
    {
      id: 4,
      name: 'investor',
      display_name: 'Investor',
      description: 'Investment professionals and funding sources',
      permission_level: 'write',
      requires_approval: true,
      is_active: true
    },
    {
      id: 5,
      name: 'moderator',
      display_name: 'Moderator',
      description: 'Content moderation and community management',
      permission_level: 'moderate',
      requires_approval: true,
      is_active: true
    },
    {
      id: 6,
      name: 'admin',
      display_name: 'Administrator',
      description: 'Administrative access to manage users and content',
      permission_level: 'admin',
      requires_approval: false,
      is_active: true
    },
    {
      id: 7,
      name: 'super_admin',
      display_name: 'Super Administrator',
      description: 'Full system control and management',
      permission_level: 'super_admin',
      requires_approval: false,
      is_active: true
    }
  ];
};

// ===== CACHED ROLE DATA =====
let cachedRoles: DatabaseRole[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get roles with caching to avoid repeated API calls
 */
export const getCachedRoles = async (): Promise<DatabaseRole[]> => {
  const now = Date.now();
  
  // Return cached data if still valid
  if (cachedRoles && (now - cacheTimestamp) < CACHE_DURATION) {
    return cachedRoles;
  }
  
  // Fetch fresh data
  const roles = await getAvailableRoles();
  cachedRoles = roles;
  cacheTimestamp = now;
  
  return roles;
};

/**
 * Clear role cache (useful when roles are updated)
 */
export const clearRoleCache = (): void => {
  cachedRoles = null;
  cacheTimestamp = 0;
};

// ===== EXPORT MAIN API =====
export const roleApi = {
  getAvailableRoles,
  getCurrentUserRoles,
  getCachedRoles,
  clearRoleCache,
  convertDatabaseRolesToConstants,
  generateRoleAIRoutes,
  doesRoleRequireApproval,
  getRoleDisplayName,
  getRolePermissionLevel
};

export default roleApi;
