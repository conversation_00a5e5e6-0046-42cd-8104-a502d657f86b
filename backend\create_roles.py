#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create all required user roles in the database
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from users.models import User<PERSON>ole

def create_roles():
    """Create all required user roles"""
    roles_data = [
        {
            'name': 'super_admin',
            'display_name': 'Super Administrator',
            'description': 'Full system control and management',
            'permission_level': 'super_admin',
            'requires_approval': False
        },
        {
            'name': 'admin',
            'display_name': 'Administrator',
            'description': 'Administrative access to manage users and content',
            'permission_level': 'admin',
            'requires_approval': False
        },
        {
            'name': 'moderator',
            'display_name': 'Moderator',
            'description': 'Content moderation and community management',
            'permission_level': 'moderate',
            'requires_approval': True
        },
        {
            'name': 'entrepreneur',
            'display_name': 'Entrepreneur',
            'description': 'Business founders and startup creators',
            'permission_level': 'write',
            'requires_approval': True
        },
        {
            'name': 'mentor',
            'display_name': 'Mentor',
            'description': 'Experienced professionals providing guidance',
            'permission_level': 'write',
            'requires_approval': True
        },
        {
            'name': 'investor',
            'display_name': 'Investor',
            'description': 'Investment professionals and funding sources',
            'permission_level': 'write',
            'requires_approval': True
        },
        {
            'name': 'user',
            'display_name': 'Community Member',
            'description': 'Regular platform users and learners',
            'permission_level': 'read',
            'requires_approval': True
        }
    ]
    
    created_count = 0
    updated_count = 0
    
    for role_data in roles_data:
        role, created = UserRole.objects.get_or_create(
            name=role_data['name'],
            defaults=role_data
        )
        
        if created:
            created_count += 1
            print(f"[+] Created role: {role.display_name}")
        else:
            # Update existing role with new data
            for key, value in role_data.items():
                if key != 'name':  # Don't update the name field
                    setattr(role, key, value)
            role.save()
            updated_count += 1
            print(f"[*] Updated role: {role.display_name}")

    print(f"\nSummary:")
    print(f"   Created: {created_count} roles")
    print(f"   Updated: {updated_count} roles")
    print(f"   Total roles in database: {UserRole.objects.count()}")

    print(f"\nAll roles:")
    for role in UserRole.objects.all().order_by('name'):
        approval_status = "Requires Approval" if role.requires_approval else "Auto-Approved"
        print(f"   - {role.display_name} ({role.name}) - {role.permission_level} - {approval_status}")

if __name__ == '__main__':
    print("Creating/updating user roles...")
    create_roles()
    print("Done!")
