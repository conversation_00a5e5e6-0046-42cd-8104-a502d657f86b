@echo off
echo 🚀 Starting Django Backend Server...
echo.

REM Get the directory where this batch file is located
set "PROJECT_DIR=%~dp0"
echo Project directory: %PROJECT_DIR%

REM Navigate to backend directory
cd /d "%PROJECT_DIR%backend"
echo Current directory: %CD%

REM Check if manage.py exists
if exist "manage.py" (
    echo ✅ Found manage.py
    echo Starting Django development server on port 8000...
    echo.
    python manage.py runserver 8000
) else (
    echo ❌ manage.py not found in backend directory
    echo Please make sure you're running this from the project root directory
    dir
)

echo.
echo Press any key to exit...
pause >nul
