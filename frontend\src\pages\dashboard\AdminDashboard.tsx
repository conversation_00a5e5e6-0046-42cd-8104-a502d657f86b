/**
 * Admin Dashboard
 * Comprehensive admin dashboard with platform management and analytics
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicTable } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Users, 
  Briefcase, 
  DollarSign, 
  TrendingUp, 
  AlertCircle,
  CheckCircle,
  Clock,
  Settings,
  BarChart3,
  PieChart,
  Activity,
  Shield,
  Database,
  Bell
} from 'lucide-react';

interface AdminStats {
  totalUsers: number;
  activeStartups: number;
  totalFunding: number;
  successRate: number;
  pendingApplications: number;
  activeMentorships: number;
  systemHealth: number;
  monthlyGrowth: number;
}

interface RecentActivity {
  id: string;
  type: 'application' | 'funding' | 'mentorship' | 'system';
  description: string;
  descriptionAr: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error' | 'info';
  user?: string;
}

interface SystemAlert {
  id: string;
  type: 'security' | 'performance' | 'maintenance' | 'user';
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  resolved: boolean;
}

const AdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockStats: AdminStats = {
        totalUsers: 2847,
        activeStartups: 156,
        totalFunding: 45600000,
        successRate: 73,
        pendingApplications: 23,
        activeMentorships: 89,
        systemHealth: 98,
        monthlyGrowth: 15
      };

      const mockActivity: RecentActivity[] = [
        {
          id: '1',
          type: 'application',
          description: 'New startup application submitted',
          descriptionAr: 'تم تقديم طلب شركة ناشئة جديدة',
          timestamp: '2024-01-22T10:30:00Z',
          status: 'info',
          user: 'Ahmed Al-Rashid'
        },
        {
          id: '2',
          type: 'funding',
          description: 'Funding round completed - $500K',
          descriptionAr: 'تم إكمال جولة تمويل - 500 ألف دولار',
          timestamp: '2024-01-22T09:15:00Z',
          status: 'success',
          user: 'FinTech Solutions'
        },
        {
          id: '3',
          type: 'mentorship',
          description: 'New mentorship match created',
          descriptionAr: 'تم إنشاء مطابقة إرشاد جديدة',
          timestamp: '2024-01-22T08:45:00Z',
          status: 'success'
        },
        {
          id: '4',
          type: 'system',
          description: 'System backup completed successfully',
          descriptionAr: 'تم إكمال النسخ الاحتياطي للنظام بنجاح',
          timestamp: '2024-01-22T02:00:00Z',
          status: 'success'
        }
      ];

      const mockAlerts: SystemAlert[] = [
        {
          id: '1',
          type: 'performance',
          title: 'High Database Load',
          titleAr: 'حمولة عالية على قاعدة البيانات',
          description: 'Database response time increased by 15%',
          descriptionAr: 'زاد وقت استجابة قاعدة البيانات بنسبة 15%',
          severity: 'medium',
          timestamp: '2024-01-22T11:00:00Z',
          resolved: false
        },
        {
          id: '2',
          type: 'security',
          title: 'Multiple Failed Login Attempts',
          titleAr: 'محاولات تسجيل دخول فاشلة متعددة',
          description: 'Detected 5 failed login attempts from IP *************',
          descriptionAr: 'تم اكتشاف 5 محاولات تسجيل دخول فاشلة من IP *************',
          severity: 'high',
          timestamp: '2024-01-22T10:45:00Z',
          resolved: false
        }
      ];

      setStats(mockStats);
      setRecentActivity(mockActivity);
      setSystemAlerts(mockAlerts);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number): string => {
    if (language === 'ar') {
      return `${(amount / 1000000).toFixed(1)} مليون ريال`;
    }
    return `$${(amount / 1000000).toFixed(1)}M`;
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'application':
        return <Briefcase className="w-4 h-4" />;
      case 'funding':
        return <DollarSign className="w-4 h-4" />;
      case 'mentorship':
        return <Users className="w-4 h-4" />;
      case 'system':
        return <Settings className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  const getActivityColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-100';
      case 'warning':
        return 'text-yellow-600 bg-yellow-100';
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-blue-600 bg-blue-100';
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'border-red-500 bg-red-50';
      case 'high':
        return 'border-orange-500 bg-orange-50';
      case 'medium':
        return 'border-yellow-500 bg-yellow-50';
      default:
        return 'border-blue-500 bg-blue-50';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'security':
        return <Shield className="w-5 h-5" />;
      case 'performance':
        return <BarChart3 className="w-5 h-5" />;
      case 'maintenance':
        return <Settings className="w-5 h-5" />;
      default:
        return <AlertCircle className="w-5 h-5" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'} data-testid="admin-dashboard">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div>
              <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                {language === 'ar' ? `مرحباً، ${user?.name || 'المدير'}` : `Welcome, ${user?.name || 'Admin'}`}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' 
                  ? 'لوحة تحكم المدير - إدارة المنصة والمراقبة'
                  : 'Admin Dashboard - Platform management and monitoring'
                }
              </ArabicTypography>
            </div>
            <div className={`flex space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <ArabicButton
                variant="outline"
                onClick={() => navigate('/admin/settings')}
                icon={<Settings className="w-4 h-4" />}
              >
                {language === 'ar' ? 'الإعدادات' : 'Settings'}
              </ArabicButton>
              <ArabicButton
                onClick={() => navigate('/admin/reports')}
                icon={<BarChart3 className="w-4 h-4" />}
              >
                {language === 'ar' ? 'التقارير' : 'Reports'}
              </ArabicButton>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.totalUsers.toLocaleString()}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'إجمالي المستخدمين' : 'Total Users'}
              </ArabicTypography>
              <div className="mt-2 text-sm text-green-600 font-arabic">
                +{stats.monthlyGrowth}% {language === 'ar' ? 'هذا الشهر' : 'this month'}
              </div>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4">
                <Briefcase className="w-6 h-6 text-green-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.activeStartups}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'الشركات الناشئة النشطة' : 'Active Startups'}
              </ArabicTypography>
              <div className="mt-2 text-sm text-blue-600 font-arabic">
                {stats.pendingApplications} {language === 'ar' ? 'في الانتظار' : 'pending'}
              </div>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {formatCurrency(stats.totalFunding)}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'إجمالي التمويل' : 'Total Funding'}
              </ArabicTypography>
              <div className="mt-2 text-sm text-green-600 font-arabic">
                {stats.successRate}% {language === 'ar' ? 'معدل النجاح' : 'success rate'}
              </div>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mx-auto mb-4">
                <Activity className="w-6 h-6 text-orange-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.systemHealth}%
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'صحة النظام' : 'System Health'}
              </ArabicTypography>
              <div className="mt-2 text-sm text-green-600 font-arabic">
                {language === 'ar' ? 'ممتاز' : 'Excellent'}
              </div>
            </ArabicCard>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* System Alerts */}
            <ArabicCard 
              title={language === 'ar' ? 'تنبيهات النظام' : 'System Alerts'}
              headerAction={
                <ArabicButton
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/admin/alerts')}
                >
                  {language === 'ar' ? 'عرض الكل' : 'View All'}
                </ArabicButton>
              }
            >
              <div className="space-y-4">
                {systemAlerts.map((alert) => (
                  <div key={alert.id} className={`border rounded-lg p-4 ${getAlertColor(alert.severity)}`}>
                    <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`flex-shrink-0 ${isRTL ? 'ml-3' : 'mr-3'}`}>
                        {getAlertIcon(alert.type)}
                      </div>
                      <div className="flex-1">
                        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <ArabicTypography variant="body1" className="font-semibold">
                            {language === 'ar' ? alert.titleAr : alert.title}
                          </ArabicTypography>
                          <span className={`px-2 py-1 rounded text-xs font-semibold ${
                            alert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                            alert.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                            alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          } font-arabic`}>
                            {alert.severity === 'critical' ? (language === 'ar' ? 'حرج' : 'Critical') :
                             alert.severity === 'high' ? (language === 'ar' ? 'عالي' : 'High') :
                             alert.severity === 'medium' ? (language === 'ar' ? 'متوسط' : 'Medium') :
                             (language === 'ar' ? 'منخفض' : 'Low')}
                          </span>
                        </div>
                        <ArabicTypography variant="body2" color="secondary" className="mt-1">
                          {language === 'ar' ? alert.descriptionAr : alert.description}
                        </ArabicTypography>
                        <div className={`flex items-center justify-between mt-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <ArabicTypography variant="caption" color="secondary">
                            {new Date(alert.timestamp).toLocaleString()}
                          </ArabicTypography>
                          <ArabicButton size="sm" variant="outline">
                            {language === 'ar' ? 'حل' : 'Resolve'}
                          </ArabicButton>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ArabicCard>

            {/* Recent Activity */}
            <ArabicCard title={language === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}>
              <div className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${getActivityColor(activity.status)} ${isRTL ? 'ml-3' : 'mr-3'}`}>
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1">
                      <ArabicTypography variant="body2" className="text-gray-900">
                        {language === 'ar' ? activity.descriptionAr : activity.description}
                        {activity.user && (
                          <span className="font-semibold"> - {activity.user}</span>
                        )}
                      </ArabicTypography>
                      <ArabicTypography variant="caption" color="secondary">
                        {new Date(activity.timestamp).toLocaleString()}
                      </ArabicTypography>
                    </div>
                  </div>
                ))}
              </div>
            </ArabicCard>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <ArabicCard title={language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}>
              <div className="space-y-3">
                <ArabicButton
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate('/admin/users')}
                  icon={<Users className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'إدارة المستخدمين' : 'Manage Users'}
                </ArabicButton>
                
                <ArabicButton
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate('/admin/applications')}
                  icon={<Briefcase className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'مراجعة الطلبات' : 'Review Applications'}
                </ArabicButton>
                
                <ArabicButton
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate('/admin/analytics')}
                  icon={<BarChart3 className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'التحليلات' : 'Analytics'}
                </ArabicButton>
                
                <ArabicButton
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate('/admin/system')}
                  icon={<Database className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'إدارة النظام' : 'System Management'}
                </ArabicButton>
              </div>
            </ArabicCard>

            {/* Platform Statistics */}
            <ArabicCard title={language === 'ar' ? 'إحصائيات المنصة' : 'Platform Statistics'}>
              <div className="space-y-4">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <ArabicTypography variant="body2" color="secondary">
                    {language === 'ar' ? 'المستخدمون النشطون اليوم' : 'Active Users Today'}
                  </ArabicTypography>
                  <ArabicTypography variant="body2" className="font-semibold">
                    1,247
                  </ArabicTypography>
                </div>
                
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <ArabicTypography variant="body2" color="secondary">
                    {language === 'ar' ? 'الجلسات الجديدة' : 'New Sessions'}
                  </ArabicTypography>
                  <ArabicTypography variant="body2" className="font-semibold">
                    89
                  </ArabicTypography>
                </div>
                
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <ArabicTypography variant="body2" color="secondary">
                    {language === 'ar' ? 'معدل الارتداد' : 'Bounce Rate'}
                  </ArabicTypography>
                  <ArabicTypography variant="body2" className="font-semibold">
                    23%
                  </ArabicTypography>
                </div>
                
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <ArabicTypography variant="body2" color="secondary">
                    {language === 'ar' ? 'متوسط وقت الجلسة' : 'Avg Session Duration'}
                  </ArabicTypography>
                  <ArabicTypography variant="body2" className="font-semibold">
                    12m 34s
                  </ArabicTypography>
                </div>
              </div>
            </ArabicCard>

            {/* System Status */}
            <ArabicCard title={language === 'ar' ? 'حالة النظام' : 'System Status'}>
              <div className="space-y-4">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <ArabicTypography variant="body2">
                      {language === 'ar' ? 'خادم الويب' : 'Web Server'}
                    </ArabicTypography>
                  </div>
                  <span className="text-green-600 text-sm font-arabic">
                    {language === 'ar' ? 'يعمل' : 'Online'}
                  </span>
                </div>
                
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <ArabicTypography variant="body2">
                      {language === 'ar' ? 'قاعدة البيانات' : 'Database'}
                    </ArabicTypography>
                  </div>
                  <span className="text-green-600 text-sm font-arabic">
                    {language === 'ar' ? 'يعمل' : 'Online'}
                  </span>
                </div>
                
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Clock className={`w-4 h-4 text-yellow-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <ArabicTypography variant="body2">
                      {language === 'ar' ? 'النسخ الاحتياطي' : 'Backup Service'}
                    </ArabicTypography>
                  </div>
                  <span className="text-yellow-600 text-sm font-arabic">
                    {language === 'ar' ? 'قيد التشغيل' : 'Running'}
                  </span>
                </div>
                
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <ArabicTypography variant="body2">
                      {language === 'ar' ? 'خدمة الذكاء الاصطناعي' : 'AI Service'}
                    </ArabicTypography>
                  </div>
                  <span className="text-green-600 text-sm font-arabic">
                    {language === 'ar' ? 'يعمل' : 'Online'}
                  </span>
                </div>
              </div>
            </ArabicCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
