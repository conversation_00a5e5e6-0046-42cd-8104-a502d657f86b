/**
 * Business Plan Review Page
 * Professional review and feedback system for business plans
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicProgress } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Star, 
  CheckCircle, 
  AlertCircle, 
  MessageSquare, 
  FileText, 
  TrendingUp,
  Users,
  DollarSign,
  Target,
  Award,
  Clock,
  ArrowLeft,
  Send,
  Download,
  Eye,
  Edit
} from 'lucide-react';

interface ReviewCriteria {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  weight: number;
  score: number;
  maxScore: number;
  feedback: string;
  suggestions: string[];
  suggestionsAr: string[];
}

interface BusinessPlanReview {
  id: string;
  planId: string;
  planTitle: string;
  reviewerId: string;
  reviewerName: string;
  reviewerTitle: string;
  reviewerAvatar?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'approved' | 'needs_revision';
  overallScore: number;
  maxScore: number;
  criteria: ReviewCriteria[];
  generalFeedback: string;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  createdAt: string;
  completedAt?: string;
  estimatedCompletionTime: string;
}

const BusinessPlanReviewPage: React.FC = () => {
  const { reviewId } = useParams();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [review, setReview] = useState<BusinessPlanReview | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeSection, setActiveSection] = useState('overview');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadReview();
  }, [reviewId]);

  const loadReview = async () => {
    try {
      // Mock data - replace with actual API call
      const mockReview: BusinessPlanReview = {
        id: reviewId || '1',
        planId: 'plan-123',
        planTitle: 'FinTech Revolution - Digital Banking Platform',
        reviewerId: 'reviewer-1',
        reviewerName: 'Dr. Omar Hassan',
        reviewerTitle: 'Senior Business Consultant & Former VC Partner',
        status: 'completed',
        overallScore: 82,
        maxScore: 100,
        criteria: [
          {
            id: 'executive_summary',
            name: 'Executive Summary',
            nameAr: 'الملخص التنفيذي',
            description: 'Clarity, completeness, and compelling nature of the executive summary',
            descriptionAr: 'وضوح واكتمال وجاذبية الملخص التنفيذي',
            weight: 15,
            score: 13,
            maxScore: 15,
            feedback: 'Strong executive summary with clear value proposition. Consider adding more specific market size data.',
            suggestions: [
              'Add specific TAM/SAM/SOM figures',
              'Include key financial projections',
              'Strengthen competitive differentiation'
            ],
            suggestionsAr: [
              'أضف أرقام TAM/SAM/SOM محددة',
              'أدرج التوقعات المالية الرئيسية',
              'عزز التمايز التنافسي'
            ]
          },
          {
            id: 'market_analysis',
            name: 'Market Analysis',
            nameAr: 'تحليل السوق',
            description: 'Depth of market research, target audience definition, and competitive analysis',
            descriptionAr: 'عمق بحث السوق وتعريف الجمهور المستهدف والتحليل التنافسي',
            weight: 20,
            score: 16,
            maxScore: 20,
            feedback: 'Comprehensive market analysis with good regional focus. Competitive analysis could be more detailed.',
            suggestions: [
              'Add more competitor financial data',
              'Include market trend analysis',
              'Strengthen regulatory landscape section'
            ],
            suggestionsAr: [
              'أضف المزيد من البيانات المالية للمنافسين',
              'أدرج تحليل اتجاهات السوق',
              'عزز قسم المشهد التنظيمي'
            ]
          },
          {
            id: 'business_model',
            name: 'Business Model',
            nameAr: 'نموذج العمل',
            description: 'Viability and scalability of the business model',
            descriptionAr: 'جدوى وقابلية توسع نموذج العمل',
            weight: 20,
            score: 17,
            maxScore: 20,
            feedback: 'Solid business model with multiple revenue streams. Consider subscription model variations.',
            suggestions: [
              'Explore freemium model options',
              'Add B2B revenue streams',
              'Consider partnership monetization'
            ],
            suggestionsAr: [
              'استكشف خيارات النموذج المجاني المتدرج',
              'أضف مصادر إيرادات B2B',
              'فكر في تحقيق الدخل من الشراكات'
            ]
          },
          {
            id: 'financial_projections',
            name: 'Financial Projections',
            nameAr: 'التوقعات المالية',
            description: 'Realism and detail of financial forecasts',
            descriptionAr: 'واقعية وتفصيل التوقعات المالية',
            weight: 25,
            score: 20,
            maxScore: 25,
            feedback: 'Well-structured financial projections with conservative assumptions. Good scenario planning.',
            suggestions: [
              'Add sensitivity analysis',
              'Include unit economics breakdown',
              'Strengthen cash flow projections'
            ],
            suggestionsAr: [
              'أضف تحليل الحساسية',
              'أدرج تفصيل اقتصاديات الوحدة',
              'عزز توقعات التدفق النقدي'
            ]
          },
          {
            id: 'team',
            name: 'Team & Management',
            nameAr: 'الفريق والإدارة',
            description: 'Team experience, skills, and organizational structure',
            descriptionAr: 'خبرة الفريق ومهاراته والهيكل التنظيمي',
            weight: 20,
            score: 16,
            maxScore: 20,
            feedback: 'Strong founding team with relevant experience. Consider adding technical co-founder.',
            suggestions: [
              'Add CTO or technical co-founder',
              'Include advisory board details',
              'Strengthen team bios'
            ],
            suggestionsAr: [
              'أضف مدير تقني أو شريك مؤسس تقني',
              'أدرج تفاصيل المجلس الاستشاري',
              'عزز السير الذاتية للفريق'
            ]
          }
        ],
        generalFeedback: 'This is a well-structured business plan with a compelling value proposition for the MENA fintech market. The team has demonstrated strong market understanding and has developed a viable business model. The financial projections are realistic and well-supported. Key areas for improvement include strengthening the competitive analysis and adding more technical depth to the product development roadmap.',
        strengths: [
          'Clear value proposition for underbanked population',
          'Strong understanding of MENA market dynamics',
          'Realistic financial projections with conservative assumptions',
          'Experienced founding team with relevant background',
          'Comprehensive go-to-market strategy'
        ],
        weaknesses: [
          'Limited competitive analysis depth',
          'Lack of technical co-founder',
          'Insufficient product development timeline',
          'Missing regulatory compliance details',
          'Limited customer acquisition cost analysis'
        ],
        recommendations: [
          'Conduct deeper competitive analysis with financial benchmarks',
          'Recruit a technical co-founder or CTO',
          'Develop detailed product roadmap with technical milestones',
          'Add comprehensive regulatory compliance section',
          'Include detailed customer acquisition and retention strategies',
          'Consider strategic partnerships with existing financial institutions'
        ],
        createdAt: '2024-01-20T09:00:00Z',
        completedAt: '2024-01-22T16:30:00Z',
        estimatedCompletionTime: '3-5 business days'
      };

      setReview(mockReview);
    } catch (error) {
      console.error('Error loading review:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    setSubmitting(true);
    try {
      // Approve business plan - replace with actual API call
      console.log('Approving business plan');
      
      if (review) {
        setReview({
          ...review,
          status: 'approved'
        });
      }
    } catch (error) {
      console.error('Error approving plan:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleRequestRevision = async () => {
    setSubmitting(true);
    try {
      // Request revision - replace with actual API call
      console.log('Requesting revision');
      
      if (review) {
        setReview({
          ...review,
          status: 'needs_revision'
        });
      }
    } catch (error) {
      console.error('Error requesting revision:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'needs_revision':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    const statuses = {
      en: {
        pending: 'Pending',
        in_progress: 'In Progress',
        completed: 'Completed',
        approved: 'Approved',
        needs_revision: 'Needs Revision'
      },
      ar: {
        pending: 'في الانتظار',
        in_progress: 'قيد المراجعة',
        completed: 'مكتمل',
        approved: 'معتمد',
        needs_revision: 'يحتاج مراجعة'
      }
    };
    return statuses[language][status as keyof typeof statuses.en] || status;
  };

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (!review) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
            {language === 'ar' ? 'لم يتم العثور على المراجعة' : 'Review Not Found'}
          </ArabicTypography>
          <ArabicButton onClick={() => navigate('/dashboard/business-plan')}>
            {language === 'ar' ? 'العودة لخطط العمل' : 'Back to Business Plans'}
          </ArabicButton>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <ArabicButton
                variant="ghost"
                onClick={() => navigate(-1)}
                icon={<ArrowLeft className="w-4 h-4" />}
                className={isRTL ? 'ml-4' : 'mr-4'}
              >
                {language === 'ar' ? 'رجوع' : 'Back'}
              </ArabicButton>
              <div>
                <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                  {language === 'ar' ? 'مراجعة خطة العمل' : 'Business Plan Review'}
                </ArabicTypography>
                <ArabicTypography variant="body1" color="secondary">
                  {review.planTitle}
                </ArabicTypography>
              </div>
            </div>
            
            <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(review.status)} font-arabic`}>
                {getStatusText(review.status)}
              </span>
              
              <ArabicButton
                variant="outline"
                onClick={() => navigate(`/dashboard/business-plan/view/${review.planId}`)}
                icon={<Eye className="w-4 h-4" />}
              >
                {language === 'ar' ? 'عرض الخطة' : 'View Plan'}
              </ArabicButton>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Overall Score */}
            <ArabicCard>
              <div className="text-center mb-6">
                <div className={`text-6xl font-bold mb-2 ${getScoreColor(review.overallScore, review.maxScore)}`}>
                  {review.overallScore}
                </div>
                <ArabicTypography variant="h4" className="text-gray-600">
                  {language === 'ar' ? 'من' : 'out of'} {review.maxScore}
                </ArabicTypography>
                <ArabicProgress
                  value={(review.overallScore / review.maxScore) * 100}
                  size="lg"
                  color="blue"
                  className="mt-4"
                />
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {review.criteria.filter(c => (c.score / c.maxScore) >= 0.8).length}
                  </div>
                  <ArabicTypography variant="caption" color="secondary">
                    {language === 'ar' ? 'نقاط قوة' : 'Strengths'}
                  </ArabicTypography>
                </div>
                <div>
                  <div className="text-2xl font-bold text-yellow-600">
                    {review.criteria.filter(c => (c.score / c.maxScore) >= 0.6 && (c.score / c.maxScore) < 0.8).length}
                  </div>
                  <ArabicTypography variant="caption" color="secondary">
                    {language === 'ar' ? 'تحسينات' : 'Improvements'}
                  </ArabicTypography>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">
                    {review.criteria.filter(c => (c.score / c.maxScore) < 0.6).length}
                  </div>
                  <ArabicTypography variant="caption" color="secondary">
                    {language === 'ar' ? 'نقاط ضعف' : 'Weaknesses'}
                  </ArabicTypography>
                </div>
              </div>
            </ArabicCard>

            {/* Detailed Criteria */}
            <ArabicCard title={language === 'ar' ? 'تقييم مفصل' : 'Detailed Assessment'}>
              <div className="space-y-6">
                {review.criteria.map((criterion) => (
                  <div key={criterion.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                    <div className={`flex items-center justify-between mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <ArabicTypography variant="h5" className="font-semibold">
                        {language === 'ar' ? criterion.nameAr : criterion.name}
                      </ArabicTypography>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={`text-lg font-bold ${getScoreColor(criterion.score, criterion.maxScore)} ${isRTL ? 'ml-2' : 'mr-2'}`}>
                          {criterion.score}/{criterion.maxScore}
                        </span>
                        <div className="flex">
                          {Array.from({ length: 5 }, (_, i) => (
                            <Star
                              key={i}
                              className={`w-4 h-4 ${
                                i < (criterion.score / criterion.maxScore) * 5
                                  ? 'text-yellow-500 fill-current'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <ArabicTypography variant="body2" color="secondary" className="mb-3">
                      {language === 'ar' ? criterion.descriptionAr : criterion.description}
                    </ArabicTypography>
                    
                    <ArabicProgress
                      value={(criterion.score / criterion.maxScore) * 100}
                      size="sm"
                      color="blue"
                      className="mb-3"
                    />
                    
                    <ArabicTypography variant="body2" className="mb-3">
                      {criterion.feedback}
                    </ArabicTypography>
                    
                    {criterion.suggestions.length > 0 && (
                      <div>
                        <ArabicTypography variant="body2" className="font-semibold mb-2">
                          {language === 'ar' ? 'اقتراحات للتحسين:' : 'Suggestions for improvement:'}
                        </ArabicTypography>
                        <ul className="space-y-1">
                          {(language === 'ar' ? criterion.suggestionsAr : criterion.suggestions).map((suggestion, index) => (
                            <li key={index} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <div className={`w-2 h-2 bg-blue-500 rounded-full mt-2 ${isRTL ? 'ml-2' : 'mr-2'}`}></div>
                              <ArabicTypography variant="body2" color="secondary">
                                {suggestion}
                              </ArabicTypography>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ArabicCard>

            {/* General Feedback */}
            <ArabicCard title={language === 'ar' ? 'التقييم العام' : 'General Feedback'}>
              <ArabicTypography variant="body1" className="leading-relaxed">
                {review.generalFeedback}
              </ArabicTypography>
            </ArabicCard>

            {/* Strengths and Weaknesses */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ArabicCard title={language === 'ar' ? 'نقاط القوة' : 'Strengths'}>
                <div className="space-y-2">
                  {review.strengths.map((strength, index) => (
                    <div key={index} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <CheckCircle className={`w-4 h-4 text-green-500 mt-0.5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <ArabicTypography variant="body2">
                        {strength}
                      </ArabicTypography>
                    </div>
                  ))}
                </div>
              </ArabicCard>

              <ArabicCard title={language === 'ar' ? 'نقاط الضعف' : 'Areas for Improvement'}>
                <div className="space-y-2">
                  {review.weaknesses.map((weakness, index) => (
                    <div key={index} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <AlertCircle className={`w-4 h-4 text-yellow-500 mt-0.5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <ArabicTypography variant="body2">
                        {weakness}
                      </ArabicTypography>
                    </div>
                  ))}
                </div>
              </ArabicCard>
            </div>

            {/* Recommendations */}
            <ArabicCard title={language === 'ar' ? 'التوصيات' : 'Recommendations'}>
              <div className="space-y-3">
                {review.recommendations.map((recommendation, index) => (
                  <div key={index} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'} mt-0.5`}>
                      <span className="text-xs font-bold text-blue-600">{index + 1}</span>
                    </div>
                    <ArabicTypography variant="body2">
                      {recommendation}
                    </ArabicTypography>
                  </div>
                ))}
              </div>
            </ArabicCard>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Reviewer Info */}
            <ArabicCard title={language === 'ar' ? 'المراجع' : 'Reviewer'}>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl font-bold text-blue-600">
                    {review.reviewerName.charAt(0)}
                  </span>
                </div>
                <ArabicTypography variant="h5" className="font-semibold mb-1">
                  {review.reviewerName}
                </ArabicTypography>
                <ArabicTypography variant="body2" color="secondary" className="mb-4">
                  {review.reviewerTitle}
                </ArabicTypography>
                
                <div className="space-y-2 text-sm">
                  <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-gray-600 font-arabic">
                      {language === 'ar' ? 'تاريخ البدء:' : 'Started:'}
                    </span>
                    <span className="font-arabic">
                      {new Date(review.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  
                  {review.completedAt && (
                    <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className="text-gray-600 font-arabic">
                        {language === 'ar' ? 'تاريخ الإكمال:' : 'Completed:'}
                      </span>
                      <span className="font-arabic">
                        {new Date(review.completedAt).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                  
                  <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-gray-600 font-arabic">
                      {language === 'ar' ? 'الوقت المقدر:' : 'Est. Time:'}
                    </span>
                    <span className="font-arabic">
                      {review.estimatedCompletionTime}
                    </span>
                  </div>
                </div>
              </div>
            </ArabicCard>

            {/* Actions */}
            {review.status === 'completed' && (
              <ArabicCard title={language === 'ar' ? 'الإجراءات' : 'Actions'}>
                <div className="space-y-3">
                  <ArabicButton
                    className="w-full"
                    onClick={handleApprove}
                    disabled={submitting}
                    icon={<CheckCircle className="w-4 h-4" />}
                  >
                    {language === 'ar' ? 'اعتماد الخطة' : 'Approve Plan'}
                  </ArabicButton>
                  
                  <ArabicButton
                    variant="outline"
                    className="w-full"
                    onClick={handleRequestRevision}
                    disabled={submitting}
                    icon={<Edit className="w-4 h-4" />}
                  >
                    {language === 'ar' ? 'طلب مراجعة' : 'Request Revision'}
                  </ArabicButton>
                  
                  <ArabicButton
                    variant="outline"
                    className="w-full"
                    icon={<Download className="w-4 h-4" />}
                  >
                    {language === 'ar' ? 'تحميل التقرير' : 'Download Report'}
                  </ArabicButton>
                  
                  <ArabicButton
                    variant="outline"
                    className="w-full"
                    icon={<MessageSquare className="w-4 h-4" />}
                  >
                    {language === 'ar' ? 'إرسال رسالة' : 'Send Message'}
                  </ArabicButton>
                </div>
              </ArabicCard>
            )}

            {/* Review Timeline */}
            <ArabicCard title={language === 'ar' ? 'الجدول الزمني' : 'Review Timeline'}>
              <div className="space-y-4">
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Clock className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className={isRTL ? 'mr-3' : 'ml-3'}>
                    <ArabicTypography variant="body2" className="font-semibold">
                      {language === 'ar' ? 'بدء المراجعة' : 'Review Started'}
                    </ArabicTypography>
                    <ArabicTypography variant="caption" color="secondary">
                      {new Date(review.createdAt).toLocaleString()}
                    </ArabicTypography>
                  </div>
                </div>
                
                {review.completedAt && (
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    </div>
                    <div className={isRTL ? 'mr-3' : 'ml-3'}>
                      <ArabicTypography variant="body2" className="font-semibold">
                        {language === 'ar' ? 'اكتمال المراجعة' : 'Review Completed'}
                      </ArabicTypography>
                      <ArabicTypography variant="caption" color="secondary">
                        {new Date(review.completedAt).toLocaleString()}
                      </ArabicTypography>
                    </div>
                  </div>
                )}
              </div>
            </ArabicCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanReviewPage;
