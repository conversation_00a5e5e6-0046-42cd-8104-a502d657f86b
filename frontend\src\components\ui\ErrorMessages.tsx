import React from 'react';
import { AlertCircle, X, CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface AlertProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary';
  }>;
}

interface ToastProps extends Omit<AlertProps, 'dismissible'> {
  duration?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

interface FormErrorProps {
  errors: Record<string, string | string[]>;
  className?: string;
}

interface InlineErrorProps {
  error?: string | string[];
  className?: string;
}

/**
 * Alert Component
 */
export const Alert: React.FC<AlertProps> = ({
  type,
  title,
  message,
  dismissible = false,
  onDismiss,
  className = '',
  actions = []
}) => {
  const { t } = useTranslation();

  const typeStyles = {
    success: {
      container: 'bg-green-50 border-green-200 text-green-800',
      icon: <CheckCircle className="w-5 h-5 text-green-400" />,
      title: 'text-green-800',
      message: 'text-green-700'
    },
    error: {
      container: 'bg-red-50 border-red-200 text-red-800',
      icon: <AlertCircle className="w-5 h-5 text-red-400" />,
      title: 'text-red-800',
      message: 'text-red-700'
    },
    warning: {
      container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      icon: <AlertTriangle className="w-5 h-5 text-yellow-400" />,
      title: 'text-yellow-800',
      message: 'text-yellow-700'
    },
    info: {
      container: 'bg-blue-50 border-blue-200 text-blue-800',
      icon: <Info className="w-5 h-5 text-blue-400" />,
      title: 'text-blue-800',
      message: 'text-blue-700'
    }
  };

  const styles = typeStyles[type];

  return (
    <div className={`border rounded-md p-4 ${styles.container} ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          {styles.icon}
        </div>
        <div className="ml-3 flex-1">
          {title && (
            <h3 className={`text-sm font-medium ${styles.title}`}>
              {title}
            </h3>
          )}
          <div className={`${title ? 'mt-2' : ''} text-sm ${styles.message}`}>
            <p>{message}</p>
          </div>
          {actions.length > 0 && (
            <div className="mt-4 flex space-x-3">
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.onClick}
                  className={`text-sm font-medium ${
                    action.variant === 'primary'
                      ? `${styles.title} hover:opacity-75`
                      : `${styles.message} hover:opacity-75`
                  }`}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>
        {dismissible && onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                onClick={onDismiss}
                className={`inline-flex rounded-md p-1.5 ${styles.message} hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600`}
              >
                <span className="sr-only">{t('common.dismiss', 'Dismiss')}</span>
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Toast Notification Component
 */
export const Toast: React.FC<ToastProps> = ({
  type,
  title,
  message,
  onDismiss,
  duration = 5000,
  position = 'top-right',
  className = '',
  actions = []
}) => {
  const [isVisible, setIsVisible] = React.useState(true);

  React.useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => onDismiss?.(), 300); // Allow fade out animation
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, onDismiss]);

  const positionStyles = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
  };

  if (!isVisible) return null;

  return (
    <div className={`fixed z-50 max-w-sm w-full ${positionStyles[position]} transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
      <Alert
        type={type}
        title={title}
        message={message}
        dismissible
        onDismiss={() => {
          setIsVisible(false);
          setTimeout(() => onDismiss?.(), 300);
        }}
        actions={actions}
        className={`shadow-lg ${className}`}
      />
    </div>
  );
};

/**
 * Form Error Display Component
 */
export const FormErrors: React.FC<FormErrorProps> = ({ errors, className = '' }) => {
  const { t } = useTranslation();

  const errorEntries = Object.entries(errors).filter(([_, value]) => value);

  if (errorEntries.length === 0) return null;

  return (
    <div className={`bg-red-50 border border-red-200 rounded-md p-4 ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <AlertCircle className="w-5 h-5 text-red-400" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            {t('errors.form.title', 'Please correct the following errors:')}
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <ul className="list-disc pl-5 space-y-1">
              {errorEntries.map(([field, error]) => {
                const errorMessages = Array.isArray(error) ? error : [error];
                return errorMessages.map((msg, index) => (
                  <li key={`${field}-${index}`}>
                    <span className="font-medium capitalize">{field.replace('_', ' ')}:</span> {msg}
                  </li>
                ));
              })}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Inline Error Component
 */
export const InlineError: React.FC<InlineErrorProps> = ({ error, className = '' }) => {
  if (!error) return null;

  const errorMessages = Array.isArray(error) ? error : [error];

  return (
    <div className={`mt-1 ${className}`}>
      {errorMessages.map((msg, index) => (
        <p key={index} className="text-sm text-red-600 flex items-center">
          <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
          {msg}
        </p>
      ))}
    </div>
  );
};

/**
 * API Error Handler Component
 */
export const APIErrorDisplay: React.FC<{
  error: any;
  onRetry?: () => void;
  className?: string;
}> = ({ error, onRetry, className = '' }) => {
  const { t } = useTranslation();

  // Extract meaningful error message
  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.detail) return error.detail;
    if (error?.error) return error.error;
    if (error?.response?.data?.message) return error.response.data.message;
    if (error?.response?.data?.detail) return error.response.data.detail;
    if (error?.response?.data?.error) return error.response.data.error;
    return t('errors.api.generic', 'An unexpected error occurred');
  };

  const getErrorTitle = (error: any): string => {
    if (error?.response?.status === 401) {
      return t('errors.api.unauthorized', 'Authentication Required');
    }
    if (error?.response?.status === 403) {
      return t('errors.api.forbidden', 'Access Denied');
    }
    if (error?.response?.status === 404) {
      return t('errors.api.notFound', 'Not Found');
    }
    if (error?.response?.status >= 500) {
      return t('errors.api.serverError', 'Server Error');
    }
    if (error?.name === 'NetworkError' || error?.message?.includes('fetch')) {
      return t('errors.api.networkError', 'Network Error');
    }
    return t('errors.api.title', 'Error');
  };

  const actions = onRetry ? [{
    label: t('common.retry', 'Try Again'),
    onClick: onRetry,
    variant: 'primary' as const
  }] : [];

  return (
    <Alert
      type="error"
      title={getErrorTitle(error)}
      message={getErrorMessage(error)}
      actions={actions}
      className={className}
    />
  );
};

/**
 * Success Message Component
 */
export const SuccessMessage: React.FC<{
  message: string;
  title?: string;
  onDismiss?: () => void;
  className?: string;
}> = ({ message, title, onDismiss, className = '' }) => {
  return (
    <Alert
      type="success"
      title={title}
      message={message}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      className={className}
    />
  );
};

/**
 * Warning Message Component
 */
export const WarningMessage: React.FC<{
  message: string;
  title?: string;
  onDismiss?: () => void;
  className?: string;
}> = ({ message, title, onDismiss, className = '' }) => {
  return (
    <Alert
      type="warning"
      title={title}
      message={message}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      className={className}
    />
  );
};

/**
 * Info Message Component
 */
export const InfoMessage: React.FC<{
  message: string;
  title?: string;
  onDismiss?: () => void;
  className?: string;
}> = ({ message, title, onDismiss, className = '' }) => {
  return (
    <Alert
      type="info"
      title={title}
      message={message}
      dismissible={!!onDismiss}
      onDismiss={onDismiss}
      className={className}
    />
  );
};
