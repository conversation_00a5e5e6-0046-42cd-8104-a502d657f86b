#!/usr/bin/env python3
"""
AI Routing Configuration Verification
Verifies that the role-based AI routing has been properly implemented in the frontend configuration.
This script checks the files without needing a running backend server.
"""

import os
import re
import json
from typing import Dict, List, Set
from test_constants import EXPECTED_AI_ROUTES, EXPECTED_AI_ROUTE_MAPPINGS, ALL_ROLES

def check_file_exists(filepath: str) -> bool:
    """Check if a file exists"""
    return os.path.exists(filepath)

def read_file_content(filepath: str) -> str:
    """Read file content safely"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"   ❌ Error reading {filepath}: {e}")
        return ""

def verify_consolidated_routes():
    """Verify that consolidatedRoutes.ts has role-specific AI routes"""
    print("🔍 Checking consolidatedRoutes.ts...")
    
    filepath = "frontend/src/routes/consolidatedRoutes.ts"
    if not check_file_exists(filepath):
        print(f"   ❌ File not found: {filepath}")
        return False
    
    content = read_file_content(filepath)
    if not content:
        return False
    
    # Check for role-specific AI routes (using centralized role constants)
    expected_routes = EXPECTED_AI_ROUTES
    
    found_routes = []
    missing_routes = []
    
    for route in expected_routes:
        if route in content:
            found_routes.append(route)
            print(f"   ✅ Found route: {route}")
        else:
            missing_routes.append(route)
            print(f"   ❌ Missing route: {route}")
    
    # Check that old dashboard/ai route is removed
    old_route_pattern = r"'/dashboard/ai'"
    if re.search(old_route_pattern, content):
        print("   ⚠️  Old '/dashboard/ai' route still exists")
        return False
    else:
        print("   ✅ Old '/dashboard/ai' route properly removed")
    
    return len(missing_routes) == 0

def verify_navigation_config():
    """Verify navigationConfig.ts has getRolePath function"""
    print("\n🔍 Checking navigationConfig.ts...")
    
    filepath = "frontend/src/config/navigationConfig.ts"
    if not check_file_exists(filepath):
        print(f"   ❌ File not found: {filepath}")
        return False
    
    content = read_file_content(filepath)
    if not content:
        return False
    
    # Check for getRolePath function
    if "getRolePath" in content:
        print("   ✅ getRolePath function found")
    else:
        print("   ❌ getRolePath function missing")
        return False
    
    # Check for role-specific AI paths in getRolePath (using centralized constants)
    role_paths = [f"'/{role}/ai'" for role in ALL_ROLES]
    
    found_paths = 0
    for path in role_paths:
        if path in content:
            found_paths += 1
            print(f"   ✅ Found path: {path}")
    
    if found_paths >= 6:  # Should have most role paths
        print(f"   ✅ Found {found_paths}/{len(role_paths)} role paths")
        return True
    else:
        print(f"   ❌ Only found {found_paths}/{len(role_paths)} role paths")
        return False

def verify_role_route_mapping():
    """Verify centralizedRoleRouteMapping.ts has new AI routes"""
    print("\n🔍 Checking centralizedRoleRouteMapping.ts...")
    
    filepath = "frontend/src/config/centralizedRoleRouteMapping.ts"
    if not check_file_exists(filepath):
        print(f"   ❌ File not found: {filepath}")
        return False
    
    content = read_file_content(filepath)
    if not content:
        return False
    
    # Check for role-specific AI route mappings (using centralized constants)
    expected_mappings = [f"path: '/{role}/ai'" for role in ALL_ROLES]
    
    found_mappings = 0
    for mapping in expected_mappings:
        if mapping in content:
            found_mappings += 1
            print(f"   ✅ Found mapping: {mapping}")
    
    if found_mappings >= 6:
        print(f"   ✅ Found {found_mappings}/{len(expected_mappings)} route mappings")
        return True
    else:
        print(f"   ❌ Only found {found_mappings}/{len(expected_mappings)} route mappings")
        return False

def verify_unified_role_manager():
    """Verify unifiedRoleManager.ts has getAIRoute function"""
    print("\n🔍 Checking unifiedRoleManager.ts...")
    
    filepath = "frontend/src/utils/unifiedRoleManager.ts"
    if not check_file_exists(filepath):
        print(f"   ❌ File not found: {filepath}")
        return False
    
    content = read_file_content(filepath)
    if not content:
        return False
    
    # Check for getAIRoute function
    if "export function getAIRoute" in content:
        print("   ✅ getAIRoute function found")
    else:
        print("   ❌ getAIRoute function missing")
        return False
    
    # Check for role-specific AI route mappings in the function
    ai_routes = [
        "'super_admin': '/super_admin/ai'",
        "'admin': '/admin/ai'",
        "'moderator': '/moderator/ai'",
        "'entrepreneur': '/entrepreneur/ai'",
        "'mentor': '/mentor/ai'",
        "'investor': '/investor/ai'",
        "'user': '/user/ai'"
    ]
    
    found_routes = 0
    for route in ai_routes:
        if route in content:
            found_routes += 1
    
    if found_routes >= 6:
        print(f"   ✅ Found {found_routes}/{len(ai_routes)} AI route mappings")
        return True
    else:
        print(f"   ❌ Only found {found_routes}/{len(ai_routes)} AI route mappings")
        return False

def verify_ai_dashboard_page():
    """Verify AIDashboardPage.tsx uses role-specific routing"""
    print("\n🔍 Checking AIDashboardPage.tsx...")
    
    filepath = "frontend/src/pages/dashboard/AIDashboardPage.tsx"
    if not check_file_exists(filepath):
        print(f"   ❌ File not found: {filepath}")
        return False
    
    content = read_file_content(filepath)
    if not content:
        return False
    
    # Check for getAIRoute import
    if "getAIRoute" in content:
        print("   ✅ getAIRoute import found")
    else:
        print("   ❌ getAIRoute import missing")
        return False
    
    # Check that old hardcoded routes are removed
    old_routes = [
        "'/dashboard/ai/analytics'",
        "'/dashboard/ai/intelligence'"
    ]
    
    has_old_routes = False
    for route in old_routes:
        if route in content:
            print(f"   ⚠️  Old hardcoded route still exists: {route}")
            has_old_routes = True
    
    if not has_old_routes:
        print("   ✅ Old hardcoded routes removed")
        return True
    else:
        return False

def verify_role_files():
    """Verify role-specific files have updated AI routes"""
    print("\n🔍 Checking role-specific files...")
    
    role_files = {
        "user": "frontend/src/roles/user.ts",
        "entrepreneur": "frontend/src/roles/entrepreneur.ts", 
        "mentor": "frontend/src/roles/mentor.ts",
        "investor": "frontend/src/roles/investor.ts",
        "moderator": "frontend/src/roles/moderator.ts",
        "admin": "frontend/src/roles/admin.ts"
    }
    
    success_count = 0
    
    for role, filepath in role_files.items():
        if not check_file_exists(filepath):
            print(f"   ❌ File not found: {filepath}")
            continue
        
        content = read_file_content(filepath)
        if not content:
            continue
        
        # Check for role-specific AI route
        expected_route = f"'/{role}/ai'"
        if expected_route in content:
            print(f"   ✅ {role}: Found AI route {expected_route}")
            success_count += 1
        else:
            print(f"   ❌ {role}: Missing AI route {expected_route}")
    
    return success_count >= 5  # Most roles should be updated

def main():
    """Run all verification checks"""
    print("🚀 Verifying AI Role-Based Routing Implementation")
    print("=" * 60)
    
    checks = [
        ("Route Configuration", verify_consolidated_routes),
        ("Navigation Config", verify_navigation_config),
        ("Route Mapping", verify_role_route_mapping),
        ("Role Manager", verify_unified_role_manager),
        ("AI Dashboard Page", verify_ai_dashboard_page),
        ("Role Files", verify_role_files)
    ]
    
    results = {}
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"   ❌ Error in {check_name}: {e}")
            results[check_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{check_name.ljust(20)}: {status}")
        if success:
            passed_checks += 1
    
    print(f"\nOverall: {passed_checks}/{total_checks} checks passed")
    
    if passed_checks == total_checks:
        print("\n🎉 All AI role-based routing verification checks PASSED!")
        print("✅ The implementation appears to be correctly configured.")
        return 0
    else:
        print(f"\n💥 {total_checks - passed_checks} verification checks FAILED!")
        print("❌ Some configuration issues need to be addressed.")
        return 1

if __name__ == "__main__":
    exit(main())
