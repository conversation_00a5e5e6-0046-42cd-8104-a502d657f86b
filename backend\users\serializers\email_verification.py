"""
Email Verification Serializers
Handles serialization for email verification endpoints
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from ..models.email_verification import EmailVerificationToken, EmailVerificationAttempt


class EmailVerificationRequestSerializer(serializers.Serializer):
    """
    Serializer for requesting email verification
    """
    email = serializers.EmailField(
        help_text="Email address to verify"
    )
    
    def validate_email(self, value):
        """
        Validate that the email belongs to the authenticated user
        """
        user = self.context['request'].user
        if not user.is_authenticated:
            raise serializers.ValidationError("Authentication required")
        
        # Allow verification of user's current email or a new email they want to change to
        if value != user.email:
            # Check if this email is already taken by another user
            if User.objects.filter(email=value).exclude(pk=user.pk).exists():
                raise serializers.ValidationError("This email is already registered to another account")
        
        return value
    
    def create(self, validated_data):
        """
        Create and send verification token
        """
        user = self.context['request'].user
        email = validated_data['email']
        request = self.context['request']
        
        # Check if user can send verification email
        can_send, message = EmailVerificationAttempt.can_send_verification(user, email)
        if not can_send:
            raise serializers.ValidationError(message)
        
        # Create verification token
        token = EmailVerificationToken.create_for_user(user, email)
        
        # Send verification email
        success = token.send_verification_email(request)
        
        # Record attempt
        EmailVerificationAttempt.record_attempt(
            user=user,
            email=email,
            success=success,
            ip_address=self.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
        
        if not success:
            raise serializers.ValidationError("Failed to send verification email. Please try again later.")
        
        return {
            'email': email,
            'message': 'Verification email sent successfully',
            'expires_at': token.expires_at
        }
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class EmailVerificationConfirmSerializer(serializers.Serializer):
    """
    Serializer for confirming email verification
    """
    token = serializers.UUIDField(
        help_text="Email verification token"
    )
    
    def validate_token(self, value):
        """
        Validate the verification token
        """
        try:
            token = EmailVerificationToken.objects.get(token=value)
            if not token.is_valid:
                raise serializers.ValidationError("Token is invalid or expired")
            return value
        except EmailVerificationToken.DoesNotExist:
            raise serializers.ValidationError("Invalid token")
    
    def create(self, validated_data):
        """
        Verify the email using the token
        """
        token_uuid = validated_data['token']
        success, message = EmailVerificationToken.verify_token(token_uuid)
        
        if not success:
            raise serializers.ValidationError(message)
        
        # Get the token to return user info
        token = EmailVerificationToken.objects.get(token=token_uuid)
        
        return {
            'message': message,
            'email': token.email,
            'user_id': token.user.id,
            'verified_at': token.used_at
        }


class EmailVerificationStatusSerializer(serializers.ModelSerializer):
    """
    Serializer for email verification status
    """
    email_verified = serializers.SerializerMethodField()
    email_verified_at = serializers.SerializerMethodField()
    pending_verification_email = serializers.SerializerMethodField()
    can_resend_verification = serializers.SerializerMethodField()
    next_resend_available_at = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'email',
            'email_verified',
            'email_verified_at',
            'pending_verification_email',
            'can_resend_verification',
            'next_resend_available_at'
        ]
    
    def get_email_verified(self, obj):
        """Check if user's email is verified"""
        if hasattr(obj, 'profile'):
            return getattr(obj.profile, 'email_verified', False)
        return False
    
    def get_email_verified_at(self, obj):
        """Get email verification timestamp"""
        if hasattr(obj, 'profile'):
            return getattr(obj.profile, 'email_verified_at', None)
        return None
    
    def get_pending_verification_email(self, obj):
        """Get email address that has pending verification"""
        pending_token = EmailVerificationToken.objects.filter(
            user=obj,
            is_used=False,
            expires_at__gt=timezone.now()
        ).first()
        
        return pending_token.email if pending_token else None
    
    def get_can_resend_verification(self, obj):
        """Check if user can resend verification email"""
        email = obj.email
        can_send, _ = EmailVerificationAttempt.can_send_verification(obj, email)
        return can_send
    
    def get_next_resend_available_at(self, obj):
        """Get timestamp when user can next resend verification"""
        from django.utils import timezone
        from datetime import timedelta
        from ..models.email_verification import EmailVerificationSettings
        
        settings = EmailVerificationSettings.get_settings()
        
        # Find the most recent attempt
        recent_attempt = EmailVerificationAttempt.objects.filter(
            user=obj,
            email=obj.email
        ).first()
        
        if recent_attempt:
            next_available = recent_attempt.attempted_at + timedelta(minutes=settings.resend_cooldown_minutes)
            if next_available > timezone.now():
                return next_available
        
        return None


class EmailVerificationTokenSerializer(serializers.ModelSerializer):
    """
    Serializer for email verification tokens (admin use)
    """
    is_expired = serializers.ReadOnlyField()
    is_valid = serializers.ReadOnlyField()
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = EmailVerificationToken
        fields = [
            'id',
            'user',
            'user_username',
            'user_email',
            'token',
            'email',
            'created_at',
            'expires_at',
            'is_used',
            'used_at',
            'is_expired',
            'is_valid'
        ]
        read_only_fields = [
            'token',
            'created_at',
            'is_used',
            'used_at',
            'is_expired',
            'is_valid'
        ]


class EmailVerificationAttemptSerializer(serializers.ModelSerializer):
    """
    Serializer for email verification attempts (admin use)
    """
    user_username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = EmailVerificationAttempt
        fields = [
            'id',
            'user',
            'user_username',
            'email',
            'attempted_at',
            'ip_address',
            'user_agent',
            'success'
        ]
        read_only_fields = [
            'attempted_at'
        ]
