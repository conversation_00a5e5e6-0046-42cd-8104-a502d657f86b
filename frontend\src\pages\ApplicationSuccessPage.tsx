/**
 * Application Success Page
 * Confirmation page after successful application submission
 */

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../components/ui/ArabicOptimizedComponents';
import { 
  CheckCircle, 
  Calendar, 
  Mail, 
  Phone, 
  FileText, 
  Users, 
  Clock,
  ArrowRight,
  Home
} from 'lucide-react';

interface NextStep {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  timeline: string;
  timelineAr: string;
  icon: React.ReactNode;
  status: 'pending' | 'in_progress' | 'completed';
}

const ApplicationSuccessPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [applicationId] = useState(`APP-${Date.now()}`);

  const nextSteps: NextStep[] = [
    {
      id: 'review',
      title: 'Application Review',
      titleAr: 'مراجعة الطلب',
      description: 'Our team will review your application and assess your startup potential',
      descriptionAr: 'سيقوم فريقنا بمراجعة طلبك وتقييم إمكانات شركتك الناشئة',
      timeline: '3-5 business days',
      timelineAr: '3-5 أيام عمل',
      icon: <FileText className="w-5 h-5" />,
      status: 'pending'
    },
    {
      id: 'interview',
      title: 'Initial Interview',
      titleAr: 'المقابلة الأولية',
      description: 'If selected, we will schedule a video interview to discuss your application',
      descriptionAr: 'في حالة الاختيار، سنحدد موعد مقابلة فيديو لمناقشة طلبك',
      timeline: '1-2 weeks after review',
      timelineAr: '1-2 أسبوع بعد المراجعة',
      icon: <Users className="w-5 h-5" />,
      status: 'pending'
    },
    {
      id: 'evaluation',
      title: 'Final Evaluation',
      titleAr: 'التقييم النهائي',
      description: 'Comprehensive evaluation including pitch presentation and due diligence',
      descriptionAr: 'تقييم شامل يشمل عرض الفكرة والعناية الواجبة',
      timeline: '2-3 weeks',
      timelineAr: '2-3 أسابيع',
      icon: <Calendar className="w-5 h-5" />,
      status: 'pending'
    },
    {
      id: 'decision',
      title: 'Final Decision',
      titleAr: 'القرار النهائي',
      description: 'You will receive our final decision and next steps if accepted',
      descriptionAr: 'ستتلقى قرارنا النهائي والخطوات التالية في حالة القبول',
      timeline: '4-6 weeks total',
      timelineAr: '4-6 أسابيع إجمالي',
      icon: <CheckCircle className="w-5 h-5" />,
      status: 'pending'
    }
  ];

  const contactInfo = [
    {
      type: 'email',
      label: language === 'ar' ? 'البريد الإلكتروني' : 'Email',
      value: '<EMAIL>',
      icon: <Mail className="w-5 h-5" />
    },
    {
      type: 'phone',
      label: language === 'ar' ? 'الهاتف' : 'Phone',
      value: '+966 11 123 4567',
      icon: <Phone className="w-5 h-5" />
    }
  ];

  useEffect(() => {
    // Track application submission
    console.log('Application submitted successfully:', applicationId);
  }, [applicationId]);

  return (
    <div className={`min-h-screen bg-gray-50 py-8 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-6">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-12 h-12 text-green-600" />
            </div>
          </div>
          
          <ArabicTypography variant="h1" className="text-gray-900 font-bold mb-4">
            {language === 'ar' ? 'تم إرسال طلبك بنجاح!' : 'Application Submitted Successfully!'}
          </ArabicTypography>
          
          <ArabicTypography variant="body1" color="secondary" className="mb-6">
            {language === 'ar' 
              ? 'شكراً لك على اهتمامك بالانضمام لحاضنة يسمين للذكاء الاصطناعي. لقد تم استلام طلبك وسنتواصل معك قريباً.'
              : 'Thank you for your interest in joining Yasmeen AI Incubator. We have received your application and will be in touch soon.'
            }
          </ArabicTypography>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 inline-block">
            <ArabicTypography variant="body2" className="text-blue-800 font-semibold">
              {language === 'ar' ? 'رقم الطلب:' : 'Application ID:'} {applicationId}
            </ArabicTypography>
          </div>
        </div>

        {/* Next Steps */}
        <ArabicCard 
          title={language === 'ar' ? 'الخطوات التالية' : 'What Happens Next'}
          className="mb-8"
        >
          <div className="space-y-6">
            {nextSteps.map((step, index) => (
              <div key={step.id} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 ${isRTL ? 'ml-4' : 'mr-4'}`}>
                  {step.icon}
                </div>
                <div className="flex-1">
                  <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <ArabicTypography variant="h5" className="text-gray-900 font-semibold">
                      {language === 'ar' ? step.titleAr : step.title}
                    </ArabicTypography>
                    <div className={`flex items-center text-sm text-gray-500 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Clock className={`w-4 h-4 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                      <span className="font-arabic">
                        {language === 'ar' ? step.timelineAr : step.timeline}
                      </span>
                    </div>
                  </div>
                  <ArabicTypography variant="body2" color="secondary" className="mt-1">
                    {language === 'ar' ? step.descriptionAr : step.description}
                  </ArabicTypography>
                  
                  {index < nextSteps.length - 1 && (
                    <div className={`mt-4 w-px h-6 bg-gray-200 ${isRTL ? 'mr-5' : 'ml-5'}`}></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </ArabicCard>

        {/* Important Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Contact Information */}
          <ArabicCard title={language === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}>
            <div className="space-y-4">
              <ArabicTypography variant="body2" color="secondary" className="mb-4">
                {language === 'ar' 
                  ? 'إذا كان لديك أي أسئلة حول طلبك، لا تتردد في التواصل معنا:'
                  : 'If you have any questions about your application, feel free to contact us:'
                }
              </ArabicTypography>
              
              {contactInfo.map((contact) => (
                <div key={contact.type} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`text-blue-600 ${isRTL ? 'ml-3' : 'mr-3'}`}>
                    {contact.icon}
                  </div>
                  <div>
                    <ArabicTypography variant="body2" className="text-gray-600">
                      {contact.label}
                    </ArabicTypography>
                    <ArabicTypography variant="body2" className="text-gray-900 font-semibold">
                      {contact.value}
                    </ArabicTypography>
                  </div>
                </div>
              ))}
            </div>
          </ArabicCard>

          {/* What to Expect */}
          <ArabicCard title={language === 'ar' ? 'ما يمكن توقعه' : 'What to Expect'}>
            <div className="space-y-4">
              <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`w-2 h-2 bg-blue-500 rounded-full mt-2 ${isRTL ? 'ml-3' : 'mr-3'}`}></div>
                <ArabicTypography variant="body2" color="secondary">
                  {language === 'ar' 
                    ? 'ستتلقى رسالة تأكيد عبر البريد الإلكتروني خلال 24 ساعة'
                    : 'You will receive a confirmation email within 24 hours'
                  }
                </ArabicTypography>
              </div>
              
              <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`w-2 h-2 bg-blue-500 rounded-full mt-2 ${isRTL ? 'ml-3' : 'mr-3'}`}></div>
                <ArabicTypography variant="body2" color="secondary">
                  {language === 'ar' 
                    ? 'سيتم تحديث حالة طلبك عبر البريد الإلكتروني والرسائل النصية'
                    : 'Application status updates will be sent via email and SMS'
                  }
                </ArabicTypography>
              </div>
              
              <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`w-2 h-2 bg-blue-500 rounded-full mt-2 ${isRTL ? 'ml-3' : 'mr-3'}`}></div>
                <ArabicTypography variant="body2" color="secondary">
                  {language === 'ar' 
                    ? 'يمكنك تتبع حالة طلبك من خلال لوحة التحكم الخاصة بك'
                    : 'You can track your application status through your dashboard'
                  }
                </ArabicTypography>
              </div>
              
              <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`w-2 h-2 bg-blue-500 rounded-full mt-2 ${isRTL ? 'ml-3' : 'mr-3'}`}></div>
                <ArabicTypography variant="body2" color="secondary">
                  {language === 'ar' 
                    ? 'ستحصل على ملاحظات مفصلة بغض النظر عن نتيجة الطلب'
                    : 'You will receive detailed feedback regardless of the application outcome'
                  }
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>
        </div>

        {/* Action Buttons */}
        <div className={`flex flex-col sm:flex-row gap-4 justify-center ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
          <ArabicButton
            variant="primary"
            onClick={() => navigate('/dashboard')}
            icon={<Home className="w-4 h-4" />}
            iconPosition="start"
          >
            {language === 'ar' ? 'العودة للوحة التحكم' : 'Go to Dashboard'}
          </ArabicButton>
          
          <ArabicButton
            variant="outline"
            onClick={() => navigate('/application/status')}
            icon={<ArrowRight className="w-4 h-4" />}
            iconPosition="end"
          >
            {language === 'ar' ? 'تتبع حالة الطلب' : 'Track Application Status'}
          </ArabicButton>
        </div>

        {/* Additional Resources */}
        <div className="mt-12 text-center">
          <ArabicTypography variant="h4" className="text-gray-900 font-bold mb-4">
            {language === 'ar' ? 'في انتظار النتائج؟' : 'While You Wait?'}
          </ArabicTypography>
          
          <ArabicTypography variant="body1" color="secondary" className="mb-6">
            {language === 'ar' 
              ? 'استفد من مواردنا المجانية لتطوير فكرة عملك'
              : 'Take advantage of our free resources to develop your business idea'
            }
          </ArabicTypography>

          <div className={`flex flex-wrap justify-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <ArabicButton
              variant="ghost"
              onClick={() => navigate('/resources')}
            >
              {language === 'ar' ? 'الموارد المجانية' : 'Free Resources'}
            </ArabicButton>
            
            <ArabicButton
              variant="ghost"
              onClick={() => navigate('/webinars')}
            >
              {language === 'ar' ? 'الندوات التعليمية' : 'Educational Webinars'}
            </ArabicButton>
            
            <ArabicButton
              variant="ghost"
              onClick={() => navigate('/community')}
            >
              {language === 'ar' ? 'انضم للمجتمع' : 'Join Community'}
            </ArabicButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationSuccessPage;
