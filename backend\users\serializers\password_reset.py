"""
Password Reset Serializers
Handles serialization for password reset endpoints
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from ..models.password_reset import PasswordResetToken, PasswordResetAttempt, PasswordResetSettings


class PasswordResetRequestSerializer(serializers.Serializer):
    """
    Serializer for requesting password reset
    """
    email = serializers.EmailField(
        help_text="Email address associated with the account"
    )
    
    def validate_email(self, value):
        """
        Validate that the email exists in the system
        """
        try:
            user = User.objects.get(email=value)
            
            # Check if email verification is required and if user's email is verified
            settings = PasswordResetSettings.get_settings()
            if settings.require_email_verification:
                if hasattr(user, 'profile') and not getattr(user.profile, 'email_verified', False):
                    raise serializers.ValidationError(
                        "Please verify your email address before requesting a password reset."
                    )
            
            return value
        except User.DoesNotExist:
            # For security, don't reveal if email exists or not
            # Return the email anyway, but we'll handle it in create()
            return value
    
    def create(self, validated_data):
        """
        Create and send password reset token
        """
        email = validated_data['email']
        request = self.context['request']
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Check rate limiting
        can_request, message = PasswordResetAttempt.can_request_reset(email, ip_address)
        if not can_request:
            raise serializers.ValidationError(message)
        
        try:
            user = User.objects.get(email=email)
            
            # Create password reset token
            token = PasswordResetToken.create_for_user(
                user=user,
                email=email,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Send reset email
            success = token.send_reset_email(request)
            
            # Record attempt
            PasswordResetAttempt.record_attempt(
                email=email,
                success=success,
                ip_address=ip_address,
                user_agent=user_agent,
                user=user
            )
            
            if not success:
                raise serializers.ValidationError("Failed to send password reset email. Please try again later.")
            
            return {
                'email': email,
                'message': 'If an account with this email exists, a password reset link has been sent.',
                'expires_at': token.expires_at
            }
            
        except User.DoesNotExist:
            # Record attempt even for non-existent users for security monitoring
            PasswordResetAttempt.record_attempt(
                email=email,
                success=False,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Return success message for security (don't reveal if email exists)
            return {
                'email': email,
                'message': 'If an account with this email exists, a password reset link has been sent.',
                'expires_at': None
            }
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
    Serializer for confirming password reset
    """
    token = serializers.UUIDField(
        help_text="Password reset token"
    )
    new_password = serializers.CharField(
        write_only=True,
        help_text="New password"
    )
    confirm_password = serializers.CharField(
        write_only=True,
        help_text="Confirm new password"
    )
    
    def validate_token(self, value):
        """
        Validate the password reset token
        """
        try:
            token = PasswordResetToken.objects.get(token=value)
            if not token.is_valid:
                raise serializers.ValidationError("Token is invalid or expired")
            return value
        except PasswordResetToken.DoesNotExist:
            raise serializers.ValidationError("Invalid token")
    
    def validate_new_password(self, value):
        """
        Validate the new password using Django's password validators
        """
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(e.messages)
        return value
    
    def validate(self, attrs):
        """
        Validate that passwords match
        """
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError("Passwords do not match")
        return attrs
    
    def create(self, validated_data):
        """
        Reset the password using the token
        """
        token_uuid = validated_data['token']
        new_password = validated_data['new_password']
        
        success, message = PasswordResetToken.reset_password(token_uuid, new_password)
        
        if not success:
            raise serializers.ValidationError(message)
        
        # Get the token to return user info
        token = PasswordResetToken.objects.get(token=token_uuid)
        
        return {
            'message': message,
            'email': token.email,
            'user_id': token.user.id,
            'reset_at': token.used_at
        }


class PasswordResetVerifyTokenSerializer(serializers.Serializer):
    """
    Serializer for verifying password reset token validity
    """
    token = serializers.UUIDField(
        help_text="Password reset token to verify"
    )
    
    def validate_token(self, value):
        """
        Validate the password reset token
        """
        try:
            token = PasswordResetToken.objects.get(token=value)
            if not token.is_valid:
                raise serializers.ValidationError("Token is invalid or expired")
            return value
        except PasswordResetToken.DoesNotExist:
            raise serializers.ValidationError("Invalid token")
    
    def create(self, validated_data):
        """
        Return token information if valid
        """
        token_uuid = validated_data['token']
        token = PasswordResetToken.objects.get(token=token_uuid)
        
        return {
            'valid': True,
            'email': token.email,
            'expires_at': token.expires_at,
            'user_id': token.user.id
        }


class PasswordResetTokenSerializer(serializers.ModelSerializer):
    """
    Serializer for password reset tokens (admin use)
    """
    is_expired = serializers.ReadOnlyField()
    is_valid = serializers.ReadOnlyField()
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = PasswordResetToken
        fields = [
            'id',
            'user',
            'user_username',
            'user_email',
            'token',
            'email',
            'created_at',
            'expires_at',
            'is_used',
            'used_at',
            'ip_address',
            'user_agent',
            'is_expired',
            'is_valid'
        ]
        read_only_fields = [
            'token',
            'secure_hash',
            'created_at',
            'is_used',
            'used_at',
            'is_expired',
            'is_valid'
        ]


class PasswordResetAttemptSerializer(serializers.ModelSerializer):
    """
    Serializer for password reset attempts (admin use)
    """
    user_username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = PasswordResetAttempt
        fields = [
            'id',
            'email',
            'ip_address',
            'user_agent',
            'attempted_at',
            'success',
            'user',
            'user_username'
        ]
        read_only_fields = [
            'attempted_at'
        ]


class PasswordResetSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for password reset settings
    """
    class Meta:
        model = PasswordResetSettings
        fields = [
            'token_expiry_hours',
            'max_attempts_per_email_per_hour',
            'max_attempts_per_ip_per_hour',
            'require_email_verification'
        ]
