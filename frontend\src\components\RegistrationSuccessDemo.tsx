import React from 'react';
import { useAppDispatch } from '../store/hooks';
import { setRegistrationData, completeRegistration } from '../store/registrationSlice';

const RegistrationSuccessDemo: React.FC = () => {
  const dispatch = useAppDispatch();

  const testUsers = [
    {
      firstName: '<PERSON>',
      lastName: '<PERSON><PERSON><PERSON>',
      selectedRole: 'entrepreneur',
      requiresApproval: true,
      description: 'Entrepreneur with tech startup idea'
    },
    {
      firstName: 'Fatima',
      lastName: '<PERSON>',
      selectedRole: 'mentor',
      requiresApproval: true,
      description: 'Experienced business mentor'
    },
    {
      firstName: '<PERSON>',
      lastName: '<PERSON><PERSON><PERSON>',
      selectedRole: 'investor',
      requiresApproval: true,
      description: 'Angel investor looking for opportunities'
    },
    {
      firstName: 'Sara',
      lastName: 'Mohammed',
      selectedRole: 'user',
      requiresApproval: false,
      description: 'Community member interested in learning'
    }
  ];

  const setTestUser = (user: typeof testUsers[0]) => {
    dispatch(setRegistrationData({
      firstName: user.firstName,
      lastName: user.lastName,
      username: `${user.firstName.toLowerCase()}_${user.selectedRole}`,
      email: `${user.firstName.toLowerCase()}@example.com`,
      selectedRole: user.selectedRole,
    }));
    
    dispatch(completeRegistration({
      requiresApproval: user.requiresApproval
    }));
    
    // Refresh the page to show updated content
    window.location.reload();
  };

  return (
    <div className="fixed top-4 right-4 bg-white/10 backdrop-blur-lg rounded-lg p-4 border border-white/20 z-50">
      <h3 className="text-white font-bold mb-3">Test Different Users</h3>
      <div className="space-y-2">
        {testUsers.map((user, index) => (
          <button
            key={index}
            onClick={() => setTestUser(user)}
            className="w-full text-left p-2 bg-blue-500/20 hover:bg-blue-500/30 rounded text-white text-sm transition-colors"
          >
            <div className="font-semibold">{user.firstName} - {user.selectedRole}</div>
            <div className="text-xs text-gray-300">{user.description}</div>
          </button>
        ))}
      </div>
      <button
        onClick={() => {
          dispatch(setRegistrationData({
            firstName: '',
            lastName: '',
            selectedRole: '',
            username: '',
            email: '',
          }));
          window.location.reload();
        }}
        className="w-full mt-3 p-2 bg-red-500/20 hover:bg-red-500/30 rounded text-white text-sm transition-colors"
      >
        Clear Data
      </button>
    </div>
  );
};

export default RegistrationSuccessDemo;
