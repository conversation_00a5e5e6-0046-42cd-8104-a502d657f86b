#!/usr/bin/env python3
"""
Page Import Test
Tests that all page components referenced in routes can be imported without errors
"""

import os
import re
import json
from pathlib import Path

def extract_imports_from_routes():
    """Extract all lazy import statements from consolidatedRoutes.ts"""
    routes_file = "frontend/src/routes/consolidatedRoutes.ts"
    
    if not os.path.exists(routes_file):
        print(f"❌ Routes file not found: {routes_file}")
        return []
    
    with open(routes_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all lazy import statements
    import_pattern = r"const\s+(\w+)\s*=\s*lazy\(\(\)\s*=>\s*import\(['\"]([^'\"]+)['\"]\)\)"
    imports = re.findall(import_pattern, content)
    
    return imports

def check_file_exists(import_path):
    """Check if the imported file exists"""
    # Convert import path to file system path
    base_path = "frontend/src"
    
    # Remove leading '../' and convert to file path
    clean_path = import_path.replace('../', '')
    full_path = os.path.join(base_path, clean_path)
    
    # Try with .tsx extension if not present
    if not full_path.endswith('.tsx') and not full_path.endswith('.ts'):
        full_path += '.tsx'
    
    return os.path.exists(full_path), full_path

def analyze_route_imports():
    """Analyze all route imports and check if files exist"""
    print("🔍 Analyzing Route Imports")
    print("=" * 50)
    
    imports = extract_imports_from_routes()
    
    if not imports:
        print("❌ No imports found in routes file")
        return
    
    print(f"Found {len(imports)} lazy import statements")
    print()
    
    missing_files = []
    existing_files = []
    
    for component_name, import_path in imports:
        exists, full_path = check_file_exists(import_path)
        
        if exists:
            existing_files.append((component_name, import_path, full_path))
            print(f"✅ {component_name}: {import_path}")
        else:
            missing_files.append((component_name, import_path, full_path))
            print(f"❌ {component_name}: {import_path} -> {full_path}")
    
    print()
    print("=" * 50)
    print("📊 IMPORT ANALYSIS SUMMARY")
    print("=" * 50)
    
    print(f"Total Imports: {len(imports)}")
    print(f"Existing Files: {len(existing_files)}")
    print(f"Missing Files: {len(missing_files)}")
    
    if missing_files:
        print()
        print("❌ MISSING FILES:")
        for component_name, import_path, full_path in missing_files:
            print(f"   - {component_name}: {full_path}")
            
        print()
        print("🔧 RECOMMENDED ACTIONS:")
        print("1. Create the missing files")
        print("2. Or update routes to point to existing files")
        print("3. Or remove unused route definitions")
    else:
        print()
        print("🎉 ALL IMPORTS ARE VALID!")
        print("All page components referenced in routes exist in the filesystem.")
    
    return {
        'total': len(imports),
        'existing': len(existing_files),
        'missing': len(missing_files),
        'missing_files': missing_files,
        'existing_files': existing_files
    }

def check_critical_pages():
    """Check if critical pages exist for each role"""
    print()
    print("🎯 Checking Critical Pages for Each Role")
    print("=" * 50)
    
    critical_pages = {
        'user': [
            'frontend/src/pages/dashboard/UserDashboardPage.tsx',
            'frontend/src/pages/dashboard/BusinessIdeasPage.tsx',
            'frontend/src/pages/dashboard/BusinessPlansPage.tsx'
        ],
        'entrepreneur': [
            'frontend/src/pages/dashboard/NewBusinessIdeaPage.tsx',
            'frontend/src/pages/dashboard/NewBusinessPlanPage.tsx',
            'frontend/src/pages/dashboard/FundingPage.tsx',
            'frontend/src/pages/dashboard/FundingApplicationsPage.tsx'
        ],
        'mentor': [
            'frontend/src/pages/dashboard/MentorDashboardPage.tsx',
            'frontend/src/pages/dashboard/MentorshipSessionsPage.tsx',
            'frontend/src/pages/dashboard/MenteesManagementPage.tsx'
        ],
        'investor': [
            'frontend/src/pages/investor/InvestorDashboardPage.tsx',
            'frontend/src/pages/dashboard/InvestorOpportunitiesPage.tsx',
            'frontend/src/pages/dashboard/PortfolioManagementPage.tsx'
        ],
        'moderator': [
            'frontend/src/pages/moderator/ModeratorDashboardPage.tsx',
            'frontend/src/pages/dashboard/ContentModerationPage.tsx',
            'frontend/src/pages/dashboard/ForumModerationPage.tsx'
        ],
        'admin': [
            'frontend/src/pages/admin/AdminDashboardPage.tsx',
            'frontend/src/components/admin/users/UsersManagement.tsx',
            'frontend/src/pages/admin/AdminAnalyticsPage.tsx'
        ],
        'super_admin': [
            'frontend/src/pages/super-admin/SuperAdminDashboardPage.tsx',
            'frontend/src/components/admin/super-admin/SystemManagement.tsx',
            'frontend/src/components/admin/super-admin/UserImpersonation.tsx'
        ]
    }
    
    role_status = {}
    
    for role, pages in critical_pages.items():
        missing_pages = []
        existing_pages = []
        
        for page_path in pages:
            if os.path.exists(page_path):
                existing_pages.append(page_path)
            else:
                missing_pages.append(page_path)
        
        role_status[role] = {
            'total': len(pages),
            'existing': len(existing_pages),
            'missing': len(missing_pages),
            'missing_pages': missing_pages
        }
        
        status_icon = "✅" if len(missing_pages) == 0 else "⚠️" if len(missing_pages) < len(pages) / 2 else "❌"
        print(f"{status_icon} {role.upper()}: {len(existing_pages)}/{len(pages)} pages exist")
        
        if missing_pages:
            for missing_page in missing_pages:
                print(f"   ❌ Missing: {missing_page}")
    
    return role_status

def main():
    """Main test function"""
    print("🚀 Starting Page Import Analysis")
    print("Testing all page components referenced in routing configuration")
    print()
    
    # Test 1: Analyze route imports
    import_results = analyze_route_imports()
    
    # Test 2: Check critical pages
    role_results = check_critical_pages()
    
    # Final summary
    print()
    print("=" * 50)
    print("🏁 FINAL SUMMARY")
    print("=" * 50)
    
    if import_results:
        import_success_rate = (import_results['existing'] / import_results['total']) * 100
        print(f"Import Success Rate: {import_success_rate:.1f}% ({import_results['existing']}/{import_results['total']})")
    
    total_critical_pages = sum(role['total'] for role in role_results.values())
    total_existing_critical = sum(role['existing'] for role in role_results.values())
    critical_success_rate = (total_existing_critical / total_critical_pages) * 100
    
    print(f"Critical Pages Success Rate: {critical_success_rate:.1f}% ({total_existing_critical}/{total_critical_pages})")
    
    # Overall assessment
    if import_results and import_results['missing'] == 0 and critical_success_rate >= 90:
        print()
        print("🎉 EXCELLENT! All imports are valid and most critical pages exist.")
        print("The application should load without major routing errors.")
    elif import_results and import_results['missing'] <= 2 and critical_success_rate >= 80:
        print()
        print("✅ GOOD! Most imports are valid with only minor issues.")
        print("The application should work with minimal routing problems.")
    else:
        print()
        print("⚠️ ISSUES FOUND! Several missing pages may cause routing errors.")
        print("Consider creating missing pages or updating route configurations.")

if __name__ == "__main__":
    main()
