/**
 * Arabic-Optimized UI Components
 * Enhanced components specifically designed for superior Arabic/RTL experience
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { ChevronLeft, ChevronRight, Calendar, Clock, User, Star } from 'lucide-react';

// Enhanced RTL-aware Button Component
interface ArabicButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  iconPosition?: 'start' | 'end';
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  arabicFont?: boolean;
}

export const ArabicButton: React.FC<ArabicButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'start',
  onClick,
  disabled = false,
  className = '',
  arabicFont = false
}) => {
  const { isRTL, language } = useLanguage();
  
  const baseClasses = `
    inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed
    ${isRTL ? 'font-arabic' : 'font-sans'}
    ${arabicFont && language === 'ar' ? 'font-arabic-display' : ''}
  `;
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };
  
  const iconSpacing = size === 'sm' ? 'gap-1.5' : size === 'md' ? 'gap-2' : 'gap-2.5';
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${icon ? iconSpacing : ''}
        ${className}
      `}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      {icon && iconPosition === 'start' && (
        <span className={isRTL ? 'order-2' : 'order-1'}>
          {icon}
        </span>
      )}
      <span className={isRTL && icon ? (iconPosition === 'start' ? 'order-1' : 'order-2') : ''}>
        {children}
      </span>
      {icon && iconPosition === 'end' && (
        <span className={isRTL ? 'order-1' : 'order-2'}>
          {icon}
        </span>
      )}
    </button>
  );
};

// Enhanced RTL-aware Card Component
interface ArabicCardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  headerAction?: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
  shadow?: 'sm' | 'md' | 'lg' | 'xl';
}

export const ArabicCard: React.FC<ArabicCardProps> = ({
  children,
  title,
  subtitle,
  headerAction,
  className = '',
  padding = 'md',
  shadow = 'md'
}) => {
  const { isRTL } = useLanguage();
  
  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };
  
  const shadowClasses = {
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl'
  };
  
  return (
    <div
      className={`
        bg-white rounded-lg border border-gray-200 ${shadowClasses[shadow]}
        ${paddingClasses[padding]} ${className}
      `}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      {(title || subtitle || headerAction) && (
        <div className={`mb-4 ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div>
              {title && (
                <h3 className="text-lg font-semibold text-gray-900 font-arabic">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1 font-arabic">
                  {subtitle}
                </p>
              )}
            </div>
            {headerAction && (
              <div className={isRTL ? 'mr-4' : 'ml-4'}>
                {headerAction}
              </div>
            )}
          </div>
        </div>
      )}
      {children}
    </div>
  );
};

// Enhanced RTL-aware Input Component
interface ArabicInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel';
  required?: boolean;
  disabled?: boolean;
  error?: string;
  helperText?: string;
  icon?: React.ReactNode;
  iconPosition?: 'start' | 'end';
  className?: string;
  arabicPlaceholder?: boolean;
}

export const ArabicInput: React.FC<ArabicInputProps> = ({
  label,
  placeholder,
  value,
  onChange,
  type = 'text',
  required = false,
  disabled = false,
  error,
  helperText,
  icon,
  iconPosition = 'start',
  className = '',
  arabicPlaceholder = false
}) => {
  const { isRTL, language } = useLanguage();
  
  const inputClasses = `
    w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
    focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
    ${icon ? (iconPosition === 'start' ? (isRTL ? 'pr-10' : 'pl-10') : (isRTL ? 'pl-10' : 'pr-10')) : ''}
    ${isRTL ? 'text-right font-arabic' : 'text-left'}
    ${arabicPlaceholder && language === 'ar' ? 'placeholder-arabic' : ''}
  `;
  
  return (
    <div className={`${className}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {label && (
        <label className={`block text-sm font-medium text-gray-700 mb-1 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <div className="relative">
        {icon && (
          <div className={`
            absolute inset-y-0 flex items-center pointer-events-none
            ${iconPosition === 'start' 
              ? (isRTL ? 'right-0 pr-3' : 'left-0 pl-3')
              : (isRTL ? 'left-0 pl-3' : 'right-0 pr-3')
            }
          `}>
            <span className="text-gray-400">
              {icon}
            </span>
          </div>
        )}
        <input
          type={type}
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className={inputClasses}
          dir={isRTL ? 'rtl' : 'ltr'}
        />
      </div>
      {error && (
        <p className={`mt-1 text-sm text-red-600 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
          {error}
        </p>
      )}
      {helperText && !error && (
        <p className={`mt-1 text-sm text-gray-500 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
          {helperText}
        </p>
      )}
    </div>
  );
};

// Enhanced RTL-aware Navigation Component
interface ArabicBreadcrumbProps {
  items: Array<{
    label: string;
    href?: string;
    current?: boolean;
  }>;
  separator?: React.ReactNode;
  className?: string;
}

export const ArabicBreadcrumb: React.FC<ArabicBreadcrumbProps> = ({
  items,
  separator,
  className = ''
}) => {
  const { isRTL } = useLanguage();
  
  const defaultSeparator = isRTL ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />;
  const separatorIcon = separator || defaultSeparator;
  
  return (
    <nav className={`flex ${isRTL ? 'flex-row-reverse' : ''} ${className}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <ol className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <span className={`text-gray-400 ${isRTL ? 'ml-2' : 'mr-2'}`}>
                {separatorIcon}
              </span>
            )}
            {item.href && !item.current ? (
              <a
                href={item.href}
                className="text-blue-600 hover:text-blue-800 font-medium font-arabic"
              >
                {item.label}
              </a>
            ) : (
              <span
                className={`font-arabic ${
                  item.current ? 'text-gray-900 font-semibold' : 'text-gray-500'
                }`}
              >
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

// Enhanced Arabic Typography System
interface ArabicTypographyProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'subtitle1' | 'subtitle2';
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'inherit';
  align?: 'auto' | 'left' | 'center' | 'right';
  className?: string;
  arabicOptimized?: boolean;
}

export const ArabicTypography: React.FC<ArabicTypographyProps> = ({
  children,
  variant = 'body1',
  weight = 'normal',
  color = 'inherit',
  align = 'auto',
  className = '',
  arabicOptimized = true
}) => {
  const { isRTL, language } = useLanguage();

  const variantClasses = {
    h1: 'text-4xl md:text-5xl lg:text-6xl leading-tight',
    h2: 'text-3xl md:text-4xl lg:text-5xl leading-tight',
    h3: 'text-2xl md:text-3xl lg:text-4xl leading-snug',
    h4: 'text-xl md:text-2xl lg:text-3xl leading-snug',
    h5: 'text-lg md:text-xl lg:text-2xl leading-normal',
    h6: 'text-base md:text-lg lg:text-xl leading-normal',
    body1: 'text-base leading-relaxed',
    body2: 'text-sm leading-relaxed',
    caption: 'text-xs leading-normal',
    subtitle1: 'text-lg leading-normal',
    subtitle2: 'text-base leading-normal'
  };

  const weightClasses = {
    light: 'font-light',
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold'
  };

  const colorClasses = {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    error: 'text-red-600',
    info: 'text-blue-500',
    inherit: 'text-inherit'
  };

  const alignClasses = {
    auto: isRTL ? 'text-right' : 'text-left',
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  const Component = variant.startsWith('h') ? variant as keyof JSX.IntrinsicElements : 'p';

  return (
    <Component
      className={`
        ${variantClasses[variant]}
        ${weightClasses[weight]}
        ${colorClasses[color]}
        ${alignClasses[align]}
        ${arabicOptimized && language === 'ar' ? 'font-arabic' : 'font-sans'}
        ${arabicOptimized && language === 'ar' && variant.startsWith('h') ? 'font-arabic-display' : ''}
        ${className}
      `}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      {children}
    </Component>
  );
};

// Enhanced RTL-aware Data Table Component
interface ArabicTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, record: any, index: number) => React.ReactNode;
  sortable?: boolean;
}

interface ArabicTableProps {
  columns: ArabicTableColumn[];
  data: any[];
  loading?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    onChange: (page: number, pageSize: number) => void;
  };
  className?: string;
  rowKey?: string | ((record: any) => string);
}

export const ArabicTable: React.FC<ArabicTableProps> = ({
  columns,
  data,
  loading = false,
  pagination,
  className = '',
  rowKey = 'id'
}) => {
  const { isRTL } = useLanguage();
  const [sortConfig, setSortConfig] = useState<{key: string, direction: 'asc' | 'desc'} | null>(null);

  const handleSort = (columnKey: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === columnKey && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key: columnKey, direction });
  };

  const sortedData = React.useMemo(() => {
    if (!sortConfig) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [data, sortConfig]);

  const getRowKey = (record: any, index: number): string => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return record[rowKey] || index.toString();
  };

  return (
    <div className={`overflow-hidden ${className}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`
                    px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider
                    ${column.align === 'center' ? 'text-center' :
                      column.align === 'right' ? 'text-right' :
                      isRTL ? 'text-right' : 'text-left'}
                    ${column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''}
                    font-arabic
                  `}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.dataIndex)}
                >
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span>{column.title}</span>
                    {column.sortable && (
                      <span className={`${isRTL ? 'mr-1' : 'ml-1'}`}>
                        {sortConfig?.key === column.dataIndex ? (
                          sortConfig.direction === 'asc' ? '↑' : '↓'
                        ) : '↕'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-4 text-center">
                  <div className="flex justify-center items-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span className={`${isRTL ? 'mr-2' : 'ml-2'} font-arabic`}>جاري التحميل...</span>
                  </div>
                </td>
              </tr>
            ) : sortedData.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-4 text-center text-gray-500 font-arabic">
                  لا توجد بيانات
                </td>
              </tr>
            ) : (
              sortedData.map((record, index) => (
                <tr key={getRowKey(record, index)} className="hover:bg-gray-50">
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={`
                        px-6 py-4 whitespace-nowrap text-sm text-gray-900
                        ${column.align === 'center' ? 'text-center' :
                          column.align === 'right' ? 'text-right' :
                          isRTL ? 'text-right' : 'text-left'}
                        font-arabic
                      `}
                    >
                      {column.render
                        ? column.render(record[column.dataIndex], record, index)
                        : record[column.dataIndex]
                      }
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {pagination && (
        <div className={`
          flex items-center justify-between px-6 py-3 bg-white border-t border-gray-200
          ${isRTL ? 'flex-row-reverse' : ''}
        `}>
          <div className="flex items-center text-sm text-gray-700 font-arabic">
            <span>
              عرض {((pagination.current - 1) * pagination.pageSize) + 1} إلى{' '}
              {Math.min(pagination.current * pagination.pageSize, pagination.total)} من{' '}
              {pagination.total} نتيجة
            </span>
          </div>
          <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
            <ArabicButton
              size="sm"
              variant="outline"
              disabled={pagination.current === 1}
              onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
            >
              {isRTL ? 'التالي' : 'السابق'}
            </ArabicButton>
            <span className="text-sm text-gray-700 font-arabic">
              صفحة {pagination.current} من {Math.ceil(pagination.total / pagination.pageSize)}
            </span>
            <ArabicButton
              size="sm"
              variant="outline"
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
              onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
            >
              {isRTL ? 'السابق' : 'التالي'}
            </ArabicButton>
          </div>
        </div>
      )}
    </div>
  );
};

// Cultural Calendar Component for MENA region
interface CulturalEvent {
  date: string;
  title: string;
  type: 'islamic' | 'national' | 'business';
  description?: string;
  impact?: 'low' | 'medium' | 'high';
}

interface ArabicCulturalCalendarProps {
  events: CulturalEvent[];
  onEventClick?: (event: CulturalEvent) => void;
  className?: string;
}

export const ArabicCulturalCalendar: React.FC<ArabicCulturalCalendarProps> = ({
  events,
  onEventClick,
  className = ''
}) => {
  const { isRTL } = useLanguage();
  const [selectedDate, setSelectedDate] = useState<string | null>(null);

  const eventTypeColors = {
    islamic: 'bg-green-100 text-green-800 border-green-200',
    national: 'bg-blue-100 text-blue-800 border-blue-200',
    business: 'bg-purple-100 text-purple-800 border-purple-200'
  };

  const impactIcons = {
    low: '○',
    medium: '◐',
    high: '●'
  };

  return (
    <div className={`${className}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="space-y-3">
        {events.map((event, index) => (
          <div
            key={index}
            className={`
              p-4 rounded-lg border cursor-pointer transition-all duration-200
              hover:shadow-md ${eventTypeColors[event.type]}
            `}
            onClick={() => {
              setSelectedDate(event.date);
              onEventClick?.(event);
            }}
          >
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="flex-1">
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} mb-1`}>
                  <Calendar className="w-4 h-4" />
                  <span className={`text-sm font-medium ${isRTL ? 'mr-2' : 'ml-2'} font-arabic`}>
                    {event.date}
                  </span>
                  {event.impact && (
                    <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm`}>
                      {impactIcons[event.impact]}
                    </span>
                  )}
                </div>
                <h4 className="font-semibold font-arabic">{event.title}</h4>
                {event.description && (
                  <p className="text-sm mt-1 font-arabic">{event.description}</p>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Enhanced RTL-aware Progress Component
interface ArabicProgressProps {
  value: number;
  max?: number;
  label?: string;
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'yellow' | 'red';
  className?: string;
}

export const ArabicProgress: React.FC<ArabicProgressProps> = ({
  value,
  max = 100,
  label,
  showPercentage = true,
  size = 'md',
  color = 'blue',
  className = ''
}) => {
  const { isRTL } = useLanguage();
  
  const percentage = Math.min((value / max) * 100, 100);
  
  const sizeClasses = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4'
  };
  
  const colorClasses = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    yellow: 'bg-yellow-600',
    red: 'bg-red-600'
  };
  
  return (
    <div className={`${className}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {(label || showPercentage) && (
        <div className={`flex justify-between items-center mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {label && (
            <span className="text-sm font-medium text-gray-700 font-arabic">
              {label}
            </span>
          )}
          {showPercentage && (
            <span className="text-sm text-gray-500 font-arabic">
              {Math.round(percentage)}%
            </span>
          )}
        </div>
      )}
      <div className={`w-full bg-gray-200 rounded-full ${sizeClasses[size]}`}>
        <div
          className={`${colorClasses[color]} ${sizeClasses[size]} rounded-full transition-all duration-300 ease-in-out`}
          style={{
            width: `${percentage}%`,
            transformOrigin: isRTL ? 'right' : 'left'
          }}
        />
      </div>
    </div>
  );
};

// Enhanced RTL-aware Alert Component
interface ArabicAlertProps {
  children: React.ReactNode;
  type?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

export const ArabicAlert: React.FC<ArabicAlertProps> = ({
  children,
  type = 'info',
  title,
  dismissible = false,
  onDismiss,
  className = ''
}) => {
  const { isRTL } = useLanguage();
  
  const typeClasses = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    success: 'bg-green-50 border-green-200 text-green-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    error: 'bg-red-50 border-red-200 text-red-800'
  };
  
  return (
    <div
      className={`
        border rounded-lg p-4 ${typeClasses[type]} ${className}
      `}
      dir={isRTL ? 'rtl' : 'ltr'}
    >
      <div className={`flex ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className="flex-1">
          {title && (
            <h4 className={`font-semibold mb-1 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
              {title}
            </h4>
          )}
          <div className={`font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
            {children}
          </div>
        </div>
        {dismissible && (
          <button
            onClick={onDismiss}
            className={`${isRTL ? 'mr-2' : 'ml-2'} text-current hover:opacity-75`}
          >
            ×
          </button>
        )}
      </div>
    </div>
  );
};
