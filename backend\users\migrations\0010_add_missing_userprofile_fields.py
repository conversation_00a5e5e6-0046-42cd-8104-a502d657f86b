# Generated manually to fix missing UserProfile fields
from django.db import migrations, models
import django.core.validators


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0009_roleapplication_company_name_and_more"),
    ]

    operations = [
        # Add missing fields to UserProfile
        migrations.AddField(
            model_name="userprofile",
            name="birth_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="phone_number",
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="website",
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="linkedin_url",
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="twitter_url",
            field=models.URL<PERSON>ield(blank=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="github_url",
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="job_title",
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="industry",
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="experience_years",
            field=models.PositiveIntegerField(
                blank=True, 
                null=True, 
                validators=[django.core.validators.MaxValueValidator(50)]
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="email_notifications",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="marketing_emails",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="profile_visibility",
            field=models.CharField(
                max_length=20,
                choices=[
                    ('public', 'Public'),
                    ('members', 'Members Only'),
                    ('private', 'Private'),
                ],
                default='members'
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="last_activity",
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
    ]
