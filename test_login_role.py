#!/usr/bin/env python3
"""
Test script to verify login response includes proper role information
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:8000"

def test_login_response(username, password):
    """Test login and check role information in response"""
    print(f"\n🔐 Testing login for: {username}")
    
    try:
        # Login request
        login_data = {
            "username": username,
            "password": password
        }
        
        response = requests.post(
            f"{BASE_URL}/api/auth/login/",
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            user_data = result.get('user', {})
            
            print(f"   ✅ Login successful!")
            print(f"   👤 User ID: {user_data.get('id')}")
            print(f"   📧 Email: {user_data.get('email')}")
            print(f"   🏷️  user_role: {user_data.get('user_role')}")
            print(f"   👑 is_staff: {user_data.get('is_staff')}")
            print(f"   🔑 is_superuser: {user_data.get('is_superuser')}")
            
            # Check profile information
            profile = user_data.get('profile', {})
            if profile:
                print(f"   📋 Profile found:")
                print(f"      active_roles: {profile.get('active_roles', [])}")
                print(f"      highest_permission_level: {profile.get('highest_permission_level')}")
            else:
                print(f"   ❌ No profile data found")
            
            return {
                'success': True,
                'user_role': user_data.get('user_role'),
                'is_staff': user_data.get('is_staff'),
                'is_superuser': user_data.get('is_superuser'),
                'active_roles': profile.get('active_roles', []) if profile else [],
                'has_profile': bool(profile)
            }
            
        else:
            print(f"   ❌ Login failed!")
            print(f"   Response: {response.text}")
            return {
                'success': False,
                'error': response.text
            }
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """Test login for different user types"""
    print("🚀 Testing Login Role Information")
    print("=" * 50)
    
    # Test with the users we created during registration testing
    test_users = [
        ("test_user_1753259713", "TestPassword123"),  # Regular user
        ("test_entrepreneur_1753259715", "TestPassword123"),  # Entrepreneur (pending approval)
        ("test_mentor_1753259717", "TestPassword123"),  # Mentor (pending approval)
        ("test_investor_1753259719", "TestPassword123"),  # Investor (pending approval)
    ]
    
    results = []
    
    for username, password in test_users:
        result = test_login_response(username, password)
        results.append({
            'username': username,
            'result': result
        })
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 LOGIN ROLE TEST SUMMARY")
    print("=" * 50)
    
    successful_logins = [r for r in results if r['result']['success']]
    failed_logins = [r for r in results if not r['result']['success']]
    
    print(f"Total Tests: {len(results)}")
    print(f"Successful Logins: {len(successful_logins)}")
    print(f"Failed Logins: {len(failed_logins)}")
    
    # Check if role information is properly included
    print("\n📋 ROLE INFORMATION ANALYSIS:")
    for result in results:
        username = result['username']
        login_result = result['result']
        
        if login_result['success']:
            user_role = login_result.get('user_role')
            has_profile = login_result.get('has_profile')
            active_roles = login_result.get('active_roles', [])
            
            status_icon = "✅" if user_role else "❌"
            print(f"   {status_icon} {username}:")
            print(f"      user_role: {user_role}")
            print(f"      has_profile: {has_profile}")
            print(f"      active_roles: {len(active_roles)} roles")
            
            if not user_role:
                print(f"      ⚠️  ISSUE: Missing user_role field!")
        else:
            print(f"   ❌ {username}: LOGIN FAILED - {login_result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
