/**
 * Error handling hooks for React components
 * Provides consistent error handling patterns across the application
 */

import { useState, useCallback, useEffect } from 'react';
import { Error<PERSON>and<PERSON>, AppError } from '../utils/errorHandling';

// Error state interface
export interface ErrorState {
  error: AppError | null;
  isError: boolean;
  errorMessage: string | null;
  errorCode: string | null;
  retryCount: number;
}

// Initial error state
const initialErrorState: ErrorState = {
  error: null,
  isError: false,
  errorMessage: null,
  errorCode: null,
  retryCount: 0
};

/**
 * Main error handling hook
 */
export function useErrorHandling(context?: string) {
  const [errorState, setErrorState] = useState<ErrorState>(initialErrorState);

  // Handle error function
  const handleError = useCallback((error: any, customContext?: string) => {
    const appError = ErrorHandler.createAppError(error, customContext || context);
    ErrorHandler.logError(appError, customContext || context);

    setErrorState({
      error: appError,
      isError: true,
      errorMessage: appError.message,
      errorCode: appError.code,
      retryCount: 0
    });

    // Dispatch custom event for global error handling
    window.dispatchEvent(new CustomEvent('app-error', {
      detail: { error: appError, context: customContext || context }
    }));

    return appError;
  }, [context]);

  // Clear error function
  const clearError = useCallback(() => {
    setErrorState(initialErrorState);
  }, []);

  // Retry function
  const retry = useCallback(() => {
    setErrorState(prev => ({
      ...prev,
      retryCount: prev.retryCount + 1
    }));
  }, []);

  // Async error handler
  const handleAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    fallback?: T,
    customContext?: string
  ) => {
    try {
      clearError();
      return await operation();
    } catch (error) {
      const appError = handleError(error, customContext);
      return fallback !== undefined ? fallback : Promise.reject(appError);
    }
  }, [handleError, clearError]);

  return {
    ...errorState,
    handleError,
    clearError,
    retry,
    handleAsync
  };
}

/**
 * Hook for API error handling
 */
export function useAPIErrorHandling() {
  const { handleError, clearError, ...errorState } = useErrorHandling('API');

  const handleAPIError = useCallback((error: any, endpoint?: string) => {
    // Enhanced API error handling
    let errorMessage = 'An unexpected error occurred';
    let errorCode = 'UNKNOWN_ERROR';

    if (error?.response) {
      // HTTP error response
      const status = error.response.status;
      const data = error.response.data;

      switch (status) {
        case 400:
          errorMessage = data?.message || 'Invalid request';
          errorCode = 'BAD_REQUEST';
          break;
        case 401:
          errorMessage = 'Authentication required';
          errorCode = 'UNAUTHORIZED';
          break;
        case 403:
          errorMessage = 'Access denied';
          errorCode = 'FORBIDDEN';
          break;
        case 404:
          errorMessage = 'Resource not found';
          errorCode = 'NOT_FOUND';
          break;
        case 422:
          errorMessage = data?.message || 'Validation error';
          errorCode = 'VALIDATION_ERROR';
          break;
        case 429:
          errorMessage = 'Too many requests. Please try again later.';
          errorCode = 'RATE_LIMITED';
          break;
        case 500:
          errorMessage = 'Server error. Please try again later.';
          errorCode = 'SERVER_ERROR';
          break;
        default:
          errorMessage = data?.message || `HTTP ${status} error`;
          errorCode = `HTTP_${status}`;
      }
    } else if (error?.code === 'NETWORK_ERROR') {
      errorMessage = 'Network error. Please check your connection.';
      errorCode = 'NETWORK_ERROR';
    } else if (error?.message) {
      errorMessage = error.message;
    }

    const enhancedError = {
      ...error,
      message: errorMessage,
      code: errorCode,
      endpoint
    };

    return handleError(enhancedError, `API:${endpoint || 'unknown'}`);
  }, [handleError]);

  return {
    ...errorState,
    handleAPIError,
    clearError
  };
}

/**
 * Hook for form error handling
 */
export function useFormErrorHandling() {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const { handleError, clearError, ...errorState } = useErrorHandling('Form');

  const setFieldError = useCallback((field: string, message: string) => {
    setFieldErrors(prev => ({
      ...prev,
      [field]: message
    }));
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllFieldErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  const handleValidationError = useCallback((error: any) => {
    if (error?.response?.data?.errors) {
      // Handle validation errors from API
      const errors = error.response.data.errors;
      const newFieldErrors: Record<string, string> = {};

      Object.entries(errors).forEach(([field, messages]) => {
        if (Array.isArray(messages)) {
          newFieldErrors[field] = messages[0];
        } else {
          newFieldErrors[field] = String(messages);
        }
      });

      setFieldErrors(newFieldErrors);
    } else {
      handleError(error);
    }
  }, [handleError]);

  const clearAllErrors = useCallback(() => {
    clearError();
    clearAllFieldErrors();
  }, [clearError, clearAllFieldErrors]);

  return {
    ...errorState,
    fieldErrors,
    setFieldError,
    clearFieldError,
    clearAllFieldErrors,
    handleValidationError,
    clearAllErrors,
    hasFieldErrors: Object.keys(fieldErrors).length > 0
  };
}

/**
 * Hook for component error recovery
 */
export function useErrorRecovery(maxRetries: number = 3) {
  const [retryCount, setRetryCount] = useState(0);
  const [isRecovering, setIsRecovering] = useState(false);

  const canRetry = retryCount < maxRetries;

  const attemptRecovery = useCallback(async (recoveryFn: () => Promise<void>) => {
    if (!canRetry) return false;

    setIsRecovering(true);
    try {
      await recoveryFn();
      setRetryCount(0); // Reset on successful recovery
      return true;
    } catch (error) {
      setRetryCount(prev => prev + 1);
      return false;
    } finally {
      setIsRecovering(false);
    }
  }, [canRetry]);

  const resetRetryCount = useCallback(() => {
    setRetryCount(0);
  }, []);

  return {
    retryCount,
    canRetry,
    isRecovering,
    attemptRecovery,
    resetRetryCount
  };
}

/**
 * Global error event listener hook
 */
export function useGlobalErrorListener() {
  useEffect(() => {
    const handleGlobalError = (event: CustomEvent) => {
      const { error, context } = event.detail;
      console.error(`Global error in ${context}:`, error);
      
      // Here you could integrate with external error tracking
      // Example: Sentry.captureException(error);
    };

    window.addEventListener('app-error', handleGlobalError as EventListener);
    
    return () => {
      window.removeEventListener('app-error', handleGlobalError as EventListener);
    };
  }, []);
}
