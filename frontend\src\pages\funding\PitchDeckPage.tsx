/**
 * Pitch Deck Management Page
 * Create, edit, and manage pitch decks with AI assistance and templates
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicProgress } from '../../components/ui/ArabicOptimizedComponents';
import { 
  FileText, 
  Edit, 
  Save, 
  Download, 
  Share2, 
  Bot, 
  Eye,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  TrendingUp,
  DollarSign,
  Target,
  Presentation,
  ArrowLeft,
  Upload,
  Copy
} from 'lucide-react';

interface PitchDeck {
  id: string;
  title: string;
  description: string;
  industry: string;
  stage: string;
  lastModified: string;
  completionPercentage: number;
  slides: PitchSlide[];
  collaborators: Array<{
    id: string;
    name: string;
    role: string;
    avatar?: string;
  }>;
  status: 'draft' | 'in_review' | 'approved' | 'needs_revision';
  template: string;
  version: number;
}

interface PitchSlide {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  content: string;
  completed: boolean;
  aiAssisted: boolean;
  lastModified: string;
  order: number;
  slideType: 'title' | 'problem' | 'solution' | 'market' | 'business_model' | 'traction' | 'team' | 'financials' | 'funding' | 'appendix';
  tips: string[];
  tipsAr: string[];
}

const PitchDeckPage: React.FC = () => {
  const { deckId } = useParams();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [pitchDeck, setPitchDeck] = useState<PitchDeck | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeSlide, setActiveSlide] = useState<string>('title');
  const [isEditing, setIsEditing] = useState(false);
  const [aiGenerating, setAiGenerating] = useState(false);

  useEffect(() => {
    loadPitchDeck();
  }, [deckId]);

  const loadPitchDeck = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API call
      const mockDeck: PitchDeck = {
        id: deckId || 'deck-**********',
        title: 'FinTech Revolution - Digital Banking Platform',
        description: 'Revolutionary digital banking platform for MENA region',
        industry: 'FinTech',
        stage: 'Series A',
        lastModified: '2024-01-22T10:30:00Z',
        completionPercentage: 75,
        status: 'draft',
        template: 'Standard VC Pitch',
        version: 3,
        collaborators: [
          {
            id: '1',
            name: 'Ahmed Al-Rashid',
            role: 'Founder & CEO'
          },
          {
            id: '2',
            name: 'Sarah Johnson',
            role: 'Co-Founder & CTO'
          }
        ],
        slides: [
          {
            id: 'title',
            title: 'Title Slide',
            titleAr: 'شريحة العنوان',
            description: 'Company name, tagline, and presenter information',
            descriptionAr: 'اسم الشركة والشعار ومعلومات المقدم',
            content: 'FinTech Revolution\nDigital Banking for Everyone\nAhmed Al-Rashid, CEO\<EMAIL>',
            completed: true,
            aiAssisted: false,
            lastModified: '2024-01-22T10:30:00Z',
            order: 1,
            slideType: 'title',
            tips: ['Keep it simple and memorable', 'Include contact information', 'Use high-quality logo'],
            tipsAr: ['اجعلها بسيطة ولا تُنسى', 'أدرج معلومات الاتصال', 'استخدم شعاراً عالي الجودة']
          },
          {
            id: 'problem',
            title: 'Problem Statement',
            titleAr: 'بيان المشكلة',
            description: 'The problem you are solving and its significance',
            descriptionAr: 'المشكلة التي تحلها وأهميتها',
            content: 'Traditional banking in MENA is outdated:\n• 60% of population is unbanked\n• Complex processes and paperwork\n• Limited digital services\n• High fees and poor customer experience',
            completed: true,
            aiAssisted: true,
            lastModified: '2024-01-21T15:20:00Z',
            order: 2,
            slideType: 'problem',
            tips: ['Use compelling statistics', 'Make it relatable', 'Show the pain clearly'],
            tipsAr: ['استخدم إحصائيات مقنعة', 'اجعلها قابلة للفهم', 'أظهر المعاناة بوضوح']
          },
          {
            id: 'solution',
            title: 'Solution',
            titleAr: 'الحل',
            description: 'Your unique solution to the problem',
            descriptionAr: 'حلك الفريد للمشكلة',
            content: 'AI-powered digital banking platform:\n• Instant account opening via mobile\n• Islamic finance compliant\n• Multi-language support (Arabic/English)\n• Advanced fraud detection\n• Micro-lending for SMEs',
            completed: true,
            aiAssisted: true,
            lastModified: '2024-01-20T09:15:00Z',
            order: 3,
            slideType: 'solution',
            tips: ['Focus on unique value proposition', 'Show how it solves the problem', 'Keep it concise'],
            tipsAr: ['ركز على عرض القيمة الفريد', 'أظهر كيف يحل المشكلة', 'اجعله مختصراً']
          },
          {
            id: 'market',
            title: 'Market Opportunity',
            titleAr: 'فرصة السوق',
            description: 'Market size, growth, and opportunity',
            descriptionAr: 'حجم السوق والنمو والفرصة',
            content: '',
            completed: false,
            aiAssisted: false,
            lastModified: '',
            order: 4,
            slideType: 'market',
            tips: ['Use TAM, SAM, SOM framework', 'Show market growth trends', 'Include competitive landscape'],
            tipsAr: ['استخدم إطار TAM, SAM, SOM', 'أظهر اتجاهات نمو السوق', 'أدرج المشهد التنافسي']
          },
          {
            id: 'traction',
            title: 'Traction & Metrics',
            titleAr: 'الجذب والمقاييس',
            description: 'Key metrics and growth indicators',
            descriptionAr: 'المقاييس الرئيسية ومؤشرات النمو',
            content: '',
            completed: false,
            aiAssisted: false,
            lastModified: '',
            order: 5,
            slideType: 'traction',
            tips: ['Show key metrics that matter', 'Demonstrate growth trajectory', 'Include user testimonials'],
            tipsAr: ['أظهر المقاييس الرئيسية المهمة', 'أظهر مسار النمو', 'أدرج شهادات المستخدمين']
          }
        ]
      };
      
      setPitchDeck(mockDeck);
    } catch (error) {
      console.error('Error loading pitch deck:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateWithAI = async (slideId: string) => {
    setAiGenerating(true);
    try {
      // Mock AI generation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Update slide with AI-generated content
      if (pitchDeck) {
        const updatedSlides = pitchDeck.slides.map(slide => {
          if (slide.id === slideId) {
            return {
              ...slide,
              content: `AI-generated content for ${slide.title}...\n• Key point 1\n• Key point 2\n• Key point 3`,
              completed: true,
              aiAssisted: true,
              lastModified: new Date().toISOString()
            };
          }
          return slide;
        });
        
        setPitchDeck({
          ...pitchDeck,
          slides: updatedSlides,
          completionPercentage: Math.round((updatedSlides.filter(s => s.completed).length / updatedSlides.length) * 100)
        });
      }
    } catch (error) {
      console.error('Error generating AI content:', error);
    } finally {
      setAiGenerating(false);
    }
  };

  const handleSave = async () => {
    try {
      // Save pitch deck - replace with actual API call
      console.log('Saving pitch deck:', pitchDeck);
      
      // Mock save
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving pitch deck:', error);
    }
  };

  const handleExport = () => {
    // Export pitch deck as PDF/PowerPoint
    console.log('Exporting pitch deck');
  };

  const handleShare = () => {
    // Share pitch deck with investors
    console.log('Sharing pitch deck');
  };

  const getSlideIcon = (slideType: string) => {
    switch (slideType) {
      case 'title':
        return <Presentation className="w-5 h-5" />;
      case 'problem':
        return <AlertCircle className="w-5 h-5" />;
      case 'solution':
        return <CheckCircle className="w-5 h-5" />;
      case 'market':
        return <TrendingUp className="w-5 h-5" />;
      case 'traction':
        return <Target className="w-5 h-5" />;
      case 'team':
        return <Users className="w-5 h-5" />;
      case 'financials':
        return <DollarSign className="w-5 h-5" />;
      default:
        return <FileText className="w-5 h-5" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (!pitchDeck) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
            {language === 'ar' ? 'لم يتم العثور على العرض التقديمي' : 'Pitch Deck Not Found'}
          </ArabicTypography>
          <ArabicButton onClick={() => navigate('/dashboard/funding/pitch-decks')}>
            {language === 'ar' ? 'العودة للعروض التقديمية' : 'Back to Pitch Decks'}
          </ArabicButton>
        </div>
      </div>
    );
  }

  const activeSlideData = pitchDeck.slides.find(s => s.id === activeSlide);

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <ArabicButton
                variant="ghost"
                onClick={() => navigate(-1)}
                icon={<ArrowLeft className="w-4 h-4" />}
                className={isRTL ? 'ml-4' : 'mr-4'}
              >
                {language === 'ar' ? 'رجوع' : 'Back'}
              </ArabicButton>
              <div>
                <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                  {pitchDeck.title}
                </ArabicTypography>
                <div className={`flex items-center mt-2 space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold font-arabic">
                    {pitchDeck.template}
                  </span>
                  <ArabicTypography variant="body2" color="secondary">
                    {language === 'ar' ? 'الإصدار' : 'Version'} {pitchDeck.version} • {language === 'ar' ? 'آخر تعديل:' : 'Last modified:'} {new Date(pitchDeck.lastModified).toLocaleDateString()}
                  </ArabicTypography>
                </div>
              </div>
            </div>
            
            <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <ArabicButton
                variant="outline"
                onClick={handleShare}
                icon={<Share2 className="w-4 h-4" />}
              >
                {language === 'ar' ? 'مشاركة' : 'Share'}
              </ArabicButton>
              
              <ArabicButton
                variant="outline"
                onClick={handleExport}
                icon={<Download className="w-4 h-4" />}
              >
                {language === 'ar' ? 'تصدير' : 'Export'}
              </ArabicButton>
              
              <ArabicButton
                onClick={isEditing ? handleSave : () => setIsEditing(true)}
                icon={isEditing ? <Save className="w-4 h-4" /> : <Edit className="w-4 h-4" />}
              >
                {isEditing ? 
                  (language === 'ar' ? 'حفظ' : 'Save') : 
                  (language === 'ar' ? 'تعديل' : 'Edit')
                }
              </ArabicButton>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Slides Navigation */}
          <div className="lg:col-span-1">
            <ArabicCard>
              <div className="mb-6">
                <ArabicTypography variant="h4" className="text-gray-900 font-bold mb-2">
                  {language === 'ar' ? 'التقدم' : 'Progress'}
                </ArabicTypography>
                <ArabicProgress
                  value={pitchDeck.completionPercentage}
                  showPercentage={true}
                  size="md"
                  color="blue"
                />
              </div>

              <div className="space-y-2">
                <ArabicTypography variant="h5" className="text-gray-900 font-semibold mb-4">
                  {language === 'ar' ? 'شرائح العرض' : 'Pitch Slides'}
                </ArabicTypography>
                
                {pitchDeck.slides
                  .sort((a, b) => a.order - b.order)
                  .map((slide) => (
                    <button
                      key={slide.id}
                      onClick={() => setActiveSlide(slide.id)}
                      className={`w-full text-left p-3 rounded-lg transition-colors ${
                        activeSlide === slide.id 
                          ? 'bg-blue-50 border-blue-200 border' 
                          : 'hover:bg-gray-50'
                      } ${isRTL ? 'text-right' : 'text-left'}`}
                    >
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`text-blue-600 ${isRTL ? 'ml-3' : 'mr-3'}`}>
                          {getSlideIcon(slide.slideType)}
                        </div>
                        <div className="flex-1">
                          <ArabicTypography variant="body2" className="font-semibold">
                            {language === 'ar' ? slide.titleAr : slide.title}
                          </ArabicTypography>
                          <div className={`flex items-center mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                            {slide.completed ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <Clock className="w-4 h-4 text-gray-400" />
                            )}
                            {slide.aiAssisted && (
                              <Bot className={`w-4 h-4 text-purple-500 ${isRTL ? 'mr-1' : 'ml-1'}`} />
                            )}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
              </div>

              {/* Collaborators */}
              <div className="mt-8">
                <ArabicTypography variant="h5" className="text-gray-900 font-semibold mb-4">
                  {language === 'ar' ? 'المتعاونون' : 'Collaborators'}
                </ArabicTypography>
                <div className="space-y-2">
                  {pitchDeck.collaborators.map((collaborator) => (
                    <div key={collaborator.id} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'}`}>
                        <span className="text-sm font-semibold text-blue-600">
                          {collaborator.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <ArabicTypography variant="body2" className="font-semibold">
                          {collaborator.name}
                        </ArabicTypography>
                        <ArabicTypography variant="caption" color="secondary">
                          {collaborator.role}
                        </ArabicTypography>
                      </div>
                    </div>
                  ))}
                  <ArabicButton
                    variant="ghost"
                    size="sm"
                    onClick={() => {/* Add collaborator */}}
                    icon={<Plus className="w-4 h-4" />}
                    className="w-full mt-2"
                  >
                    {language === 'ar' ? 'إضافة متعاون' : 'Add Collaborator'}
                  </ArabicButton>
                </div>
              </div>
            </ArabicCard>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeSlideData && (
              <ArabicCard>
                <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                      {language === 'ar' ? activeSlideData.titleAr : activeSlideData.title}
                    </ArabicTypography>
                    <ArabicTypography variant="body1" color="secondary">
                      {language === 'ar' ? activeSlideData.descriptionAr : activeSlideData.description}
                    </ArabicTypography>
                  </div>
                  
                  {!activeSlideData.completed && (
                    <ArabicButton
                      onClick={() => handleGenerateWithAI(activeSlideData.id)}
                      disabled={aiGenerating}
                      icon={<Bot className="w-4 h-4" />}
                      variant="outline"
                    >
                      {aiGenerating 
                        ? (language === 'ar' ? 'جاري الإنشاء...' : 'Generating...')
                        : (language === 'ar' ? 'إنشاء بالذكاء الاصطناعي' : 'Generate with AI')
                      }
                    </ArabicButton>
                  )}
                </div>

                {/* Content Editor */}
                <div className="min-h-96 mb-6">
                  {isEditing ? (
                    <textarea
                      className="w-full h-96 p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                      value={activeSlideData.content}
                      onChange={(e) => {
                        // Update slide content
                        const updatedSlides = pitchDeck.slides.map(slide => {
                          if (slide.id === activeSlideData.id) {
                            return { ...slide, content: e.target.value };
                          }
                          return slide;
                        });
                        setPitchDeck({ ...pitchDeck, slides: updatedSlides });
                      }}
                      placeholder={language === 'ar' ? 'اكتب محتوى هذه الشريحة...' : 'Write the content for this slide...'}
                      dir={isRTL ? 'rtl' : 'ltr'}
                    />
                  ) : (
                    <div className="prose max-w-none font-arabic" dir={isRTL ? 'rtl' : 'ltr'}>
                      {activeSlideData.content ? (
                        <div className="whitespace-pre-wrap bg-gray-50 p-6 rounded-lg border">
                          {activeSlideData.content}
                        </div>
                      ) : (
                        <div className="text-center py-12 text-gray-500">
                          <Presentation className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                          <ArabicTypography variant="body1">
                            {language === 'ar' 
                              ? 'لم يتم إنشاء محتوى لهذه الشريحة بعد'
                              : 'No content has been created for this slide yet'
                            }
                          </ArabicTypography>
                          <ArabicButton
                            className="mt-4"
                            onClick={() => handleGenerateWithAI(activeSlideData.id)}
                            icon={<Bot className="w-4 h-4" />}
                          >
                            {language === 'ar' ? 'إنشاء بالذكاء الاصطناعي' : 'Generate with AI'}
                          </ArabicButton>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Tips */}
                <ArabicCard className="bg-blue-50 border-blue-200">
                  <ArabicTypography variant="h5" className="text-blue-900 font-semibold mb-3">
                    {language === 'ar' ? 'نصائح لهذه الشريحة' : 'Tips for this slide'}
                  </ArabicTypography>
                  <div className="space-y-2">
                    {(language === 'ar' ? activeSlideData.tipsAr : activeSlideData.tips).map((tip, index) => (
                      <div key={index} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`w-2 h-2 bg-blue-500 rounded-full mt-2 ${isRTL ? 'ml-3' : 'mr-3'}`}></div>
                        <ArabicTypography variant="body2" className="text-blue-800">
                          {tip}
                        </ArabicTypography>
                      </div>
                    ))}
                  </div>
                </ArabicCard>

                {/* Slide Footer */}
                {activeSlideData.lastModified && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className={`flex items-center justify-between text-sm text-gray-500 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Clock className={`w-4 h-4 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <span className="font-arabic">
                          {language === 'ar' ? 'آخر تعديل:' : 'Last modified:'} {new Date(activeSlideData.lastModified).toLocaleString()}
                        </span>
                      </div>
                      {activeSlideData.aiAssisted && (
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <Bot className={`w-4 h-4 text-purple-500 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                          <span className="font-arabic">
                            {language === 'ar' ? 'تم إنشاؤه بالذكاء الاصطناعي' : 'AI Generated'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </ArabicCard>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PitchDeckPage;
