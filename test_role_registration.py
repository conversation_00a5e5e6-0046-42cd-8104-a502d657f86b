#!/usr/bin/env python3
"""
Test script to verify role-based registration and approval logic
Tests all user roles to ensure proper approval requirements
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8000"

def test_user_registration(role, username_suffix=""):
    """Test registration for a specific role"""
    timestamp = int(time.time())
    username = f"test_{role}_{timestamp}{username_suffix}"
    email = f"test_{role}_{timestamp}@example.com"
    
    registration_data = {
        "username": username,
        "first_name": "Test",
        "last_name": f"{role.title()}User",
        "email": email,
        "password": "TestPassword123",
        "password_confirm": "TestPassword123",
        "phone": "+1234567890",
        "location": "Test City",
        "bio": f"Test {role} user",
        "company": "Test Company",
        "job_title": f"Test {role.title()}",
        "language": "en",
        "selected_role": role,
        # Role-specific fields
        "business_name": "Test Business" if role == "entrepreneur" else "",
        "business_stage": "idea" if role == "entrepreneur" else "",
        "industry": "Technology" if role == "entrepreneur" else "",
        "expertise": "Business Strategy" if role == "mentor" else "",
        "experience": "5-10" if role == "mentor" else "",
        "investment_range": "10k-50k" if role == "investor" else "",
        "investment_stage": "seed" if role == "investor" else "",
        "interests": "Learning and networking" if role == "user" else "",
        "goals": "Professional development" if role == "user" else ""
    }
    
    print(f"\n🧪 Testing {role.upper()} registration...")
    print(f"   Username: {username}")
    print(f"   Email: {email}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/register-enhanced/",
            json=registration_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            requires_approval = result.get('requires_approval', True)
            status = result.get('status', 'unknown')
            message = result.get('message', 'No message')
            user_data = result.get('user', {})
            is_active = user_data.get('is_active', False)
            
            print(f"   ✅ Registration successful!")
            print(f"   📋 Status: {status}")
            print(f"   🔒 Requires Approval: {requires_approval}")
            print(f"   👤 User Active: {is_active}")
            print(f"   💬 Message: {message}")
            
            # Verify expected behavior based on role
            if role == 'user':
                expected_approval = False
                expected_active = True
                expected_status = 'active'
            else:
                expected_approval = True
                expected_active = False
                expected_status = 'pending_approval'
            
            # Check if behavior matches expectations
            approval_correct = requires_approval == expected_approval
            active_correct = is_active == expected_active
            status_correct = status == expected_status
            
            if approval_correct and active_correct and status_correct:
                print(f"   ✅ PASS: Role behavior is correct!")
            else:
                print(f"   ❌ FAIL: Role behavior is incorrect!")
                print(f"      Expected - Approval: {expected_approval}, Active: {expected_active}, Status: {expected_status}")
                print(f"      Actual   - Approval: {requires_approval}, Active: {is_active}, Status: {status}")
            
            return {
                'success': True,
                'role': role,
                'username': username,
                'requires_approval': requires_approval,
                'is_active': is_active,
                'status': status,
                'behavior_correct': approval_correct and active_correct and status_correct
            }
            
        else:
            print(f"   ❌ Registration failed!")
            print(f"   Response: {response.text}")
            return {
                'success': False,
                'role': role,
                'error': response.text
            }
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {
            'success': False,
            'role': role,
            'error': str(e)
        }

def main():
    """Test all user roles"""
    print("🚀 Starting Role-Based Registration Tests")
    print("=" * 50)
    
    # Test all roles
    roles_to_test = ['user', 'entrepreneur', 'mentor', 'investor']
    results = []
    
    for role in roles_to_test:
        result = test_user_registration(role)
        results.append(result)
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    correct_behavior = [r for r in successful_tests if r.get('behavior_correct', False)]
    
    print(f"Total Tests: {len(results)}")
    print(f"Successful Registrations: {len(successful_tests)}")
    print(f"Failed Registrations: {len(failed_tests)}")
    print(f"Correct Role Behavior: {len(correct_behavior)}")
    
    if len(correct_behavior) == len(roles_to_test):
        print("\n🎉 ALL TESTS PASSED! Role-based approval logic is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
    
    # Detailed results
    print("\n📋 DETAILED RESULTS:")
    for result in results:
        if result['success']:
            status_icon = "✅" if result.get('behavior_correct', False) else "❌"
            print(f"   {status_icon} {result['role'].upper()}: "
                  f"Approval={result['requires_approval']}, "
                  f"Active={result['is_active']}, "
                  f"Status={result['status']}")
        else:
            print(f"   ❌ {result['role'].upper()}: FAILED - {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
