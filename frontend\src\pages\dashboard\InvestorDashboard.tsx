/**
 * Investor Dashboard
 * Comprehensive dashboard for investors with deal flow and portfolio management
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicTable } from '../../components/ui/ArabicOptimizedComponents';
import { 
  DollarSign, 
  TrendingUp, 
  Briefcase, 
  Eye, 
  Star,
  Calendar,
  FileText,
  Target,
  PieChart,
  BarChart3,
  Filter,
  Search,
  Plus
} from 'lucide-react';

interface InvestorStats {
  totalInvestments: number;
  portfolioValue: number;
  activeDeals: number;
  successfulExits: number;
  averageROI: number;
  dealsPipeline: number;
}

interface Investment {
  id: string;
  company: string;
  industry: string;
  stage: string;
  amount: number;
  date: string;
  valuation: number;
  ownership: number;
  status: 'active' | 'exited' | 'written_off';
  currentValue: number;
  roi: number;
}

interface DealOpportunity {
  id: string;
  company: string;
  industry: string;
  stage: string;
  fundingRound: string;
  amountSeeking: number;
  valuation: number;
  description: string;
  submittedDate: string;
  status: 'new' | 'reviewing' | 'due_diligence' | 'term_sheet' | 'closed';
  matchScore: number;
}

const InvestorDashboard: React.FC = () => {
  const { user } = useAuth();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [stats, setStats] = useState<InvestorStats | null>(null);
  const [portfolio, setPortfolio] = useState<Investment[]>([]);
  const [dealFlow, setDealFlow] = useState<DealOpportunity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockStats: InvestorStats = {
        totalInvestments: 15,
        portfolioValue: 12500000,
        activeDeals: 3,
        successfulExits: 4,
        averageROI: 3.2,
        dealsPipeline: 8
      };

      const mockPortfolio: Investment[] = [
        {
          id: '1',
          company: 'FinTech Solutions',
          industry: 'FinTech',
          stage: 'Series A',
          amount: 500000,
          date: '2023-06-15',
          valuation: 5000000,
          ownership: 10,
          status: 'active',
          currentValue: 750000,
          roi: 1.5
        },
        {
          id: '2',
          company: 'HealthTech Innovations',
          industry: 'HealthTech',
          stage: 'Seed',
          amount: 250000,
          date: '2023-08-20',
          valuation: 2500000,
          ownership: 10,
          status: 'active',
          currentValue: 400000,
          roi: 1.6
        },
        {
          id: '3',
          company: 'EduPlatform',
          industry: 'EdTech',
          stage: 'Pre-Seed',
          amount: 100000,
          date: '2023-10-10',
          valuation: 1000000,
          ownership: 10,
          status: 'active',
          currentValue: 150000,
          roi: 1.5
        }
      ];

      const mockDealFlow: DealOpportunity[] = [
        {
          id: '1',
          company: 'AI Logistics',
          industry: 'Logistics',
          stage: 'Series A',
          fundingRound: 'Series A',
          amountSeeking: 2000000,
          valuation: 15000000,
          description: 'AI-powered logistics optimization platform for MENA region',
          submittedDate: '2024-01-20',
          status: 'new',
          matchScore: 92
        },
        {
          id: '2',
          company: 'Green Energy Solutions',
          industry: 'CleanTech',
          stage: 'Seed',
          fundingRound: 'Seed',
          amountSeeking: 1000000,
          valuation: 8000000,
          description: 'Renewable energy solutions for commercial buildings',
          submittedDate: '2024-01-18',
          status: 'reviewing',
          matchScore: 85
        },
        {
          id: '3',
          company: 'Digital Healthcare',
          industry: 'HealthTech',
          stage: 'Pre-Seed',
          fundingRound: 'Pre-Seed',
          amountSeeking: 500000,
          valuation: 3000000,
          description: 'Telemedicine platform for rural areas',
          submittedDate: '2024-01-15',
          status: 'due_diligence',
          matchScore: 78
        }
      ];

      setStats(mockStats);
      setPortfolio(mockPortfolio);
      setDealFlow(mockDealFlow);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number): string => {
    if (language === 'ar') {
      return `${(amount / 1000000).toFixed(1)} مليون ريال`;
    }
    return `$${(amount / 1000000).toFixed(1)}M`;
  };

  const portfolioColumns = [
    {
      key: 'company',
      title: language === 'ar' ? 'الشركة' : 'Company',
      dataIndex: 'company',
      render: (value: string, record: Investment) => (
        <div>
          <div className="font-semibold font-arabic">{value}</div>
          <div className="text-sm text-gray-500 font-arabic">{record.industry}</div>
        </div>
      )
    },
    {
      key: 'investment',
      title: language === 'ar' ? 'الاستثمار' : 'Investment',
      dataIndex: 'amount',
      render: (value: number, record: Investment) => (
        <div>
          <div className="font-semibold font-arabic">{formatCurrency(value)}</div>
          <div className="text-sm text-gray-500 font-arabic">{record.ownership}% ownership</div>
        </div>
      )
    },
    {
      key: 'currentValue',
      title: language === 'ar' ? 'القيمة الحالية' : 'Current Value',
      dataIndex: 'currentValue',
      render: (value: number, record: Investment) => (
        <div>
          <div className="font-semibold font-arabic">{formatCurrency(value)}</div>
          <div className={`text-sm ${record.roi > 1 ? 'text-green-600' : 'text-red-600'} font-arabic`}>
            {record.roi > 1 ? '+' : ''}{((record.roi - 1) * 100).toFixed(1)}%
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: language === 'ar' ? 'الحالة' : 'Status',
      dataIndex: 'status',
      render: (value: string) => (
        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
          value === 'active' ? 'bg-green-100 text-green-800' :
          value === 'exited' ? 'bg-blue-100 text-blue-800' :
          'bg-red-100 text-red-800'
        } font-arabic`}>
          {value === 'active' ? (language === 'ar' ? 'نشط' : 'Active') :
           value === 'exited' ? (language === 'ar' ? 'خرج' : 'Exited') :
           (language === 'ar' ? 'شطب' : 'Written Off')}
        </span>
      )
    },
    {
      key: 'actions',
      title: language === 'ar' ? 'الإجراءات' : 'Actions',
      dataIndex: 'id',
      render: (id: string) => (
        <ArabicButton
          size="sm"
          variant="outline"
          onClick={() => navigate(`/dashboard/portfolio/${id}`)}
        >
          {language === 'ar' ? 'عرض' : 'View'}
        </ArabicButton>
      )
    }
  ];

  const dealFlowColumns = [
    {
      key: 'company',
      title: language === 'ar' ? 'الشركة' : 'Company',
      dataIndex: 'company',
      render: (value: string, record: DealOpportunity) => (
        <div>
          <div className="font-semibold font-arabic">{value}</div>
          <div className="text-sm text-gray-500 font-arabic">{record.industry} • {record.stage}</div>
        </div>
      )
    },
    {
      key: 'funding',
      title: language === 'ar' ? 'التمويل المطلوب' : 'Funding Sought',
      dataIndex: 'amountSeeking',
      render: (value: number, record: DealOpportunity) => (
        <div>
          <div className="font-semibold font-arabic">{formatCurrency(value)}</div>
          <div className="text-sm text-gray-500 font-arabic">
            {language === 'ar' ? 'التقييم:' : 'Valuation:'} {formatCurrency(record.valuation)}
          </div>
        </div>
      )
    },
    {
      key: 'matchScore',
      title: language === 'ar' ? 'نقاط التطابق' : 'Match Score',
      dataIndex: 'matchScore',
      render: (value: number) => (
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`w-12 h-2 bg-gray-200 rounded-full ${isRTL ? 'ml-2' : 'mr-2'}`}>
            <div 
              className={`h-2 rounded-full ${
                value >= 90 ? 'bg-green-500' :
                value >= 70 ? 'bg-yellow-500' :
                'bg-red-500'
              }`}
              style={{ width: `${value}%` }}
            />
          </div>
          <span className="text-sm font-semibold font-arabic">{value}%</span>
        </div>
      )
    },
    {
      key: 'status',
      title: language === 'ar' ? 'الحالة' : 'Status',
      dataIndex: 'status',
      render: (value: string) => (
        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
          value === 'new' ? 'bg-blue-100 text-blue-800' :
          value === 'reviewing' ? 'bg-yellow-100 text-yellow-800' :
          value === 'due_diligence' ? 'bg-purple-100 text-purple-800' :
          value === 'term_sheet' ? 'bg-green-100 text-green-800' :
          'bg-gray-100 text-gray-800'
        } font-arabic`}>
          {value === 'new' ? (language === 'ar' ? 'جديد' : 'New') :
           value === 'reviewing' ? (language === 'ar' ? 'قيد المراجعة' : 'Reviewing') :
           value === 'due_diligence' ? (language === 'ar' ? 'العناية الواجبة' : 'Due Diligence') :
           value === 'term_sheet' ? (language === 'ar' ? 'ورقة الشروط' : 'Term Sheet') :
           (language === 'ar' ? 'مغلق' : 'Closed')}
        </span>
      )
    },
    {
      key: 'actions',
      title: language === 'ar' ? 'الإجراءات' : 'Actions',
      dataIndex: 'id',
      render: (id: string) => (
        <div className={`flex space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
          <ArabicButton
            size="sm"
            variant="outline"
            onClick={() => navigate(`/dashboard/deals/${id}`)}
            icon={<Eye className="w-3 h-3" />}
          >
            {language === 'ar' ? 'عرض' : 'View'}
          </ArabicButton>
        </div>
      )
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div>
              <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                {language === 'ar' ? `مرحباً، ${user?.name || 'المستثمر'}` : `Welcome, ${user?.name || 'Investor'}`}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' 
                  ? 'لوحة تحكم المستثمر - إدارة المحفظة وتدفق الصفقات'
                  : 'Investor Dashboard - Manage portfolio and deal flow'
                }
              </ArabicTypography>
            </div>
            <div className={`flex space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <ArabicButton
                variant="outline"
                onClick={() => navigate('/dashboard/deals/filter')}
                icon={<Filter className="w-4 h-4" />}
              >
                {language === 'ar' ? 'تصفية الصفقات' : 'Filter Deals'}
              </ArabicButton>
              <ArabicButton
                onClick={() => navigate('/dashboard/deals/new')}
                icon={<Plus className="w-4 h-4" />}
              >
                {language === 'ar' ? 'صفقة جديدة' : 'New Deal'}
              </ArabicButton>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
                <Briefcase className="w-6 h-6 text-blue-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.totalInvestments}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'إجمالي الاستثمارات' : 'Total Investments'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {formatCurrency(stats.portfolioValue)}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'قيمة المحفظة' : 'Portfolio Value'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.averageROI.toFixed(1)}x
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'متوسط العائد' : 'Average ROI'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mx-auto mb-4">
                <Target className="w-6 h-6 text-orange-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.dealsPipeline}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'الصفقات في الانتظار' : 'Deals in Pipeline'}
              </ArabicTypography>
            </ArabicCard>
          </div>
        )}

        {/* Main Content */}
        <div className="space-y-8">
          {/* Deal Flow */}
          <ArabicCard 
            title={language === 'ar' ? 'تدفق الصفقات' : 'Deal Flow'}
            headerAction={
              <ArabicButton
                variant="outline"
                size="sm"
                onClick={() => navigate('/dashboard/deals')}
              >
                {language === 'ar' ? 'عرض الكل' : 'View All'}
              </ArabicButton>
            }
          >
            <ArabicTable
              columns={dealFlowColumns}
              data={dealFlow}
              rowKey="id"
            />
          </ArabicCard>

          {/* Portfolio */}
          <ArabicCard 
            title={language === 'ar' ? 'المحفظة الاستثمارية' : 'Investment Portfolio'}
            headerAction={
              <ArabicButton
                variant="outline"
                size="sm"
                onClick={() => navigate('/dashboard/portfolio')}
              >
                {language === 'ar' ? 'عرض الكل' : 'View All'}
              </ArabicButton>
            }
          >
            <ArabicTable
              columns={portfolioColumns}
              data={portfolio}
              rowKey="id"
            />
          </ArabicCard>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <ArabicCard className="text-center cursor-pointer hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
                <Search className="w-6 h-6 text-blue-600" />
              </div>
              <ArabicTypography variant="h5" className="text-gray-900 font-semibold mb-2">
                {language === 'ar' ? 'البحث عن صفقات' : 'Deal Discovery'}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'اكتشف فرص استثمارية جديدة' : 'Discover new investment opportunities'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center cursor-pointer hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4">
                <PieChart className="w-6 h-6 text-green-600" />
              </div>
              <ArabicTypography variant="h5" className="text-gray-900 font-semibold mb-2">
                {language === 'ar' ? 'تحليل المحفظة' : 'Portfolio Analytics'}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'تحليل أداء استثماراتك' : 'Analyze your investment performance'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center cursor-pointer hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
              <ArabicTypography variant="h5" className="text-gray-900 font-semibold mb-2">
                {language === 'ar' ? 'تقارير السوق' : 'Market Reports'}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'تقارير وتحليلات السوق' : 'Market insights and analysis'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center cursor-pointer hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mx-auto mb-4">
                <Calendar className="w-6 h-6 text-orange-600" />
              </div>
              <ArabicTypography variant="h5" className="text-gray-900 font-semibold mb-2">
                {language === 'ar' ? 'الأحداث القادمة' : 'Upcoming Events'}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'فعاليات ومؤتمرات الاستثمار' : 'Investment events and conferences'}
              </ArabicTypography>
            </ArabicCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvestorDashboard;
