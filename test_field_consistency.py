#!/usr/bin/env python3
"""
Test script to verify field name consistency between frontend and backend
Tests that the backend returns the correct fields that the frontend expects
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8000"

def test_login_response_fields(username, password):
    """Test login response to verify correct field names"""
    print(f"\n🔍 Testing field consistency for: {username}")
    
    try:
        # Step 1: Login
        login_response = requests.post(
            f"{BASE_URL}/api/auth/login/",
            json={
                "username": username,
                "password": password
            },
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Login Status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"   ❌ Login failed: {login_response.text}")
            return {'success': False, 'error': 'Login failed'}
        
        login_data = login_response.json()
        user_data = login_data.get('user', {})
        
        print(f"   ✅ Login successful!")
        
        # Step 2: Check for CORRECT fields (your 7-role system)
        correct_fields = {
            'id': user_data.get('id'),
            'username': user_data.get('username'),
            'email': user_data.get('email'),
            'first_name': user_data.get('first_name'),
            'last_name': user_data.get('last_name'),
            'full_name': user_data.get('full_name'),
            'is_active': user_data.get('is_active'),
            'date_joined': user_data.get('date_joined'),
            'last_login': user_data.get('last_login'),
            'user_role': user_data.get('user_role'),  # YOUR ACTUAL ROLE FIELD
            'role_permissions': user_data.get('role_permissions'),
            'is_staff_member': user_data.get('is_staff_member'),
            'role_assignments': user_data.get('role_assignments'),
            'profile': user_data.get('profile')
        }
        
        # Step 3: Check for INCORRECT fields (Django boolean fields that shouldn't exist)
        incorrect_fields = {
            'is_staff': user_data.get('is_staff'),
            'is_superuser': user_data.get('is_superuser'),
            'is_admin': user_data.get('is_admin')  # This field doesn't exist in Django User model
        }
        
        print(f"\n   📋 CORRECT FIELDS (Your 7-Role System):")
        for field, value in correct_fields.items():
            if value is not None:
                print(f"      ✅ {field}: {value}")
            else:
                print(f"      ⚠️  {field}: None/Missing")
        
        print(f"\n   ❌ INCORRECT FIELDS (Django Boolean Fields - Should NOT exist):")
        incorrect_found = False
        for field, value in incorrect_fields.items():
            if value is not None:
                print(f"      ❌ {field}: {value} (SHOULD NOT EXIST)")
                incorrect_found = True
            else:
                print(f"      ✅ {field}: None (Correctly absent)")
        
        # Step 4: Validate role system
        user_role = user_data.get('user_role')
        valid_roles = ['super_admin', 'admin', 'moderator', 'entrepreneur', 'mentor', 'investor', 'user']
        
        role_valid = user_role in valid_roles if user_role else False
        
        print(f"\n   🎯 ROLE VALIDATION:")
        print(f"      Role: {user_role}")
        print(f"      Valid: {'✅' if role_valid else '❌'}")
        print(f"      Valid Roles: {valid_roles}")
        
        # Step 5: Check profile structure
        profile = user_data.get('profile', {})
        if profile:
            print(f"\n   👤 PROFILE STRUCTURE:")
            primary_role = profile.get('primary_role')
            if primary_role:
                print(f"      Primary Role: {primary_role.get('name')} ({primary_role.get('permission_level')})")
            
            active_roles = profile.get('active_roles', [])
            if active_roles:
                print(f"      Active Roles: {len(active_roles)} roles")
                for role in active_roles[:3]:  # Show first 3
                    print(f"        - {role.get('name')} ({role.get('permission_level')})")
        
        return {
            'success': True,
            'username': username,
            'user_role': user_role,
            'role_valid': role_valid,
            'incorrect_fields_found': incorrect_found,
            'has_profile': bool(profile),
            'correct_fields_count': len([v for v in correct_fields.values() if v is not None]),
            'field_analysis': {
                'correct': correct_fields,
                'incorrect': incorrect_fields
            }
        }
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {
            'success': False,
            'username': username,
            'error': str(e)
        }

def main():
    """Test field consistency for different user types"""
    print("🚀 Starting Field Name Consistency Tests")
    print("=" * 60)
    
    # Test users with different roles
    test_users = [
        {'username': 'test_user_1753259713', 'password': 'TestPassword123', 'expected_role': 'user'},
        {'username': 'test_entrepreneur_1753259715', 'password': 'TestPassword123', 'expected_role': 'entrepreneur'},
        {'username': 'test_mentor_1753259717', 'password': 'TestPassword123', 'expected_role': 'mentor'},
        {'username': 'test_investor_1753259719', 'password': 'TestPassword123', 'expected_role': 'investor'},
    ]
    
    results = []
    
    for user_info in test_users:
        result = test_login_response_fields(
            user_info['username'], 
            user_info['password']
        )
        result['expected_role'] = user_info['expected_role']
        results.append(result)
        time.sleep(1)  # Delay between tests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FIELD CONSISTENCY TEST SUMMARY")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    valid_roles = [r for r in successful_tests if r.get('role_valid', False)]
    no_incorrect_fields = [r for r in successful_tests if not r.get('incorrect_fields_found', True)]
    
    print(f"Total Tests: {len(results)}")
    print(f"Successful Logins: {len(successful_tests)}")
    print(f"Failed Tests: {len(failed_tests)}")
    print(f"Valid Role Fields: {len(valid_roles)}")
    print(f"No Incorrect Fields: {len(no_incorrect_fields)}")
    
    if len(valid_roles) == len(successful_tests) and len(no_incorrect_fields) == len(successful_tests):
        print("\n🎉 ALL TESTS PASSED! Field consistency is perfect!")
    else:
        print("\n⚠️  Some field consistency issues found. Check details above.")
    
    # Detailed results
    print("\n📋 DETAILED RESULTS:")
    for result in results:
        if result['success']:
            role_icon = "✅" if result.get('role_valid', False) else "❌"
            fields_icon = "✅" if not result.get('incorrect_fields_found', True) else "❌"
            print(f"   {role_icon} {result['username']}: "
                  f"Role={result.get('user_role', 'None')} "
                  f"{fields_icon} Fields Clean")
        else:
            print(f"   ❌ {result['username']}: FAILED - {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
