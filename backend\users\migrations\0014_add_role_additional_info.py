# Generated by Django 5.2.1 on 2025-07-22 12:11

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0013_make_expertise_nullable"),
    ]

    operations = [
        migrations.AddField(
            model_name="userprofile",
            name="role_additional_info",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text="Additional information specific to user's role",
            ),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="expertise",
            field=models.TextField(
                blank=True,
                default="",
                help_text="Areas of expertise and skills",
                null=True,
            ),
        ),
    ]
