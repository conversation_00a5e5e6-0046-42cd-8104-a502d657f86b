{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label='users' %}">Users</a>
&rsaquo; <a href="{% url 'admin:users_userapproval_changelist' %}">User approvals</a>
&rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <form method="post">
        {% csrf_token %}
        
        <div class="module aligned">
            <h2>Reject User Registration</h2>
            
            <div class="form-row">
                <div>
                    <label><strong>User:</strong></label>
                    <p>{{ approval.user.get_full_name|default:approval.user.username }} ({{ approval.user.email }})</p>
                </div>
            </div>
            
            <div class="form-row">
                <div>
                    <label><strong>Registration Date:</strong></label>
                    <p>{{ approval.created_at }}</p>
                </div>
            </div>
            
            <div class="form-row">
                <div>
                    <label for="{{ form.reason.id_for_label }}">{{ form.reason.label }}:</label>
                    {{ form.reason }}
                    {% if form.reason.help_text %}
                        <p class="help">{{ form.reason.help_text }}</p>
                    {% endif %}
                    {{ form.reason.errors }}
                </div>
            </div>
        </div>
        
        <div class="submit-row">
            <input type="submit" value="Reject User" class="default" />
            <a href="{% url 'admin:users_userapproval_changelist' %}" class="button cancel-link">Cancel</a>
        </div>
    </form>
</div>
{% endblock %}