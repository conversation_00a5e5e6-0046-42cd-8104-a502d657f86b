<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Manual Server Startup Guide</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2d3748;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        .step {
            background: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .step h3 {
            color: #2d3748;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .command {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            position: relative;
        }
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4299e1;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        .copy-btn:hover {
            background: #3182ce;
        }
        .status-check {
            background: #e6fffa;
            border: 1px solid #38b2ac;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-offline { background: #e53e3e; }
        .status-online { background: #38a169; }
        .status-checking { background: #d69e2e; animation: pulse 1s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.2s;
        }
        .button:hover {
            background: #3182ce;
        }
        .success {
            background: #c6f6d5;
            border-color: #38a169;
            color: #22543d;
        }
        .warning {
            background: #fef5e7;
            border-color: #d69e2e;
            color: #744210;
        }
        .error {
            background: #fed7d7;
            border-color: #e53e3e;
            color: #742a2a;
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Manual Server Startup Guide</h1>
        
        <div class="status-check">
            <h3>📊 Current Server Status</h3>
            <p>
                <span class="status-indicator" id="backend-indicator"></span>
                <strong>Backend (Django):</strong> <span id="backend-status">Checking...</span>
            </p>
            <p>
                <span class="status-indicator" id="frontend-indicator"></span>
                <strong>Frontend (React):</strong> <span id="frontend-status">Checking...</span>
            </p>
            <button class="button" onclick="checkServers()">🔄 Check Status</button>
            <button class="button" onclick="autoRefresh()">🔄 Auto Refresh</button>
        </div>

        <div class="step">
            <h3>🔧 Step 1: Start Backend Server</h3>
            <p><strong>Open Command Prompt or Terminal and run:</strong></p>
            <div class="command">
                <button class="copy-btn" onclick="copyToClipboard('backend-cmd')">Copy</button>
                <div id="backend-cmd"># Navigate to your project directory (replace with your actual path)
cd "C:\Users\<USER>\Desktop\AI PROJECTS\sb1-ybvwcyge"

# Go to backend folder
cd backend

# Start Django server
python manage.py runserver 8000</div>
            </div>
            <p><strong>Expected output:</strong></p>
            <div class="command">Starting development server at http://127.0.0.1:8000/
Quit the server with CTRL-BREAK.</div>
            <button class="button" onclick="testBackend()">🧪 Test Backend</button>
        </div>

        <div class="step">
            <h3>⚛️ Step 2: Start Frontend Server</h3>
            <p><strong>Open a NEW Command Prompt/Terminal and run:</strong></p>
            <div class="command">
                <button class="copy-btn" onclick="copyToClipboard('frontend-cmd')">Copy</button>
                <div id="frontend-cmd"># Navigate to your project directory (replace with your actual path)
cd "C:\Users\<USER>\Desktop\AI PROJECTS\sb1-ybvwcyge"

# Go to frontend folder
cd frontend

# Start React development server
npm run dev</div>
            </div>
            <p><strong>Expected output:</strong></p>
            <div class="command">Local:   http://localhost:3000/
Network: use --host to expose</div>
            <button class="button" onclick="testFrontend()">🧪 Test Frontend</button>
        </div>

        <div class="step success" id="ready-section" style="display: none;">
            <h3>🎉 Servers are Running!</h3>
            <p>Both servers are now running successfully. You can access the application:</p>
            <div class="quick-links">
                <a href="http://localhost:3000" class="button" target="_blank">🏠 Main Application</a>
                <a href="http://localhost:8000/admin/" class="button" target="_blank">👑 Admin Panel</a>
                <a href="http://localhost:3000/login" class="button" target="_blank">🔐 Login Page</a>
                <a href="http://localhost:3000/dashboard" class="button" target="_blank">📊 Dashboard</a>
            </div>
        </div>

        <div class="step">
            <h3>🧪 Step 3: Test All Fixes</h3>
            <p>Once both servers are running, test all the implemented fixes:</p>
            <button class="button" onclick="openTestSuite()">🚀 Open Test Suite</button>
            <button class="button" onclick="openValidation()">✅ Open Validation</button>
            <button class="button" onclick="runQuickTest()">⚡ Quick Test</button>
        </div>

        <div class="step warning">
            <h3>⚠️ Troubleshooting</h3>
            <details>
                <summary><strong>Common Issues</strong></summary>
                <ul>
                    <li><strong>Python not found:</strong> Install Python from <a href="https://python.org" target="_blank">python.org</a></li>
                    <li><strong>npm not found:</strong> Install Node.js from <a href="https://nodejs.org" target="_blank">nodejs.org</a></li>
                    <li><strong>Port already in use:</strong> Kill the process or use different ports</li>
                    <li><strong>Module not found:</strong> Run <code>pip install -r requirements.txt</code> in backend</li>
                    <li><strong>Dependencies missing:</strong> Run <code>npm install</code> in frontend</li>
                </ul>
            </details>
        </div>

        <div class="step" id="test-results" style="display: none;">
            <h3>📊 Test Results</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;

        async function checkServers() {
            // Update indicators to checking state
            document.getElementById('backend-indicator').className = 'status-indicator status-checking';
            document.getElementById('frontend-indicator').className = 'status-indicator status-checking';
            document.getElementById('backend-status').textContent = 'Checking...';
            document.getElementById('frontend-status').textContent = 'Checking...';

            // Check backend
            let backendOnline = false;
            try {
                const response = await fetch('http://localhost:8000/api/health/', {
                    method: 'GET',
                    mode: 'cors',
                    timeout: 5000
                });
                backendOnline = response.ok;
            } catch (error) {
                backendOnline = false;
            }

            // Check frontend
            let frontendOnline = false;
            try {
                const response = await fetch('http://localhost:3000/', {
                    method: 'GET',
                    mode: 'no-cors',
                    timeout: 5000
                });
                frontendOnline = true;
            } catch (error) {
                frontendOnline = false;
            }

            // Update status display
            updateStatus('backend', backendOnline);
            updateStatus('frontend', frontendOnline);

            // Show ready section if both are online
            const readySection = document.getElementById('ready-section');
            if (backendOnline && frontendOnline) {
                readySection.style.display = 'block';
            } else {
                readySection.style.display = 'none';
            }
        }

        function updateStatus(server, isOnline) {
            const indicator = document.getElementById(`${server}-indicator`);
            const status = document.getElementById(`${server}-status`);
            
            if (isOnline) {
                indicator.className = 'status-indicator status-online';
                status.textContent = 'Online ✅';
                status.style.color = '#22543d';
            } else {
                indicator.className = 'status-indicator status-offline';
                status.textContent = 'Offline ❌';
                status.style.color = '#742a2a';
            }
        }

        function autoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                document.querySelector('button[onclick="autoRefresh()"]').textContent = '🔄 Auto Refresh';
            } else {
                autoRefreshInterval = setInterval(checkServers, 5000);
                document.querySelector('button[onclick="autoRefresh()"]').textContent = '⏹️ Stop Auto Refresh';
                checkServers(); // Check immediately
            }
        }

        async function testBackend() {
            try {
                const response = await fetch('http://localhost:8000/api/health/');
                if (response.ok) {
                    alert('✅ Backend is running correctly!\n\nServer: http://localhost:8000\nAdmin: http://localhost:8000/admin/');
                } else {
                    alert(`⚠️ Backend responded with status: ${response.status}\n\nPlease check the server logs.`);
                }
            } catch (error) {
                alert('❌ Backend is not running.\n\nPlease start the Django server first:\n\ncd backend\npython manage.py runserver 8000');
            }
        }

        async function testFrontend() {
            try {
                const response = await fetch('http://localhost:3000/', { mode: 'no-cors' });
                alert('✅ Frontend is running correctly!\n\nApplication: http://localhost:3000');
            } catch (error) {
                alert('❌ Frontend is not running.\n\nPlease start the React server first:\n\ncd frontend\nnpm run dev');
            }
        }

        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                const btn = element.parentElement.querySelector('.copy-btn');
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                setTimeout(() => {
                    btn.textContent = originalText;
                }, 2000);
            });
        }

        function openTestSuite() {
            window.open('./comprehensive-app-test.html', '_blank');
        }

        function openValidation() {
            window.open('./test-validation.html', '_blank');
        }

        async function runQuickTest() {
            const results = document.getElementById('test-results');
            const content = document.getElementById('results-content');
            
            content.innerHTML = '<p>Running quick tests...</p>';
            results.style.display = 'block';
            
            const tests = [
                { name: 'Backend Health', test: () => fetch('http://localhost:8000/api/health/') },
                { name: 'Frontend Access', test: () => fetch('http://localhost:3000/', { mode: 'no-cors' }) },
                { name: 'Admin Panel', test: () => fetch('http://localhost:8000/admin/', { mode: 'no-cors' }) }
            ];
            
            let results_html = '<h4>Quick Test Results:</h4><ul>';
            
            for (const test of tests) {
                try {
                    await test.test();
                    results_html += `<li>✅ ${test.name}: OK</li>`;
                } catch (error) {
                    results_html += `<li>❌ ${test.name}: Failed</li>`;
                }
            }
            
            results_html += '</ul>';
            content.innerHTML = results_html;
        }

        // Auto-check status on load
        window.addEventListener('load', () => {
            checkServers();
        });
    </script>
</body>
</html>
