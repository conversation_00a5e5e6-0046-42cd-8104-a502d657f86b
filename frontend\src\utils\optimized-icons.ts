/**
 * OPTIMIZED ICONS
 * Tree-shaken icon imports to reduce bundle size
 * Only import the icons we actually use
 */

// Import only the icons we need from lucide-react
import {
  // Navigation & UI
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  
  // Dashboard & Analytics
  BarChart3,
  LineChart,
  PieChart,
  TrendingUp,
  TrendingDown,
  Activity,
  
  // User & Profile
  User,
  Users,
  UserPlus,
  UserMinus,
  UserCheck,
  UserX,
  Crown,
  Shield,
  ShieldCheck,
  ShieldAlert,
  
  // Actions
  Plus,
  Minus,
  Edit,
  Edit2,
  Edit3,
  Trash,
  Trash2,
  Save,
  Download,
  Upload,
  Copy,
  Share,
  Share2,
  
  // Status & Feedback
  Check,
  CheckCircle,
  CheckCircle2,
  XCircle,
  AlertCircle,
  AlertTriangle,
  Info,
  HelpCircle,
  
  // Communication
  Mail,
  MessageSquare,
  MessageCircle,
  Phone,
  PhoneCall,
  Video,
  Mic,
  MicOff,
  
  // Files & Documents
  File,
  FileText,
  Folder,
  FolderOpen,
  Image,
  FileImage,
  
  // Settings & Configuration
  Settings,
  Cog,
  Sliders,
  Filter,
  SlidersHorizontal,
  MoreHorizontal,
  MoreVertical,
  
  // Search & Navigation
  Search,
  Eye,
  EyeOff,
  Home,
  MapPin,
  Navigation,
  Compass,
  
  // Time & Calendar
  Clock,
  Calendar,
  CalendarDays,
  Timer,
  
  // Business & Finance
  DollarSign,
  CreditCard,
  Wallet,
  Building,
  Building2,
  Store,
  ShoppingCart,
  ShoppingBag,
  Briefcase,
  
  // Technology & Development
  Code,
  Code2,
  Terminal,
  Database,
  Server,
  Cloud,
  Wifi,
  WifiOff,
  Signal,
  Battery,
  Zap,
  
  // Media & Content
  Play,
  Pause,
  Square,
  Volume2,
  VolumeX,
  Camera,
  
  // Utility
  RefreshCw,
  RotateCcw,
  ExternalLink,
  Link,
  
  // Forms & Input
  Lock,
  Unlock,
  Key,
  Fingerprint,
  
  // Social & Community
  Heart,
  Star,
  ThumbsUp,
  ThumbsDown,
  Flag,
  
  // Notifications
  Bell,
  BellOff,
  BellRing,
  
  // Layout & Design
  Layout,
  LayoutGrid,
  LayoutList,
  Sidebar,
  PanelLeft,
  PanelRight,
  
  // Transportation
  Car,
  Truck,
  Plane,
  Ship,
  
  // Weather & Environment
  Sun,
  Moon,
  CloudRain,
  
  // Tools & Equipment
  Wrench,
  Hammer,
  Scissors,
  Paperclip,
  
  // Education & Learning
  BookOpen,
  GraduationCap,
  Award,
  Target,
  
  // Gaming & Entertainment
  Gamepad,
  Gamepad2,
  
  // Miscellaneous
  Gift,
  Coffee,
  Lightbulb,
  Globe,
  Smartphone,
  Tablet,
  Monitor,
  Laptop,
  
  // Special Icons
  LogIn,
  LogOut,
  Power,
  PowerOff
} from 'lucide-react';

// Re-export all imported icons
export {
  // Navigation & UI
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,

  // Dashboard & Analytics
  BarChart3,
  LineChart,
  PieChart,
  TrendingUp,
  TrendingDown,
  Activity,

  // User & Profile
  User,
  Users,
  UserPlus,
  UserMinus,
  UserCheck,
  UserX,
  Crown,
  Shield,
  ShieldCheck,
  ShieldAlert,

  // Actions
  Plus,
  Minus,
  Edit,
  Edit2,
  Edit3,
  Trash,
  Trash2,
  Save,
  Download,
  Upload,
  Copy,
  Share,
  Share2,

  // Status & Feedback
  Check,
  CheckCircle,
  CheckCircle2,
  XCircle,
  AlertCircle,
  AlertTriangle,
  Info,
  HelpCircle,

  // Communication
  Mail,
  MessageSquare,
  MessageCircle,
  Phone,
  PhoneCall,
  Video,
  Mic,
  MicOff,

  // Files & Documents
  File,
  FileText,
  Folder,
  FolderOpen,
  Image,
  FileImage,

  // Settings & Configuration
  Settings,
  Cog,
  Sliders,
  Filter,
  SlidersHorizontal,
  MoreHorizontal,
  MoreVertical,

  // Search & Navigation
  Search,
  Eye,
  EyeOff,
  Home,
  MapPin,
  Navigation,
  Compass,

  // Time & Calendar
  Clock,
  Calendar,
  CalendarDays,
  Timer,

  // Business & Finance
  DollarSign,
  CreditCard,
  Wallet,
  Building,
  Building2,
  Store,
  ShoppingCart,
  ShoppingBag,
  Briefcase,

  // Technology & Development
  Code,
  Code2,
  Terminal,
  Database,
  Server,
  Cloud,
  Wifi,
  WifiOff,
  Signal,
  Battery,
  Zap,

  // Media & Content
  Play,
  Pause,
  Square,
  Volume2,
  VolumeX,
  Camera,

  // Utility
  RefreshCw,
  RotateCcw,
  ExternalLink,
  Link,

  // Forms & Input
  Lock,
  Unlock,
  Key,
  Fingerprint,

  // Social & Community
  Heart,
  Star,
  ThumbsUp,
  ThumbsDown,
  Flag,

  // Notifications
  Bell,
  BellOff,
  BellRing,

  // Layout & Design
  Layout,
  LayoutGrid,
  LayoutList,
  Sidebar,
  PanelLeft,
  PanelRight,

  // Transportation
  Car,
  Truck,
  Plane,
  Ship,

  // Weather & Environment
  Sun,
  Moon,
  CloudRain,

  // Tools & Equipment
  Wrench,
  Hammer,
  Scissors,
  Paperclip,

  // Education & Learning
  BookOpen,
  GraduationCap,
  Award,
  Target,

  // Gaming & Entertainment
  Gamepad,
  Gamepad2,

  // Miscellaneous
  Gift,
  Coffee,
  Lightbulb,
  Globe,
  Smartphone,
  Tablet,
  Monitor,
  Laptop,

  // Special Icons
  LogIn,
  LogOut,
  Power,
  PowerOff
};

// Create aliases for icons that don't exist in lucide-react
export const Stop = Square;
export const CloudUpload = Upload;
export const CloudDownload = Download;
export const Stopwatch = Timer;

// Icon size constants for consistency
export const ICON_SIZES = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  xxl: 48,
} as const;

// Common icon props for consistency
export const ICON_PROPS = {
  strokeWidth: 1.5,
  className: 'inline-block',
} as const;

// Pre-configured icon sets for common use cases
export const DASHBOARD_ICONS = {
  analytics: BarChart3,
  users: Users,
  revenue: DollarSign,
  growth: TrendingUp,
  ideas: Lightbulb,
  settings: Settings,
};

export const NAVIGATION_ICONS = {
  home: Home,
  dashboard: LayoutGrid,
  users: Users,
  settings: Settings,
  profile: User,
  logout: LogOut,
};

export const ACTION_ICONS = {
  add: Plus,
  edit: Edit,
  delete: Trash2,
  save: Save,
  cancel: X,
  confirm: Check,
};

export const STATUS_ICONS = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
  loading: RefreshCw,
};

// Type definitions for better TypeScript support
export type IconSize = keyof typeof ICON_SIZES;
export type DashboardIcon = keyof typeof DASHBOARD_ICONS;
export type NavigationIcon = keyof typeof NAVIGATION_ICONS;
export type ActionIcon = keyof typeof ACTION_ICONS;
export type StatusIcon = keyof typeof STATUS_ICONS;
