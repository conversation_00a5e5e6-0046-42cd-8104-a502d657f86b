"""
✅ AI MOCK SERVICE FOR DEVELOPMENT
Provides realistic AI responses for testing and development when real API is not available
"""

import logging
import random
import time
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class MockAIService:
    """Mock AI service that provides realistic responses for development"""
    
    def __init__(self):
        self.is_available = True
        self.model_name = "mock-ai-service"
        self.session_counter = 0
        
    def get_status(self) -> Dict[str, Any]:
        """Return mock status"""
        return {
            'is_available': True,
            'service': 'mock-ai-service',
            'model': 'development-mock',
            'status': 'connected',
            'timestamp': datetime.now().isoformat()
        }
    
    def start_session(self, session_type: str = 'general', language: str = 'ar') -> Dict[str, Any]:
        """Start a mock AI session"""
        self.session_counter += 1
        session_id = f"mock_session_{self.session_counter}_{int(time.time())}"
        
        return {
            'session_id': session_id,
            'session_type': session_type,
            'language': language,
            'status': 'active',
            'created_at': datetime.now().isoformat()
        }
    
    def generate_content(self, prompt: str, language: str = 'ar') -> str:
        """Generate mock AI response based on prompt"""
        
        # Simulate thinking time
        time.sleep(random.uniform(0.5, 2.0))
        
        # Role-based responses
        if 'entrepreneur' in prompt.lower() or 'ريادة' in prompt or 'مشروع' in prompt:
            responses = [
                "مرحباً! أنا ياسمين، مساعدك في ريادة الأعمال. يمكنني مساعدتك في تطوير أفكار المشاريع، إعداد خطط العمل، تحليل السوق، واستراتيجيات التمويل. ما هو المشروع الذي تفكر فيه؟",
                "بصفتي مساعدك في ريادة الأعمال، يمكنني مساعدتك في:\n\n• تطوير وتحليل أفكار المشاريع\n• إعداد خطط العمل الشاملة\n• دراسة السوق والمنافسين\n• استراتيجيات التسويق والمبيعات\n• خيارات التمويل والاستثمار\n\nما هو التحدي الذي تواجهه في مشروعك؟",
                "أهلاً وسهلاً! كوني مختصة في ريادة الأعمال، يمكنني مساعدتك في تحويل فكرتك إلى مشروع ناجح. سواء كنت في مرحلة الفكرة أو التنفيذ، أنا هنا لدعمك. ما هي رؤيتك لمشروعك؟"
            ]
        elif 'mentor' in prompt.lower() or 'إرشاد' in prompt or 'مرشد' in prompt:
            responses = [
                "مرحباً! أنا ياسمين، مساعدك في الإرشاد والتوجيه. يمكنني مساعدتك في تطوير استراتيجيات الإرشاد، تقديم النصائح للمبتدئين، وبناء برامج التطوير المهني. كيف يمكنني مساعدتك اليوم؟",
                "كمساعدك في الإرشاد، يمكنني مساعدتك في:\n\n• تطوير مهارات الإرشاد والتوجيه\n• إعداد برامج التدريب والتطوير\n• تقنيات التواصل الفعال\n• بناء علاقات إرشادية قوية\n• تقييم التقدم والنتائج\n\nما هو مجال الإرشاد الذي تركز عليه؟"
            ]
        elif 'investor' in prompt.lower() or 'استثمار' in prompt or 'مستثمر' in prompt:
            responses = [
                "أهلاً بك! أنا ياسمين، مساعدك في الاستثمار. يمكنني مساعدتك في تحليل الفرص الاستثمارية، تقييم المخاطر، دراسة الجدوى المالية، وتطوير استراتيجيات الاستثمار. ما هي الفرصة التي تدرسها؟",
                "كمساعدك في الاستثمار، يمكنني مساعدتك في:\n\n• تحليل الفرص الاستثمارية\n• تقييم المخاطر والعوائد\n• دراسة الجدوى المالية\n• تحليل السوق والاتجاهات\n• استراتيجيات التنويع\n\nما نوع الاستثمارات التي تهتم بها؟"
            ]
        elif 'admin' in prompt.lower() or 'إدارة' in prompt or 'مدير' in prompt:
            responses = [
                "مرحباً! أنا ياسمين، مساعدك الإداري. يمكنني مساعدتك في إدارة المنصة، تنظيم المحتوى، إدارة المستخدمين، وتحسين العمليات. كيف يمكنني مساعدتك في المهام الإدارية؟",
                "كمساعدك الإداري، يمكنني مساعدتك في:\n\n• إدارة المستخدمين والصلاحيات\n• تنظيم المحتوى والبيانات\n• مراقبة الأداء والإحصائيات\n• تحسين العمليات والإجراءات\n• إعداد التقارير والتحليلات\n\nما هي المهمة الإدارية التي تحتاج مساعدة فيها؟"
            ]
        elif any(word in prompt.lower() for word in ['hello', 'hi', 'مرحبا', 'أهلا', 'السلام']):
            responses = [
                "أهلاً وسهلاً! أنا ياسمين، مساعدك الذكي. يمكنني مساعدتك في مجالات متعددة مثل ريادة الأعمال، التعليم، والإرشاد. كيف يمكنني مساعدتك اليوم؟",
                "مرحباً بك! أنا ياسمين AI، مساعدك الشخصي في رحلة التعلم وريادة الأعمال. أنا هنا لمساعدتك في تحقيق أهدافك. ما الذي تود التحدث عنه؟",
                "السلام عليكم ومرحباً بك! أنا ياسمين، مساعدك الذكي المتخصص في دعم المجتمع السوري في مجال علوم البيانات والذكاء الاصطناعي. كيف يمكنني خدمتك؟"
            ]
        elif any(word in prompt for word in ['فكرة', 'مشروع', 'business', 'idea', 'startup']):
            responses = [
                "فكرة رائعة! دعني أساعدك في تطوير هذه الفكرة. لتقديم أفضل المساعدة، أحتاج لمعرفة:\n\n• ما هو المجال أو الصناعة؟\n• من هو الجمهور المستهدف؟\n• ما هي المشكلة التي تحلها؟\n• ما هي الموارد المتاحة لديك؟\n\nشاركني المزيد من التفاصيل لأتمكن من مساعدتك بشكل أفضل.",
                "ممتاز! تطوير الأفكار التجارية هو تخصصي. يمكنني مساعدتك في:\n\n• تحليل الفكرة وتقييم جدواها\n• دراسة السوق والمنافسين\n• تحديد نموذج العمل المناسب\n• وضع استراتيجية التنفيذ\n• تقدير التكاليف والإيرادات\n\nحدثني أكثر عن فكرتك وسأقدم لك تحليلاً مفصلاً."
            ]
        elif any(word in prompt for word in ['تعلم', 'دراسة', 'تعليم', 'learn', 'study', 'education']):
            responses = [
                "التعلم رحلة رائعة! يمكنني مساعدتك في:\n\n• وضع خطة تعليمية مخصصة\n• اختيار أفضل المصادر والموارد\n• تطوير مهارات جديدة\n• متابعة التقدم وتقييم الأداء\n• التحضير للامتحانات والشهادات\n\nما هو المجال الذي تريد التعلم فيه؟",
                "أحب مساعدة الناس في رحلة التعلم! سواء كان ذلك في:\n\n• علوم البيانات والذكاء الاصطناعي\n• ريادة الأعمال والإدارة\n• التكنولوجيا والبرمجة\n• المهارات الشخصية والمهنية\n\nما هو هدفك التعليمي؟ وما مستواك الحالي؟"
            ]
        else:
            # General responses
            responses = [
                "شكراً لك على سؤالك. يمكنني مساعدتك في مجالات متعددة مثل ريادة الأعمال، التعليم، والتطوير المهني. هل يمكنك توضيح ما تحتاج مساعدة فيه بالتحديد؟",
                "أنا هنا لمساعدتك! كوني مساعد ذكي متخصص، يمكنني تقديم المساعدة في:\n\n• تطوير الأفكار التجارية\n• التخطيط الاستراتيجي\n• التعلم والتطوير\n• حل المشكلات\n• تقديم النصائح المهنية\n\nما هو التحدي الذي تواجهه؟",
                "سؤال مثير للاهتمام! لأتمكن من تقديم أفضل إجابة، هل يمكنك تقديم المزيد من التفاصيل؟ أو إخباري عن السياق الذي تسأل فيه؟ هذا سيساعدني في تخصيص إجابتي لاحتياجاتك."
            ]
        
        return random.choice(responses)
    
    def chat(self, message: str, session_type: str = 'general', language: str = 'ar') -> Dict[str, Any]:
        """Mock chat function"""
        try:
            response_text = self.generate_content(message, language)
            
            return {
                'success': True,
                'response': response_text,
                'session_id': f"mock_session_{int(time.time())}",
                'interaction_id': f"interaction_{random.randint(1000, 9999)}",
                'model': 'mock-ai-service',
                'timestamp': datetime.now().isoformat(),
                'data': {
                    'content': response_text,
                    'language': language,
                    'session_type': session_type
                }
            }
        except Exception as e:
            logger.error(f"Mock AI error: {e}")
            return {
                'success': False,
                'error': f"Mock AI service error: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }

# Global mock instance
mock_ai_service = MockAIService()

def get_mock_ai_service():
    """Get the global mock AI service instance"""
    return mock_ai_service
