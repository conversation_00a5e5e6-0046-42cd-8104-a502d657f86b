/**
 * Mentorship Page
 * Main mentorship dashboard for managing mentorship activities
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { 
  Users, 
  Calendar, 
  MessageCircle, 
  Star,
  Clock,
  CheckCircle,
  Plus,
  Search,
  BookOpen,
  Award,
  TrendingUp,
  Target
} from 'lucide-react';

interface MentorshipSession {
  id: number;
  mentorName: string;
  date: string;
  time: string;
  duration: number;
  status: 'upcoming' | 'completed' | 'cancelled';
  topic: string;
  type: 'video' | 'phone' | 'in-person';
}

interface MentorshipStats {
  totalSessions: number;
  completedSessions: number;
  activeMentors: number;
  averageRating: number;
}

const MentorshipPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [sessions, setSessions] = useState<MentorshipSession[]>([]);
  const [stats, setStats] = useState<MentorshipStats>({
    totalSessions: 0,
    completedSessions: 0,
    activeMentors: 0,
    averageRating: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockSessions: MentorshipSession[] = [
      {
        id: 1,
        mentorName: 'Sarah Johnson',
        date: '2024-01-25',
        time: '14:00',
        duration: 60,
        status: 'upcoming',
        topic: 'Product Strategy Review',
        type: 'video'
      },
      {
        id: 2,
        mentorName: 'Michael Chen',
        date: '2024-01-20',
        time: '10:00',
        duration: 45,
        status: 'completed',
        topic: 'Fundraising Strategy',
        type: 'video'
      },
      {
        id: 3,
        mentorName: 'Emily Rodriguez',
        date: '2024-01-18',
        time: '16:30',
        duration: 30,
        status: 'completed',
        topic: 'Marketing Plan Discussion',
        type: 'phone'
      }
    ];

    const mockStats: MentorshipStats = {
      totalSessions: 12,
      completedSessions: 8,
      activeMentors: 3,
      averageRating: 4.8
    };

    setTimeout(() => {
      setSessions(mockSessions);
      setStats(mockStats);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return '📹';
      case 'phone':
        return '📞';
      case 'in-person':
        return '🤝';
      default:
        return '💬';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading', 'Loading...')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Users className={`text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={32} />
                {t('mentorship.dashboard.title', 'Mentorship Dashboard')}
              </h1>
              <p className="text-gray-600 mt-2">
                {t('mentorship.dashboard.subtitle', 'Manage your mentorship journey and sessions')}
              </p>
            </div>
            
            <div className="flex space-x-4">
              <button
                onClick={() => navigate('/dashboard/find-mentor')}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Search size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('mentorship.dashboard.findMentor', 'Find Mentor')}
              </button>
              <button
                onClick={() => navigate('/dashboard/mentorship/schedule')}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                <Plus size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('mentorship.dashboard.scheduleSession', 'Schedule Session')}
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Calendar className="text-blue-600" size={24} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {t('mentorship.stats.totalSessions', 'Total Sessions')}
                </p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalSessions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <CheckCircle className="text-green-600" size={24} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {t('mentorship.stats.completed', 'Completed')}
                </p>
                <p className="text-2xl font-bold text-gray-900">{stats.completedSessions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Users className="text-purple-600" size={24} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {t('mentorship.stats.activeMentors', 'Active Mentors')}
                </p>
                <p className="text-2xl font-bold text-gray-900">{stats.activeMentors}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <Star className="text-yellow-600" size={24} />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {t('mentorship.stats.averageRating', 'Average Rating')}
                </p>
                <p className="text-2xl font-bold text-gray-900">{stats.averageRating}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <button
            onClick={() => navigate('/dashboard/find-mentor')}
            className="bg-white rounded-lg shadow-sm p-6 text-left hover:shadow-md transition-shadow"
          >
            <div className="flex items-center mb-4">
              <Search className="text-blue-500 mr-3" size={24} />
              <h3 className="text-lg font-semibold text-gray-900">
                {t('mentorship.quickActions.findMentor', 'Find a Mentor')}
              </h3>
            </div>
            <p className="text-gray-600">
              {t('mentorship.quickActions.findMentorDesc', 'Browse and connect with experienced professionals')}
            </p>
          </button>

          <button
            onClick={() => navigate('/dashboard/mentorship/resources')}
            className="bg-white rounded-lg shadow-sm p-6 text-left hover:shadow-md transition-shadow"
          >
            <div className="flex items-center mb-4">
              <BookOpen className="text-green-500 mr-3" size={24} />
              <h3 className="text-lg font-semibold text-gray-900">
                {t('mentorship.quickActions.resources', 'Learning Resources')}
              </h3>
            </div>
            <p className="text-gray-600">
              {t('mentorship.quickActions.resourcesDesc', 'Access curated learning materials and guides')}
            </p>
          </button>

          <button
            onClick={() => navigate('/dashboard/mentorship/goals')}
            className="bg-white rounded-lg shadow-sm p-6 text-left hover:shadow-md transition-shadow"
          >
            <div className="flex items-center mb-4">
              <Target className="text-purple-500 mr-3" size={24} />
              <h3 className="text-lg font-semibold text-gray-900">
                {t('mentorship.quickActions.goals', 'Set Goals')}
              </h3>
            </div>
            <p className="text-gray-600">
              {t('mentorship.quickActions.goalsDesc', 'Define and track your mentorship objectives')}
            </p>
          </button>
        </div>

        {/* Recent Sessions */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">
                {t('mentorship.dashboard.recentSessions', 'Recent Sessions')}
              </h2>
              <button
                onClick={() => navigate('/dashboard/mentorship/sessions')}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                {t('common.viewAll', 'View All')}
              </button>
            </div>
          </div>

          <div className="p-6">
            {sessions.length === 0 ? (
              <div className="text-center py-12">
                <Calendar className="mx-auto text-gray-400 mb-4" size={48} />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {t('mentorship.dashboard.noSessions', 'No sessions yet')}
                </h3>
                <p className="text-gray-600 mb-6">
                  {t('mentorship.dashboard.noSessionsDesc', 'Schedule your first mentorship session to get started')}
                </p>
                <button
                  onClick={() => navigate('/dashboard/find-mentor')}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Search size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('mentorship.dashboard.findFirstMentor', 'Find Your First Mentor')}
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {sessions.slice(0, 5).map((session) => (
                  <div key={session.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center">
                      <div className="text-2xl mr-4">{getTypeIcon(session.type)}</div>
                      <div>
                        <h4 className="font-medium text-gray-900">{session.topic}</h4>
                        <p className="text-sm text-gray-600">
                          {t('mentorship.session.with', 'with')} {session.mentorName}
                        </p>
                        <div className="flex items-center mt-1 text-sm text-gray-500">
                          <Calendar size={14} className="mr-1" />
                          <span>{formatDate(session.date)} at {session.time}</span>
                          <Clock size={14} className="ml-3 mr-1" />
                          <span>{session.duration} min</span>
                        </div>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                      {t(`mentorship.session.status.${session.status}`, session.status)}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MentorshipPage;
