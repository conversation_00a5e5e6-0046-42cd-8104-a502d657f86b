from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth.models import User
from django.contrib.auth import authenticate, login, logout
from django.db.models import Count, Q
from django.db import transaction
from django.utils import timezone
from rest_framework_simplejwt.tokens import RefreshToken
from ..models import UserProfile, UserRole, UserRoleAssignment
from ..serializers import UserSerializer, UserProfileSerializer, UserRegistrationSerializer
from api.permissions import IsAdminUser


class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer

    def get_permissions(self):
        if self.action == 'create' or self.action == 'login':
            permission_classes = [permissions.AllowAny]
        elif self.action == 'list' or self.action == 'retrieve' or self.action == 'destroy':
            # Only admin users can list all users, get user details, or delete users
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'], permission_classes=[IsAdminUser])
    def admin_dashboard_stats(self, request):
        """Get enhanced statistics for the admin dashboard"""
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count, Sum, Avg, F, Q

        # Basic user statistics
        total_users = User.objects.count()
        active_users = User.objects.filter(is_active=True).count()
        staff_users = User.objects.filter(is_staff=True).count()
        superusers = User.objects.filter(is_superuser=True).count()

        # Time periods for trend analysis
        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)
        sixty_days_ago = now - timedelta(days=60)
        ninety_days_ago = now - timedelta(days=90)

        # User growth metrics
        new_users_last_30_days = User.objects.filter(date_joined__gte=thirty_days_ago).count()
        new_users_last_60_days = User.objects.filter(date_joined__gte=sixty_days_ago).count()
        new_users_last_90_days = User.objects.filter(date_joined__gte=ninety_days_ago).count()

        # User activity metrics
        active_users_last_30_days = User.objects.filter(
            last_login__gte=thirty_days_ago
        ).count()

        # Profile completion metrics
        profiles_with_bio = UserProfile.objects.exclude(bio='').count()
        profiles_with_image = UserProfile.objects.exclude(profile_image='').count()
        complete_profiles = UserProfile.objects.exclude(
            Q(bio='') | Q(location='') | Q(company='')
        ).count()

        return Response({
            'user_statistics': {
                'total_users': total_users,
                'active_users': active_users,
                'staff_users': staff_users,
                'superusers': superusers,
                'new_users_last_30_days': new_users_last_30_days,
                'new_users_last_60_days': new_users_last_60_days,
                'new_users_last_90_days': new_users_last_90_days,
                'active_users_last_30_days': active_users_last_30_days,
            },
            'profile_statistics': {
                'profiles_with_bio': profiles_with_bio,
                'profiles_with_image': profiles_with_image,
                'complete_profiles': complete_profiles,
                'completion_rate': round((complete_profiles / total_users) * 100, 2) if total_users > 0 else 0,
            },
            'growth_trends': {
                'monthly_growth': new_users_last_30_days - (new_users_last_60_days - new_users_last_30_days),
                'quarterly_growth': new_users_last_90_days,
            }
        })

    # Registration endpoint removed - use dedicated auth endpoints in auth_urls.py instead

    @action(detail=False, methods=['post'], permission_classes=[permissions.AllowAny])
    def login(self, request):
        """Login user"""
        username = request.data.get('username')
        password = request.data.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if user.is_active:
                    refresh = RefreshToken.for_user(user)
                    return Response({
                        'user': UserSerializer(user).data,
                        'refresh': str(refresh),
                        'access': str(refresh.access_token),
                    })
                else:
                    return Response({'error': 'Account is disabled'}, 
                                  status=status.HTTP_401_UNAUTHORIZED)
            else:
                return Response({'error': 'Invalid credentials'}, 
                              status=status.HTTP_401_UNAUTHORIZED)
        else:
            return Response({'error': 'Username and password required'}, 
                          status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def logout(self, request):
        """Logout user"""
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response({'message': 'Successfully logged out'})
        except Exception as e:
            return Response({'error': 'Invalid token'}, 
                          status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def profile(self, request):
        """Get current user's profile"""
        serializer = UserSerializer(request.user)
        return Response(serializer.data)

    @action(detail=False, methods=['put', 'patch'], permission_classes=[permissions.IsAuthenticated])
    def update_profile(self, request):
        """Update current user's profile"""
        serializer = UserSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserProfileViewSet(viewsets.ModelViewSet):
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filter profiles based on user permissions"""
        if self.request.user.is_staff:
            return UserProfile.objects.all()
        else:
            # Regular users can only see public profiles and their own
            return UserProfile.objects.filter(
                Q(profile_visibility='public') | 
                Q(user=self.request.user)
            )

    def get_permissions(self):
        """Set permissions based on action"""
        if self.action == 'list' or self.action == 'retrieve':
            permission_classes = [permissions.IsAuthenticated]
        elif self.action in ['update', 'partial_update']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsAdminUser]
        return [permission() for permission in permission_classes]

    def update(self, request, *args, **kwargs):
        """Only allow users to update their own profile"""
        profile = self.get_object()
        if profile.user != request.user and not request.user.is_staff:
            return Response({'error': 'Permission denied'}, 
                          status=status.HTTP_403_FORBIDDEN)
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        """Only allow users to update their own profile"""
        profile = self.get_object()
        if profile.user != request.user and not request.user.is_staff:
            return Response({'error': 'Permission denied'}, 
                          status=status.HTTP_403_FORBIDDEN)
        return super().partial_update(request, *args, **kwargs)

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def upload_image(self, request, pk=None):
        """Upload profile image"""
        profile = self.get_object()
        if profile.user != request.user and not request.user.is_staff:
            return Response({'error': 'Permission denied'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        if 'image' not in request.FILES:
            return Response({'error': 'No image provided'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        profile.profile_image = request.FILES['image']
        profile.save()
        
        serializer = UserProfileSerializer(profile)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def my_profile(self, request):
        """Get current user's profile"""
        try:
            profile = request.user.profile
            serializer = UserProfileSerializer(profile)
            return Response(serializer.data)
        except UserProfile.DoesNotExist:
            return Response({'error': 'Profile not found'}, 
                          status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['put', 'patch'], permission_classes=[permissions.IsAuthenticated])
    def update_my_profile(self, request):
        """Update current user's profile"""
        try:
            profile = request.user.profile
            serializer = UserProfileSerializer(profile, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except UserProfile.DoesNotExist:
            return Response({'error': 'Profile not found'},
                          status=status.HTTP_404_NOT_FOUND)


class AvailableRolesView(APIView):
    """
    API endpoint to get all available roles from the database
    This ensures frontend uses actual database roles, not hardcoded constants
    """
    permission_classes = [permissions.AllowAny]  # Public endpoint for registration

    def get(self, request):
        """Get all available roles with their details"""
        try:
            roles = UserRole.objects.filter(is_active=True).order_by('name')

            roles_data = []
            for role in roles:
                roles_data.append({
                    'id': role.id,
                    'name': role.name,
                    'display_name': role.display_name,
                    'description': role.description,
                    'permission_level': role.permission_level,
                    'requires_approval': role.requires_approval,
                    'is_active': role.is_active
                })

            return Response({
                'success': True,
                'roles': roles_data,
                'total_count': len(roles_data)
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': f'Failed to fetch roles: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserRoleInfoView(APIView):
    """
    API endpoint to get current user's role information
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """Get current user's role information"""
        try:
            user = request.user

            # Get user profile
            try:
                profile = user.profile
            except UserProfile.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'User profile not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get active role assignments
            active_assignments = UserRoleAssignment.objects.filter(
                user_profile=profile,
                is_active=True
            ).select_related('role')

            user_roles = []
            for assignment in active_assignments:
                user_roles.append({
                    'id': assignment.role.id,
                    'name': assignment.role.name,
                    'display_name': assignment.role.display_name,
                    'permission_level': assignment.role.permission_level,
                    'assigned_at': assignment.assigned_at,
                    'expires_at': assignment.expires_at
                })

            # Determine primary role (first active role or 'user' as fallback)
            primary_role = user_roles[0]['name'] if user_roles else 'user'

            return Response({
                'success': True,
                'user_id': user.id,
                'username': user.username,
                'primary_role': primary_role,
                'all_roles': user_roles,
                'is_staff': user.is_staff,
                'is_superuser': user.is_superuser
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': f'Failed to get user role info: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
