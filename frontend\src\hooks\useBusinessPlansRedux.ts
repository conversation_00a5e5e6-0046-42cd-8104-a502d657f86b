/**
 * Business Plans Redux Hooks
 * Provides hooks for interacting with business plans Redux state
 */

import { useCallback, useEffect, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  fetchBusinessPlan,
  fetchBusinessPlanSections,
  updateBusinessPlanSection,
  setActiveSectionId,
  setSectionContent,
  setHasUnsavedChanges,
  clearError,
  clearSectionsError,
  resetBusinessPlanState,
  selectCurrentBusinessPlan,
  selectBusinessPlanSections,
  selectIsLoading
} from '../store/businessPlansSlice';
import { BusinessPlan, BusinessPlanSection } from '../services/businessPlanApi';

/**
 * Hook for managing business plan details
 */
export const useBusinessPlanDetail = (businessPlanId?: number) => {
  const dispatch = useAppDispatch();
  const businessPlan = useAppSelector(selectCurrentBusinessPlan);
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(state => state.businessPlans.error);

  const loadBusinessPlan = useCallback((id: number) => {
    dispatch(fetchBusinessPlan(id));
  }, [dispatch]);

  const updatePlan = useCallback((updates: Partial<BusinessPlan>) => {
    if (businessPlan) {
      // Update logic would go here
      console.log('Updating business plan:', updates);
    }
  }, [businessPlan]);

  useEffect(() => {
    if (businessPlanId && businessPlanId !== businessPlan?.id) {
      loadBusinessPlan(businessPlanId);
    }
  }, [businessPlanId, businessPlan?.id, loadBusinessPlan]);

  return {
    businessPlan,
    isLoading,
    error,
    loadBusinessPlan,
    updatePlan
  };
};

/**
 * Hook for managing business plan sections
 */
export const useBusinessPlanSections = (businessPlanId?: number) => {
  const dispatch = useAppDispatch();
  const sections = useAppSelector(selectBusinessPlanSections);
  const currentSection = useAppSelector(state => state.businessPlans.currentSection);
  const activeSectionId = useAppSelector(state => state.businessPlans.activeSectionId);
  const isLoading = useAppSelector(state => state.businessPlans.isSectionsLoading);
  const error = useAppSelector(state => state.businessPlans.sectionsError);

  const loadSections = useCallback((planId: number) => {
    dispatch(fetchBusinessPlanSections(planId));
  }, [dispatch]);

  const setActiveSection = useCallback((sectionId: number | null) => {
    dispatch(setActiveSectionId(sectionId));
  }, [dispatch]);

  useEffect(() => {
    if (businessPlanId) {
      loadSections(businessPlanId);
    }
  }, [businessPlanId, loadSections]);

  return {
    sections,
    currentSection,
    activeSectionId,
    isLoading,
    error,
    loadSections,
    setActiveSection
  };
};

/**
 * Hook for editing business plan sections
 */
export const useBusinessPlanSectionEditor = () => {
  const dispatch = useAppDispatch();
  const sectionContent = useAppSelector(state => state.businessPlans.sectionContent);
  const hasUnsavedChanges = useAppSelector(state => state.businessPlans.hasUnsavedChanges);
  const isAutoSaving = useAppSelector(state => state.businessPlans.isAutoSaving);
  const autoSaveError = useAppSelector(state => state.businessPlans.autoSaveError);

  const updateContent = useCallback((content: string) => {
    dispatch(setSectionContent(content));
  }, [dispatch]);

  const saveSection = useCallback(async (sectionId: number, content: string) => {
    try {
      await dispatch(updateBusinessPlanSection({ sectionId, content })).unwrap();
      dispatch(setHasUnsavedChanges(false));
    } catch (error) {
      console.error('Failed to save section:', error);
    }
  }, [dispatch]);

  return {
    sectionContent,
    hasUnsavedChanges,
    isAutoSaving,
    autoSaveError,
    updateContent,
    saveSection
  };
};

/**
 * Hook for auto-saving functionality
 */
export const useAutoSave = (sectionId?: number, content?: string, delay: number = 2000) => {
  const dispatch = useAppDispatch();
  const hasUnsavedChanges = useAppSelector(state => state.businessPlans.hasUnsavedChanges);

  useEffect(() => {
    if (!hasUnsavedChanges || !sectionId || !content) return;

    const timeoutId = setTimeout(() => {
      dispatch(updateBusinessPlanSection({ sectionId, content }));
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [dispatch, sectionId, content, hasUnsavedChanges, delay]);

  return {
    hasUnsavedChanges
  };
};

/**
 * Hook for keyboard shortcuts in business plan editor
 */
export const useBusinessPlanKeyboardShortcuts = (callbacks: {
  onSave?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
}) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            callbacks.onSave?.();
            break;
          case 'z':
            if (event.shiftKey) {
              event.preventDefault();
              callbacks.onRedo?.();
            } else {
              event.preventDefault();
              callbacks.onUndo?.();
            }
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [callbacks]);
};

/**
 * Hook for cleaning up business plan state
 */
export const useBusinessPlanCleanup = () => {
  const dispatch = useAppDispatch();

  const cleanup = useCallback(() => {
    dispatch(resetBusinessPlanState());
  }, [dispatch]);

  const clearErrors = useCallback(() => {
    dispatch(clearError());
    dispatch(clearSectionsError());
  }, [dispatch]);

  return {
    cleanup,
    clearErrors
  };
};
