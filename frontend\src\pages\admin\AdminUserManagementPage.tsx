/**
 * Admin User Management Page
 * Comprehensive interface for managing users and role applications
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicInput } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Users, 
  Search, 
  Filter, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock,
  Mail,
  Phone,
  MapPin,
  Calendar,
  MoreHorizontal,
  UserCheck,
  UserX,
  Shield,
  DollarSign,
  AlertCircle,
  Download,
  RefreshCw
} from 'lucide-react';

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location: string;
  registeredAt: string;
  emailVerified: boolean;
  status: 'active' | 'pending' | 'suspended';
  roles: Array<{
    role: 'user' | 'mentor' | 'investor' | 'moderator' | 'admin' | 'super_admin';
    status: 'active' | 'pending' | 'rejected';
    appliedAt: string;
    approvedAt?: string;
    approvedBy?: string;
  }>;
  lastActive?: string;
}

interface FilterOptions {
  status: string;
  role: string;
  emailVerified: string;
  dateRange: string;
  search: string;
}

const AdminUserManagementPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    status: '',
    role: '',
    emailVerified: '',
    dateRange: '',
    search: ''
  });

  useEffect(() => {
    loadUsers();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [filters, users]);

  const loadUsers = async () => {
    try {
      // Mock data - replace with actual API call
      const mockUsers: User[] = [
        {
          id: '1',
          firstName: 'Ahmed',
          lastName: 'Al-Rashid',
          email: '<EMAIL>',
          phone: '+966501234567',
          location: 'Riyadh, Saudi Arabia',
          registeredAt: '2024-01-20T10:30:00Z',
          emailVerified: true,
          status: 'active',
          roles: [
            { role: 'user', status: 'active', appliedAt: '2024-01-20T10:30:00Z', approvedAt: '2024-01-20T10:30:00Z' },
            { role: 'mentor', status: 'pending', appliedAt: '2024-01-20T10:30:00Z' }
          ],
          lastActive: '2024-01-22T15:45:00Z'
        },
        {
          id: '2',
          firstName: 'Sarah',
          lastName: 'Johnson',
          email: '<EMAIL>',
          location: 'Dubai, UAE',
          registeredAt: '2024-01-21T14:20:00Z',
          emailVerified: true,
          status: 'active',
          roles: [
            { role: 'user', status: 'active', appliedAt: '2024-01-21T14:20:00Z', approvedAt: '2024-01-21T14:20:00Z' },
            { role: 'investor', status: 'pending', appliedAt: '2024-01-21T14:20:00Z' }
          ],
          lastActive: '2024-01-22T09:30:00Z'
        },
        {
          id: '3',
          firstName: 'Omar',
          lastName: 'Hassan',
          email: '<EMAIL>',
          location: 'Cairo, Egypt',
          registeredAt: '2024-01-22T08:15:00Z',
          emailVerified: false,
          status: 'pending',
          roles: [
            { role: 'user', status: 'active', appliedAt: '2024-01-22T08:15:00Z', approvedAt: '2024-01-22T08:15:00Z' },
            { role: 'moderator', status: 'pending', appliedAt: '2024-01-22T08:15:00Z' }
          ]
        }
      ];

      setUsers(mockUsers);
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = users;

    // Search filter
    if (filters.search) {
      filtered = filtered.filter(user =>
        user.firstName.toLowerCase().includes(filters.search.toLowerCase()) ||
        user.lastName.toLowerCase().includes(filters.search.toLowerCase()) ||
        user.email.toLowerCase().includes(filters.search.toLowerCase())
      );
    }

    // Status filter
    if (filters.status) {
      filtered = filtered.filter(user => user.status === filters.status);
    }

    // Role filter
    if (filters.role) {
      filtered = filtered.filter(user => 
        user.roles.some(role => role.role === filters.role)
      );
    }

    // Email verified filter
    if (filters.emailVerified) {
      filtered = filtered.filter(user => 
        filters.emailVerified === 'true' ? user.emailVerified : !user.emailVerified
      );
    }

    setFilteredUsers(filtered);
  };

  const handleApproveRole = async (userId: string, role: string) => {
    try {
      // Approve role - replace with actual API call
      console.log('Approving role:', userId, role);
      
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? {
              ...user,
              roles: user.roles.map(r => 
                r.role === role 
                  ? { ...r, status: 'active' as const, approvedAt: new Date().toISOString(), approvedBy: 'current-admin' }
                  : r
              )
            }
          : user
      ));
    } catch (error) {
      console.error('Error approving role:', error);
    }
  };

  const handleRejectRole = async (userId: string, role: string) => {
    try {
      // Reject role - replace with actual API call
      console.log('Rejecting role:', userId, role);
      
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? {
              ...user,
              roles: user.roles.map(r => 
                r.role === role 
                  ? { ...r, status: 'rejected' as const, approvedAt: new Date().toISOString(), approvedBy: 'current-admin' }
                  : r
              )
            }
          : user
      ));
    } catch (error) {
      console.error('Error rejecting role:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'mentor':
        return <Users className="w-4 h-4" />;
      case 'investor':
        return <DollarSign className="w-4 h-4" />;
      case 'moderator':
        return <Shield className="w-4 h-4" />;
      case 'admin':
      case 'super_admin':
        return <UserCheck className="w-4 h-4" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  const getRoleColor = (role: string, status: string) => {
    if (status === 'pending') return 'bg-yellow-100 text-yellow-800';
    if (status === 'rejected') return 'bg-red-100 text-red-800';
    
    switch (role) {
      case 'mentor':
        return 'bg-blue-100 text-blue-800';
      case 'investor':
        return 'bg-green-100 text-green-800';
      case 'moderator':
        return 'bg-purple-100 text-purple-800';
      case 'admin':
      case 'super_admin':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const pendingApplicationsCount = users.reduce((count, user) => {
    return count + user.roles.filter(role => role.status === 'pending').length;
  }, 0);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div>
              <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                {language === 'ar' ? 'إدارة المستخدمين' : 'User Management'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' 
                  ? 'إدارة حسابات المستخدمين وطلبات الأدوار'
                  : 'Manage user accounts and role applications'
                }
              </ArabicTypography>
            </div>
            
            <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <ArabicButton
                variant="outline"
                onClick={() => loadUsers()}
                icon={<RefreshCw className="w-4 h-4" />}
              >
                {language === 'ar' ? 'تحديث' : 'Refresh'}
              </ArabicButton>
              
              <ArabicButton
                variant="outline"
                icon={<Download className="w-4 h-4" />}
              >
                {language === 'ar' ? 'تصدير' : 'Export'}
              </ArabicButton>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`p-3 rounded-lg bg-blue-100 ${isRTL ? 'ml-4' : 'mr-4'}`}>
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <ArabicTypography variant="h3" className="font-bold text-gray-900">
                  {users.length}
                </ArabicTypography>
                <ArabicTypography variant="body2" color="secondary">
                  {language === 'ar' ? 'إجمالي المستخدمين' : 'Total Users'}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`p-3 rounded-lg bg-yellow-100 ${isRTL ? 'ml-4' : 'mr-4'}`}>
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <ArabicTypography variant="h3" className="font-bold text-gray-900">
                  {pendingApplicationsCount}
                </ArabicTypography>
                <ArabicTypography variant="body2" color="secondary">
                  {language === 'ar' ? 'طلبات معلقة' : 'Pending Applications'}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`p-3 rounded-lg bg-green-100 ${isRTL ? 'ml-4' : 'mr-4'}`}>
                <UserCheck className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <ArabicTypography variant="h3" className="font-bold text-gray-900">
                  {users.filter(u => u.status === 'active').length}
                </ArabicTypography>
                <ArabicTypography variant="body2" color="secondary">
                  {language === 'ar' ? 'مستخدمين نشطين' : 'Active Users'}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`p-3 rounded-lg bg-red-100 ${isRTL ? 'ml-4' : 'mr-4'}`}>
                <AlertCircle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <ArabicTypography variant="h3" className="font-bold text-gray-900">
                  {users.filter(u => !u.emailVerified).length}
                </ArabicTypography>
                <ArabicTypography variant="body2" color="secondary">
                  {language === 'ar' ? 'غير مؤكد البريد' : 'Unverified Email'}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>
        </div>

        {/* Filters and Search */}
        <ArabicCard className="mb-6">
          <div className={`flex flex-col md:flex-row gap-4 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
            <div className="flex-1">
              <ArabicInput
                placeholder={language === 'ar' ? 'البحث عن المستخدمين...' : 'Search users...'}
                value={filters.search}
                onChange={(value) => setFilters({...filters, search: value})}
                icon={<Search className="w-5 h-5" />}
                iconPosition="start"
              />
            </div>
            
            <ArabicButton
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              icon={<Filter className="w-4 h-4" />}
            >
              {language === 'ar' ? 'تصفية' : 'Filters'}
            </ArabicButton>
          </div>

          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.status}
                    onChange={(e) => setFilters({...filters, status: e.target.value})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'جميع الحالات' : 'All Statuses'}</option>
                    <option value="active">{language === 'ar' ? 'نشط' : 'Active'}</option>
                    <option value="pending">{language === 'ar' ? 'معلق' : 'Pending'}</option>
                    <option value="suspended">{language === 'ar' ? 'معلق' : 'Suspended'}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'الدور' : 'Role'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.role}
                    onChange={(e) => setFilters({...filters, role: e.target.value})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'جميع الأدوار' : 'All Roles'}</option>
                    <option value="user">{language === 'ar' ? 'مستخدم' : 'User'}</option>
                    <option value="mentor">{language === 'ar' ? 'موجه' : 'Mentor'}</option>
                    <option value="investor">{language === 'ar' ? 'مستثمر' : 'Investor'}</option>
                    <option value="moderator">{language === 'ar' ? 'مشرف' : 'Moderator'}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'تأكيد البريد' : 'Email Verified'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.emailVerified}
                    onChange={(e) => setFilters({...filters, emailVerified: e.target.value})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'الكل' : 'All'}</option>
                    <option value="true">{language === 'ar' ? 'مؤكد' : 'Verified'}</option>
                    <option value="false">{language === 'ar' ? 'غير مؤكد' : 'Unverified'}</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <ArabicButton
                    variant="outline"
                    onClick={() => setFilters({
                      status: '',
                      role: '',
                      emailVerified: '',
                      dateRange: '',
                      search: ''
                    })}
                    className="w-full"
                  >
                    {language === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
                  </ArabicButton>
                </div>
              </div>
            </div>
          )}
        </ArabicCard>

        {/* Users Table */}
        <ArabicCard>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className={`py-3 px-4 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'المستخدم' : 'User'}
                  </th>
                  <th className={`py-3 px-4 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'الأدوار' : 'Roles'}
                  </th>
                  <th className={`py-3 px-4 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </th>
                  <th className={`py-3 px-4 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'تاريخ التسجيل' : 'Registered'}
                  </th>
                  <th className={`py-3 px-4 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'الإجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'}`}>
                          <span className="text-sm font-bold text-blue-600">
                            {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <ArabicTypography variant="body2" className="font-semibold">
                            {user.firstName} {user.lastName}
                          </ArabicTypography>
                          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <Mail className={`w-3 h-3 text-gray-400 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                            <ArabicTypography variant="caption" color="secondary">
                              {user.email}
                            </ArabicTypography>
                            {!user.emailVerified && (
                              <AlertCircle className={`w-3 h-3 text-red-500 ${isRTL ? 'mr-1' : 'ml-1'}`} />
                            )}
                          </div>
                          {user.location && (
                            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <MapPin className={`w-3 h-3 text-gray-400 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                              <ArabicTypography variant="caption" color="secondary">
                                {user.location}
                              </ArabicTypography>
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    
                    <td className="py-4 px-4">
                      <div className="flex flex-wrap gap-1">
                        {user.roles.map((role, index) => (
                          <span
                            key={index}
                            className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${getRoleColor(role.role, role.status)} font-arabic`}
                          >
                            {getRoleIcon(role.role)}
                            <span className={isRTL ? 'mr-1' : 'ml-1'}>
                              {language === 'ar' 
                                ? (role.role === 'user' ? 'مستخدم' : 
                                   role.role === 'mentor' ? 'موجه' :
                                   role.role === 'investor' ? 'مستثمر' :
                                   role.role === 'moderator' ? 'مشرف' : role.role)
                                : role.role
                              }
                            </span>
                            {role.status === 'pending' && (
                              <Clock className={`w-3 h-3 ${isRTL ? 'mr-1' : 'ml-1'}`} />
                            )}
                          </span>
                        ))}
                      </div>
                    </td>
                    
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(user.status)} font-arabic`}>
                        {language === 'ar' 
                          ? (user.status === 'active' ? 'نشط' : 
                             user.status === 'pending' ? 'معلق' : 
                             user.status === 'suspended' ? 'معلق' : user.status)
                          : user.status
                        }
                      </span>
                    </td>
                    
                    <td className="py-4 px-4">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Calendar className={`w-3 h-3 text-gray-400 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <ArabicTypography variant="caption" color="secondary">
                          {new Date(user.registeredAt).toLocaleDateString()}
                        </ArabicTypography>
                      </div>
                    </td>
                    
                    <td className="py-4 px-4">
                      <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                        <ArabicButton
                          size="sm"
                          variant="outline"
                          onClick={() => navigate(`/admin/users/${user.id}`)}
                          icon={<Eye className="w-3 h-3" />}
                        >
                          {language === 'ar' ? 'عرض' : 'View'}
                        </ArabicButton>
                        
                        {user.roles.some(role => role.status === 'pending') && (
                          <>
                            <ArabicButton
                              size="sm"
                              onClick={() => {
                                const pendingRole = user.roles.find(role => role.status === 'pending');
                                if (pendingRole) {
                                  handleApproveRole(user.id, pendingRole.role);
                                }
                              }}
                              icon={<CheckCircle className="w-3 h-3" />}
                            >
                              {language === 'ar' ? 'موافقة' : 'Approve'}
                            </ArabicButton>
                            
                            <ArabicButton
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const pendingRole = user.roles.find(role => role.status === 'pending');
                                if (pendingRole) {
                                  handleRejectRole(user.id, pendingRole.role);
                                }
                              }}
                              icon={<XCircle className="w-3 h-3" />}
                            >
                              {language === 'ar' ? 'رفض' : 'Reject'}
                            </ArabicButton>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <ArabicTypography variant="h4" className="text-gray-500 mb-2">
                {language === 'ar' ? 'لا توجد مستخدمين' : 'No users found'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' 
                  ? 'جرب تعديل معايير البحث أو الفلاتر'
                  : 'Try adjusting your search criteria or filters'
                }
              </ArabicTypography>
            </div>
          )}
        </ArabicCard>
      </div>
    </div>
  );
};

export default AdminUserManagementPage;
