/**
 * Mentor Discovery Page
 * Find and connect with mentors based on industry, expertise, and compatibility
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicInput } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Search, 
  Filter, 
  Star, 
  MapPin, 
  Briefcase, 
  Users, 
  Clock,
  MessageSquare,
  Award,
  TrendingUp,
  Heart,
  Eye,
  Calendar
} from 'lucide-react';

interface Mentor {
  id: string;
  name: string;
  title: string;
  company: string;
  industry: string[];
  expertise: string[];
  location: string;
  experience: number;
  rating: number;
  reviewCount: number;
  menteeCount: number;
  successStories: number;
  languages: string[];
  availability: 'available' | 'limited' | 'unavailable';
  hourlyRate?: number;
  bio: string;
  bioAr: string;
  avatar?: string;
  matchScore?: number;
  responseTime: string;
  sessionTypes: string[];
}

interface FilterOptions {
  industry: string;
  expertise: string;
  location: string;
  availability: string;
  rating: number;
  experience: number;
  language: string;
}

const MentorDiscoveryPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [mentors, setMentors] = useState<Mentor[]>([]);
  const [filteredMentors, setFilteredMentors] = useState<Mentor[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    industry: '',
    expertise: '',
    location: '',
    availability: '',
    rating: 0,
    experience: 0,
    language: ''
  });

  useEffect(() => {
    loadMentors();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [searchQuery, filters, mentors]);

  const loadMentors = async () => {
    try {
      // Mock data - replace with actual API call
      const mockMentors: Mentor[] = [
        {
          id: '1',
          name: 'Dr. Ahmed Al-Rashid',
          title: 'Senior FinTech Advisor',
          company: 'MENA Ventures',
          industry: ['FinTech', 'Banking'],
          expertise: ['Digital Payments', 'Blockchain', 'Islamic Finance', 'Regulatory Compliance'],
          location: 'Riyadh, Saudi Arabia',
          experience: 15,
          rating: 4.9,
          reviewCount: 47,
          menteeCount: 23,
          successStories: 18,
          languages: ['Arabic', 'English'],
          availability: 'available',
          hourlyRate: 150,
          bio: 'Former VP of Digital Innovation at Saudi National Bank with 15+ years in FinTech',
          bioAr: 'نائب رئيس سابق للابتكار الرقمي في البنك الأهلي السعودي مع أكثر من 15 عامًا في التكنولوجيا المالية',
          matchScore: 95,
          responseTime: '< 2 hours',
          sessionTypes: ['Video Call', 'Phone', 'In-Person']
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          title: 'HealthTech Innovation Lead',
          company: 'Dubai Health Authority',
          industry: ['HealthTech', 'Digital Health'],
          expertise: ['Telemedicine', 'Health Data Analytics', 'Medical Devices', 'Healthcare AI'],
          location: 'Dubai, UAE',
          experience: 12,
          rating: 4.8,
          reviewCount: 32,
          menteeCount: 19,
          successStories: 14,
          languages: ['English', 'Arabic'],
          availability: 'limited',
          hourlyRate: 120,
          bio: 'Leading digital transformation in healthcare across MENA region',
          bioAr: 'تقود التحول الرقمي في الرعاية الصحية عبر منطقة الشرق الأوسط وشمال أفريقيا',
          matchScore: 88,
          responseTime: '< 4 hours',
          sessionTypes: ['Video Call', 'Phone']
        },
        {
          id: '3',
          name: 'Omar Hassan',
          title: 'EdTech Entrepreneur',
          company: 'EduMENA',
          industry: ['EdTech', 'E-Learning'],
          expertise: ['Online Learning Platforms', 'Educational Content', 'Student Analytics', 'Arabic EdTech'],
          location: 'Cairo, Egypt',
          experience: 8,
          rating: 4.7,
          reviewCount: 28,
          menteeCount: 15,
          successStories: 11,
          languages: ['Arabic', 'English', 'French'],
          availability: 'available',
          hourlyRate: 80,
          bio: 'Founded 3 successful EdTech startups, expert in Arabic educational content',
          bioAr: 'أسس 3 شركات ناشئة ناجحة في التكنولوجيا التعليمية، خبير في المحتوى التعليمي العربي',
          matchScore: 82,
          responseTime: '< 6 hours',
          sessionTypes: ['Video Call', 'Phone', 'Chat']
        },
        {
          id: '4',
          name: 'Fatima Al-Zahra',
          title: 'Venture Capital Partner',
          company: 'MENA Growth Fund',
          industry: ['Investment', 'Venture Capital'],
          expertise: ['Startup Funding', 'Due Diligence', 'Business Strategy', 'Market Expansion'],
          location: 'Beirut, Lebanon',
          experience: 10,
          rating: 4.9,
          reviewCount: 41,
          menteeCount: 27,
          successStories: 22,
          languages: ['Arabic', 'English', 'French'],
          availability: 'available',
          hourlyRate: 200,
          bio: 'Led investments in 50+ MENA startups with $100M+ in funding',
          bioAr: 'قادت استثمارات في أكثر من 50 شركة ناشئة في المنطقة بتمويل يزيد عن 100 مليون دولار',
          matchScore: 91,
          responseTime: '< 1 hour',
          sessionTypes: ['Video Call', 'In-Person']
        }
      ];

      setMentors(mockMentors);
      setFilteredMentors(mockMentors);
    } catch (error) {
      console.error('Error loading mentors:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = mentors;

    // Search query filter
    if (searchQuery) {
      filtered = filtered.filter(mentor =>
        mentor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        mentor.expertise.some(exp => exp.toLowerCase().includes(searchQuery.toLowerCase())) ||
        mentor.industry.some(ind => ind.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Industry filter
    if (filters.industry) {
      filtered = filtered.filter(mentor =>
        mentor.industry.includes(filters.industry)
      );
    }

    // Availability filter
    if (filters.availability) {
      filtered = filtered.filter(mentor =>
        mentor.availability === filters.availability
      );
    }

    // Rating filter
    if (filters.rating > 0) {
      filtered = filtered.filter(mentor =>
        mentor.rating >= filters.rating
      );
    }

    // Experience filter
    if (filters.experience > 0) {
      filtered = filtered.filter(mentor =>
        mentor.experience >= filters.experience
      );
    }

    setFilteredMentors(filtered);
  };

  const handleConnect = (mentorId: string) => {
    navigate(`/dashboard/mentorship/connect/${mentorId}`);
  };

  const handleViewProfile = (mentorId: string) => {
    navigate(`/dashboard/mentorship/mentor/${mentorId}`);
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'available':
        return 'text-green-600 bg-green-100';
      case 'limited':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-red-600 bg-red-100';
    }
  };

  const getAvailabilityText = (availability: string) => {
    const texts = {
      en: {
        available: 'Available',
        limited: 'Limited',
        unavailable: 'Unavailable'
      },
      ar: {
        available: 'متاح',
        limited: 'محدود',
        unavailable: 'غير متاح'
      }
    };
    return texts[language][availability as keyof typeof texts.en] || availability;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <ArabicTypography variant="h1" className="text-gray-900 font-bold mb-2">
            {language === 'ar' ? 'اكتشف الموجهين' : 'Discover Mentors'}
          </ArabicTypography>
          <ArabicTypography variant="body1" color="secondary">
            {language === 'ar' 
              ? 'تواصل مع خبراء الصناعة وقادة الأعمال لتوجيه رحلة ريادة الأعمال'
              : 'Connect with industry experts and business leaders to guide your entrepreneurial journey'
            }
          </ArabicTypography>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8">
          <div className={`flex flex-col md:flex-row gap-4 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
            <div className="flex-1">
              <ArabicInput
                placeholder={language === 'ar' ? 'ابحث عن موجهين حسب الاسم أو الخبرة أو الصناعة...' : 'Search mentors by name, expertise, or industry...'}
                value={searchQuery}
                onChange={setSearchQuery}
                icon={<Search className="w-5 h-5" />}
                iconPosition="start"
              />
            </div>
            <ArabicButton
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              icon={<Filter className="w-4 h-4" />}
            >
              {language === 'ar' ? 'تصفية' : 'Filters'}
            </ArabicButton>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <ArabicCard className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'الصناعة' : 'Industry'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.industry}
                    onChange={(e) => setFilters({...filters, industry: e.target.value})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'جميع الصناعات' : 'All Industries'}</option>
                    <option value="FinTech">{language === 'ar' ? 'التكنولوجيا المالية' : 'FinTech'}</option>
                    <option value="HealthTech">{language === 'ar' ? 'التكنولوجيا الصحية' : 'HealthTech'}</option>
                    <option value="EdTech">{language === 'ar' ? 'التكنولوجيا التعليمية' : 'EdTech'}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'التوفر' : 'Availability'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.availability}
                    onChange={(e) => setFilters({...filters, availability: e.target.value})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'جميع الحالات' : 'All Availability'}</option>
                    <option value="available">{language === 'ar' ? 'متاح' : 'Available'}</option>
                    <option value="limited">{language === 'ar' ? 'محدود' : 'Limited'}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'الحد الأدنى للتقييم' : 'Minimum Rating'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.rating}
                    onChange={(e) => setFilters({...filters, rating: Number(e.target.value)})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="0">{language === 'ar' ? 'جميع التقييمات' : 'All Ratings'}</option>
                    <option value="4">4+ ⭐</option>
                    <option value="4.5">4.5+ ⭐</option>
                    <option value="4.8">4.8+ ⭐</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'سنوات الخبرة' : 'Experience Years'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.experience}
                    onChange={(e) => setFilters({...filters, experience: Number(e.target.value)})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="0">{language === 'ar' ? 'جميع المستويات' : 'All Levels'}</option>
                    <option value="5">5+ {language === 'ar' ? 'سنوات' : 'years'}</option>
                    <option value="10">10+ {language === 'ar' ? 'سنوات' : 'years'}</option>
                    <option value="15">15+ {language === 'ar' ? 'سنوات' : 'years'}</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <ArabicButton
                    variant="outline"
                    onClick={() => setFilters({
                      industry: '',
                      expertise: '',
                      location: '',
                      availability: '',
                      rating: 0,
                      experience: 0,
                      language: ''
                    })}
                    className="w-full"
                  >
                    {language === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
                  </ArabicButton>
                </div>
              </div>
            </ArabicCard>
          )}
        </div>

        {/* Results Header */}
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicTypography variant="h4" className="text-gray-900 font-semibold">
            {language === 'ar' 
              ? `${filteredMentors.length} موجه متاح`
              : `${filteredMentors.length} mentors available`
            }
          </ArabicTypography>
          <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
            <span className="text-sm text-gray-500 font-arabic">
              {language === 'ar' ? 'ترتيب حسب:' : 'Sort by:'}
            </span>
            <select className="text-sm border border-gray-300 rounded px-2 py-1 font-arabic">
              <option>{language === 'ar' ? 'الأفضل تطابقاً' : 'Best Match'}</option>
              <option>{language === 'ar' ? 'أعلى تقييم' : 'Highest Rated'}</option>
              <option>{language === 'ar' ? 'الأكثر خبرة' : 'Most Experienced'}</option>
            </select>
          </div>
        </div>

        {/* Mentors Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMentors.map((mentor) => (
            <ArabicCard key={mentor.id} className="hover:shadow-lg transition-shadow">
              <div className="text-center mb-4">
                <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl font-bold text-blue-600">
                    {mentor.name.charAt(0)}
                  </span>
                </div>
                
                <ArabicTypography variant="h5" className="text-gray-900 font-bold mb-1">
                  {mentor.name}
                </ArabicTypography>
                
                <ArabicTypography variant="body2" color="secondary" className="mb-2">
                  {mentor.title}
                </ArabicTypography>
                
                <ArabicTypography variant="body2" className="text-blue-600 font-semibold">
                  {mentor.company}
                </ArabicTypography>
              </div>

              {/* Match Score */}
              {mentor.matchScore && (
                <div className="mb-4 text-center">
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${
                    mentor.matchScore >= 90 ? 'bg-green-100 text-green-800' :
                    mentor.matchScore >= 80 ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    <Heart className={`w-4 h-4 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                    <span className="font-arabic">
                      {mentor.matchScore}% {language === 'ar' ? 'تطابق' : 'match'}
                    </span>
                  </div>
                </div>
              )}

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                <div>
                  <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span className={`text-sm font-semibold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                      {mentor.rating}
                    </span>
                  </div>
                  <ArabicTypography variant="caption" color="secondary">
                    ({mentor.reviewCount})
                  </ArabicTypography>
                </div>
                
                <div>
                  <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Users className="w-4 h-4 text-blue-500" />
                    <span className={`text-sm font-semibold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                      {mentor.menteeCount}
                    </span>
                  </div>
                  <ArabicTypography variant="caption" color="secondary">
                    {language === 'ar' ? 'متدرب' : 'mentees'}
                  </ArabicTypography>
                </div>
                
                <div>
                  <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Award className="w-4 h-4 text-green-500" />
                    <span className={`text-sm font-semibold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                      {mentor.successStories}
                    </span>
                  </div>
                  <ArabicTypography variant="caption" color="secondary">
                    {language === 'ar' ? 'نجاح' : 'success'}
                  </ArabicTypography>
                </div>
              </div>

              {/* Expertise Tags */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {mentor.expertise.slice(0, 3).map((skill, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full font-arabic"
                    >
                      {skill}
                    </span>
                  ))}
                  {mentor.expertise.length > 3 && (
                    <span className="px-2 py-1 bg-gray-50 text-gray-600 text-xs rounded-full font-arabic">
                      +{mentor.expertise.length - 3}
                    </span>
                  )}
                </div>
              </div>

              {/* Availability and Response Time */}
              <div className="mb-4 space-y-2">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-sm text-gray-600 font-arabic">
                    {language === 'ar' ? 'التوفر:' : 'Availability:'}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getAvailabilityColor(mentor.availability)} font-arabic`}>
                    {getAvailabilityText(mentor.availability)}
                  </span>
                </div>
                
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-sm text-gray-600 font-arabic">
                    {language === 'ar' ? 'وقت الرد:' : 'Response time:'}
                  </span>
                  <span className="text-sm font-semibold text-gray-900 font-arabic">
                    {mentor.responseTime}
                  </span>
                </div>
              </div>

              {/* Hourly Rate */}
              {mentor.hourlyRate && (
                <div className="mb-4 text-center">
                  <ArabicTypography variant="body1" className="font-bold text-gray-900">
                    ${mentor.hourlyRate}/{language === 'ar' ? 'ساعة' : 'hour'}
                  </ArabicTypography>
                </div>
              )}

              {/* Action Buttons */}
              <div className={`flex space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                <ArabicButton
                  size="sm"
                  className="flex-1"
                  onClick={() => handleConnect(mentor.id)}
                  icon={<MessageSquare className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'تواصل' : 'Connect'}
                </ArabicButton>
                
                <ArabicButton
                  size="sm"
                  variant="outline"
                  onClick={() => handleViewProfile(mentor.id)}
                  icon={<Eye className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'عرض' : 'View'}
                </ArabicButton>
              </div>
            </ArabicCard>
          ))}
        </div>

        {/* No Results */}
        {filteredMentors.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <ArabicTypography variant="h4" className="text-gray-500 mb-2">
              {language === 'ar' ? 'لم يتم العثور على موجهين' : 'No mentors found'}
            </ArabicTypography>
            <ArabicTypography variant="body1" color="secondary">
              {language === 'ar' 
                ? 'جرب تعديل معايير البحث أو الفلاتر'
                : 'Try adjusting your search criteria or filters'
              }
            </ArabicTypography>
          </div>
        )}
      </div>
    </div>
  );
};

export default MentorDiscoveryPage;
