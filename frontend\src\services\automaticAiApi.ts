/**
 * Automatic AI API Service
 * Handles automatic AI operations and utilities for admin management
 */

import { api } from './api';

export interface AISystemStatus {
  status: 'online' | 'offline' | 'maintenance' | 'error';
  uptime: number;
  lastCheck: string;
  version: string;
  activeConnections: number;
  responseTime: number;
  errorRate: number;
}

export interface AIServiceMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  peakUsage: number;
  currentLoad: number;
  queueSize: number;
}

export interface AIModelInfo {
  name: string;
  version: string;
  status: 'active' | 'inactive' | 'training' | 'error';
  accuracy: number;
  lastTrained: string;
  requestCount: number;
  errorCount: number;
}

export interface AIConfiguration {
  maxConcurrentRequests: number;
  timeoutDuration: number;
  retryAttempts: number;
  enableAutoScaling: boolean;
  enableLogging: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  rateLimitPerUser: number;
  rateLimitWindow: number;
}

/**
 * Automatic AI Utilities
 */
export const automaticAiUtils = {
  /**
   * Get AI system status
   */
  async getSystemStatus(): Promise<AISystemStatus> {
    try {
      const response = await api.get('/ai/system/status/');
      return response;
    } catch (error) {
      console.error('Failed to get AI system status:', error);
      // Return default status on error
      return {
        status: 'error',
        uptime: 0,
        lastCheck: new Date().toISOString(),
        version: 'unknown',
        activeConnections: 0,
        responseTime: 0,
        errorRate: 100
      };
    }
  },

  /**
   * Get AI service metrics
   */
  async getServiceMetrics(): Promise<AIServiceMetrics> {
    try {
      const response = await api.get('/ai/system/metrics/');
      return response;
    } catch (error) {
      console.error('Failed to get AI service metrics:', error);
      return {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        peakUsage: 0,
        currentLoad: 0,
        queueSize: 0
      };
    }
  },

  /**
   * Get AI model information
   */
  async getModelInfo(): Promise<AIModelInfo[]> {
    try {
      const response = await api.get('/ai/models/info/');
      return response.models || [];
    } catch (error) {
      console.error('Failed to get AI model info:', error);
      return [
        {
          name: 'Gemini Pro',
          version: '1.0',
          status: 'active',
          accuracy: 85,
          lastTrained: new Date().toISOString(),
          requestCount: 0,
          errorCount: 0
        }
      ];
    }
  },

  /**
   * Get AI configuration
   */
  async getConfiguration(): Promise<AIConfiguration> {
    try {
      const response = await api.get('/ai/system/config/');
      return response;
    } catch (error) {
      console.error('Failed to get AI configuration:', error);
      return {
        maxConcurrentRequests: 10,
        timeoutDuration: 30000,
        retryAttempts: 3,
        enableAutoScaling: true,
        enableLogging: true,
        logLevel: 'info',
        rateLimitPerUser: 100,
        rateLimitWindow: 3600
      };
    }
  },

  /**
   * Update AI configuration
   */
  async updateConfiguration(config: Partial<AIConfiguration>): Promise<AIConfiguration> {
    try {
      const response = await api.patch('/ai/system/config/', config);
      return response;
    } catch (error) {
      console.error('Failed to update AI configuration:', error);
      throw error;
    }
  },

  /**
   * Restart AI service
   */
  async restartService(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await api.post('/ai/system/restart/');
      return response;
    } catch (error) {
      console.error('Failed to restart AI service:', error);
      return {
        success: false,
        message: 'Failed to restart AI service'
      };
    }
  },

  /**
   * Clear AI cache
   */
  async clearCache(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await api.post('/ai/system/clear-cache/');
      return response;
    } catch (error) {
      console.error('Failed to clear AI cache:', error);
      return {
        success: false,
        message: 'Failed to clear AI cache'
      };
    }
  },

  /**
   * Get AI usage statistics
   */
  async getUsageStats(timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<any> {
    try {
      const response = await api.get(`/ai/system/usage-stats/?timeframe=${timeframe}`);
      return response;
    } catch (error) {
      console.error('Failed to get AI usage stats:', error);
      return {
        timeframe,
        totalRequests: 0,
        uniqueUsers: 0,
        averageResponseTime: 0,
        errorRate: 0,
        data: []
      };
    }
  },

  /**
   * Test AI connectivity
   */
  async testConnectivity(): Promise<{ success: boolean; responseTime: number; message: string }> {
    const startTime = Date.now();
    try {
      await api.get('/ai/system/ping/');
      const responseTime = Date.now() - startTime;
      return {
        success: true,
        responseTime,
        message: 'AI service is responding normally'
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        success: false,
        responseTime,
        message: 'AI service is not responding'
      };
    }
  },

  /**
   * Get AI error logs
   */
  async getErrorLogs(limit: number = 50): Promise<any[]> {
    try {
      const response = await api.get(`/ai/system/error-logs/?limit=${limit}`);
      return response.logs || [];
    } catch (error) {
      console.error('Failed to get AI error logs:', error);
      return [];
    }
  },

  /**
   * Enable/disable AI service
   */
  async toggleService(enabled: boolean): Promise<{ success: boolean; message: string }> {
    try {
      const response = await api.post('/ai/system/toggle/', { enabled });
      return response;
    } catch (error) {
      console.error('Failed to toggle AI service:', error);
      return {
        success: false,
        message: 'Failed to toggle AI service'
      };
    }
  }
};

export default automaticAiUtils;
