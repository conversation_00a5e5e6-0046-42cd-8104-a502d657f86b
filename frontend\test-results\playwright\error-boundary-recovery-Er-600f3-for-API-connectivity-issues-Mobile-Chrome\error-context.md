# Page snapshot

```yaml
- link "Yasmeen AI":
  - /url: /
  - heading "Yasmeen AI" [level=1]
- heading "Create Your Account" [level=2]
- paragraph: Join the leading entrepreneurship platform in the region
- text: Step 1 of 4 25%
- img
- heading "Basic Information" [level=3]
- paragraph: Enter your personal information
- text: First Name *
- textbox "Enter first name": Test
- text: Last Name *
- textbox "Enter last name": User
- text: Username *
- img
- textbox "Enter username": testuser
- text: Email Address *
- img
- textbox "Enter email address": <EMAIL>
- text: Password *
- img
- textbox "Enter password": testpass123
- button "Show password":
  - img
- paragraph: Password must be at least 8 characters
- text: Confirm Password *
- img
- textbox "Confirm password": testpass123
- button "Show confirm password":
  - img
- text: Phone Number (Optional)
- img
- textbox "Enter phone number"
- text: Location *
- img
- textbox "e.g., Riyadh, Saudi Arabia"
- heading "Professional Information (Optional)" [level=4]
- text: Company
- textbox "Company name"
- text: Job Title
- textbox "e.g., Software Developer"
- text: Industry
- textbox "e.g., Technology"
- text: Website
- textbox "https://example.com"
- text: LinkedIn URL
- textbox "https://linkedin.com/in/username"
- text: Bio
- textbox "Write a brief description about yourself..."
- button "Previous" [disabled]:
  - img
  - text: Previous
- button "Next":
  - text: Next
  - img
- paragraph:
  - text: Already have an account?
  - link "Sign In":
    - /url: /login
- button "📊 Perf"
- button "Open Tanstack query devtools":
  - img
```