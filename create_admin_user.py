#!/usr/bin/env python
"""
Create an admin user for testing the admin dashboard
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserRole, UserProfile

def create_admin_user():
    """Create an admin user for testing"""
    print("[ADMIN] Creating admin user for testing...")
    
    # Check if admin user already exists
    admin_username = "admin"
    admin_email = "<EMAIL>"
    admin_password = "admin123"
    
    try:
        # Try to get existing admin user
        admin_user = User.objects.get(username=admin_username)
        print(f"[EXISTS] Admin user '{admin_username}' already exists")
        
        # Update password in case it was different
        admin_user.set_password(admin_password)
        admin_user.save()
        print(f"[UPDATED] Password updated for admin user")
        
    except User.DoesNotExist:
        # Create new admin user
        admin_user = User.objects.create_user(
            username=admin_username,
            email=admin_email,
            password=admin_password,
            first_name="Admin",
            last_name="User",
            is_staff=True,
            is_superuser=True,
            is_active=True
        )
        print(f"[CREATED] Admin user '{admin_username}' created successfully")
    
    # Ensure admin has proper role
    try:
        admin_role = UserRole.objects.get(name='admin')
        
        # Check if profile exists
        if hasattr(admin_user, 'profile'):
            profile = admin_user.profile
        else:
            # Create profile if it doesn't exist
            profile = UserProfile.objects.create(
                user=admin_user,
                bio="System Administrator",
                location="System",
                language="en"
            )
            print(f"[CREATED] Profile created for admin user")
        
        # Check if user already has admin role assignment
        from users.models import RoleApplication
        existing_role_app = RoleApplication.objects.filter(
            user=admin_user,
            requested_role=admin_role
        ).first()

        if not existing_role_app:
            # Create role application for admin
            RoleApplication.objects.create(
                user=admin_user,
                requested_role=admin_role,
                motivation="System administrator account",
                qualifications="System admin privileges",
                experience="System level access",
                status='approved'  # Auto-approve admin
            )
            print(f"[ASSIGNED] Admin role application created and approved")
        else:
            print(f"[EXISTS] Admin role already assigned")
            
    except UserRole.DoesNotExist:
        print(f"[WARNING] Admin role not found in database")
    
    print(f"\n[SUCCESS] Admin user ready!")
    print(f"   Username: {admin_username}")
    print(f"   Password: {admin_password}")
    print(f"   Email: {admin_email}")
    print(f"   Login URL: http://localhost:3002/login")
    print(f"   Admin Dashboard: http://localhost:3002/admin/user-approvals")

def create_test_regular_user():
    """Create a regular test user for comparison"""
    print(f"\n[USER] Creating regular test user...")
    
    test_username = "testuser"
    test_email = "<EMAIL>"
    test_password = "test123"
    
    try:
        # Try to get existing user
        test_user = User.objects.get(username=test_username)
        print(f"[EXISTS] Test user '{test_username}' already exists")
        
        # Update password
        test_user.set_password(test_password)
        test_user.save()
        
    except User.DoesNotExist:
        # Create new test user
        test_user = User.objects.create_user(
            username=test_username,
            email=test_email,
            password=test_password,
            first_name="Test",
            last_name="User",
            is_active=True
        )
        print(f"[CREATED] Test user '{test_username}' created successfully")
    
    print(f"   Username: {test_username}")
    print(f"   Password: {test_password}")
    print(f"   Email: {test_email}")

if __name__ == '__main__':
    create_admin_user()
    create_test_regular_user()
    print(f"\n[COMPLETE] User creation complete!")
    print(f"\n[NEXT STEPS]:")
    print(f"   1. Go to http://localhost:3002/login")
    print(f"   2. Login with admin/admin123")
    print(f"   3. Navigate to /admin/user-approvals")
    print(f"   4. Test the approval interface with pending users")
