<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Comprehensive Application Test Suite</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-card {
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            background: #f7fafc;
        }
        .test-card h3 {
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status.success { background: #c6f6d5; color: #22543d; }
        .status.error { background: #fed7d7; color: #742a2a; }
        .status.warning { background: #fef5e7; color: #744210; }
        .status.pending { background: #e6fffa; color: #234e52; }
        .test-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            margin: 5px;
            transition: background 0.2s;
        }
        .test-button:hover { background: #3182ce; }
        .test-button:disabled { background: #a0aec0; cursor: not-allowed; }
        .log {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 20px;
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        .quick-link {
            display: block;
            padding: 15px;
            background: #4299e1;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            transition: background 0.2s;
        }
        .quick-link:hover { background: #3182ce; }
        .summary {
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Comprehensive Application Test Suite</h1>
        
        <div class="test-grid">
            <!-- Backend Tests -->
            <div class="test-card">
                <h3>🔧 Backend Server Tests</h3>
                <div id="backend-status" class="status pending">Pending</div>
                <p>Testing Django backend server and API endpoints</p>
                <button class="test-button" onclick="testBackend()">Test Backend</button>
                <button class="test-button" onclick="openBackendAdmin()">Open Admin</button>
            </div>

            <!-- Frontend Tests -->
            <div class="test-card">
                <h3>⚛️ Frontend Application Tests</h3>
                <div id="frontend-status" class="status pending">Pending</div>
                <p>Testing React frontend application and routing</p>
                <button class="test-button" onclick="testFrontend()">Test Frontend</button>
                <button class="test-button" onclick="openFrontend()">Open App</button>
            </div>

            <!-- Authentication Tests -->
            <div class="test-card">
                <h3>🔐 Authentication System Tests</h3>
                <div id="auth-status" class="status pending">Pending</div>
                <p>Testing login, logout, and role-based access</p>
                <button class="test-button" onclick="testAuthentication()">Test Auth</button>
                <button class="test-button" onclick="openLogin()">Open Login</button>
            </div>

            <!-- API Integration Tests -->
            <div class="test-card">
                <h3>🔗 API Integration Tests</h3>
                <div id="api-status" class="status pending">Pending</div>
                <p>Testing API endpoints and data flow</p>
                <button class="test-button" onclick="testAPIIntegration()">Test APIs</button>
                <button class="test-button" onclick="testCRUD()">Test CRUD</button>
            </div>

            <!-- Performance Tests -->
            <div class="test-card">
                <h3>⚡ Performance Tests</h3>
                <div id="performance-status" class="status pending">Pending</div>
                <p>Testing page load times and responsiveness</p>
                <button class="test-button" onclick="testPerformance()">Test Performance</button>
                <button class="test-button" onclick="runLoadTest()">Load Test</button>
            </div>

            <!-- Error Handling Tests -->
            <div class="test-card">
                <h3>🛡️ Error Handling Tests</h3>
                <div id="error-status" class="status pending">Pending</div>
                <p>Testing error boundaries and recovery</p>
                <button class="test-button" onclick="testErrorHandling()">Test Errors</button>
                <button class="test-button" onclick="simulateError()">Simulate Error</button>
            </div>
        </div>

        <div class="summary" id="summary" style="display: none;">
            <h3>📊 Test Summary</h3>
            <div id="summary-content"></div>
        </div>

        <div class="quick-links">
            <a href="http://localhost:3000" class="quick-link" target="_blank">🏠 Frontend App</a>
            <a href="http://localhost:8000/admin/" class="quick-link" target="_blank">👑 Admin Panel</a>
            <a href="http://localhost:3000/login" class="quick-link" target="_blank">🔐 Login Page</a>
            <a href="http://localhost:3000/dashboard" class="quick-link" target="_blank">📊 Dashboard</a>
            <a href="http://localhost:3000/admin/dashboard" class="quick-link" target="_blank">🔧 Admin Dashboard</a>
            <a href="./test-validation.html" class="quick-link" target="_blank">🧪 Validation Tests</a>
        </div>

        <div class="log" id="log"></div>
    </div>

    <script>
        let testResults = {};
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(testId, status, message) {
            const statusElement = document.getElementById(`${testId}-status`);
            statusElement.className = `status ${status}`;
            statusElement.textContent = message;
            testResults[testId] = { status, message };
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('summary');
            const content = document.getElementById('summary-content');
            
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(r => r.status === 'success').length;
            const failed = Object.values(testResults).filter(r => r.status === 'error').length;
            const warnings = Object.values(testResults).filter(r => r.status === 'warning').length;
            
            if (total > 0) {
                content.innerHTML = `
                    <p><strong>Total Tests:</strong> ${total}</p>
                    <p><strong>Passed:</strong> ${passed} ✅</p>
                    <p><strong>Failed:</strong> ${failed} ❌</p>
                    <p><strong>Warnings:</strong> ${warnings} ⚠️</p>
                    <p><strong>Success Rate:</strong> ${Math.round((passed / total) * 100)}%</p>
                `;
                summary.style.display = 'block';
            }
        }

        // Test Functions
        async function testBackend() {
            log('Testing backend server...', 'info');
            updateStatus('backend', 'pending', 'Testing...');
            
            try {
                const response = await fetch('http://localhost:8000/api/health/', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (response.ok) {
                    updateStatus('backend', 'success', 'Backend OK');
                    log('Backend server is running and responding ✅', 'success');
                } else {
                    updateStatus('backend', 'warning', `HTTP ${response.status}`);
                    log(`Backend responded with status ${response.status}`, 'warning');
                }
            } catch (error) {
                updateStatus('backend', 'error', 'Connection Failed');
                log(`Backend test failed: ${error.message}`, 'error');
            }
        }

        async function testFrontend() {
            log('Testing frontend application...', 'info');
            updateStatus('frontend', 'pending', 'Testing...');
            
            try {
                const response = await fetch('http://localhost:3000/', {
                    method: 'GET',
                    mode: 'no-cors'
                });
                
                updateStatus('frontend', 'success', 'Frontend OK');
                log('Frontend application is accessible ✅', 'success');
            } catch (error) {
                updateStatus('frontend', 'error', 'Connection Failed');
                log(`Frontend test failed: ${error.message}`, 'error');
            }
        }

        async function testAuthentication() {
            log('Testing authentication system...', 'info');
            updateStatus('auth', 'pending', 'Testing...');
            
            try {
                // Test login endpoint
                const response = await fetch('http://localhost:8000/api/auth/login/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'test', password: 'test' })
                });
                
                if (response.status === 400 || response.status === 401) {
                    updateStatus('auth', 'success', 'Auth Endpoint OK');
                    log('Authentication endpoint is responding correctly ✅', 'success');
                } else {
                    updateStatus('auth', 'warning', `HTTP ${response.status}`);
                    log(`Auth endpoint responded with status ${response.status}`, 'warning');
                }
            } catch (error) {
                updateStatus('auth', 'error', 'Connection Failed');
                log(`Authentication test failed: ${error.message}`, 'error');
            }
        }

        async function testAPIIntegration() {
            log('Testing API integration...', 'info');
            updateStatus('api', 'pending', 'Testing...');
            
            const endpoints = [
                '/api/incubator/business-plans/',
                '/api/incubator/business-ideas/',
                '/api/users/',
                '/api/auth/user/'
            ];
            
            let successCount = 0;
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`http://localhost:8000${endpoint}`);
                    if (response.status < 500) {
                        successCount++;
                        log(`✅ ${endpoint} - OK`, 'success');
                    } else {
                        log(`❌ ${endpoint} - Server Error`, 'error');
                    }
                } catch (error) {
                    log(`❌ ${endpoint} - Connection Failed`, 'error');
                }
            }
            
            const successRate = (successCount / endpoints.length) * 100;
            if (successRate >= 75) {
                updateStatus('api', 'success', `${successCount}/${endpoints.length} OK`);
            } else if (successRate >= 50) {
                updateStatus('api', 'warning', `${successCount}/${endpoints.length} OK`);
            } else {
                updateStatus('api', 'error', `${successCount}/${endpoints.length} OK`);
            }
        }

        async function testPerformance() {
            log('Testing performance...', 'info');
            updateStatus('performance', 'pending', 'Testing...');
            
            const startTime = performance.now();
            
            try {
                await fetch('http://localhost:3000/', { mode: 'no-cors' });
                const loadTime = performance.now() - startTime;
                
                if (loadTime < 2000) {
                    updateStatus('performance', 'success', `${Math.round(loadTime)}ms`);
                    log(`Performance test passed: ${Math.round(loadTime)}ms ✅`, 'success');
                } else if (loadTime < 5000) {
                    updateStatus('performance', 'warning', `${Math.round(loadTime)}ms`);
                    log(`Performance acceptable: ${Math.round(loadTime)}ms`, 'warning');
                } else {
                    updateStatus('performance', 'error', `${Math.round(loadTime)}ms`);
                    log(`Performance poor: ${Math.round(loadTime)}ms`, 'error');
                }
            } catch (error) {
                updateStatus('performance', 'error', 'Test Failed');
                log(`Performance test failed: ${error.message}`, 'error');
            }
        }

        async function testErrorHandling() {
            log('Testing error handling...', 'info');
            updateStatus('error', 'pending', 'Testing...');
            
            try {
                // Test 404 endpoint
                const response = await fetch('http://localhost:8000/api/nonexistent/');
                
                if (response.status === 404) {
                    updateStatus('error', 'success', 'Error Handling OK');
                    log('Error handling is working correctly ✅', 'success');
                } else {
                    updateStatus('error', 'warning', `HTTP ${response.status}`);
                    log(`Unexpected response: ${response.status}`, 'warning');
                }
            } catch (error) {
                updateStatus('error', 'error', 'Test Failed');
                log(`Error handling test failed: ${error.message}`, 'error');
            }
        }

        // Helper functions
        function openBackendAdmin() {
            window.open('http://localhost:8000/admin/', '_blank');
        }

        function openFrontend() {
            window.open('http://localhost:3000/', '_blank');
        }

        function openLogin() {
            window.open('http://localhost:3000/login', '_blank');
        }

        function testCRUD() {
            log('CRUD operations test would require authentication', 'info');
            window.open('http://localhost:3000/admin/dashboard', '_blank');
        }

        function runLoadTest() {
            log('Running basic load test...', 'info');
            Promise.all([
                testBackend(),
                testFrontend(),
                testAPIIntegration()
            ]).then(() => {
                log('Load test completed ✅', 'success');
            });
        }

        function simulateError() {
            log('Simulating error for testing...', 'warning');
            try {
                throw new Error('Simulated test error');
            } catch (error) {
                log(`Caught simulated error: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            log('🚀 Comprehensive test suite loaded', 'info');
            log('Click test buttons to run individual tests', 'info');
            log('Or use quick links to open application pages', 'info');
            
            // Auto-test basic connectivity
            setTimeout(() => {
                testBackend();
                setTimeout(() => testFrontend(), 1000);
            }, 1000);
        });
    </script>
</body>
</html>
