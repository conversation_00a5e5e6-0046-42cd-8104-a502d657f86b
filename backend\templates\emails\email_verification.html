<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email Address - {{ site_name }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        h1 {
            color: #1a202c;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .subtitle {
            color: #718096;
            margin: 10px 0 0;
            font-size: 16px;
        }
        .content {
            margin: 30px 0;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2d3748;
        }
        .message {
            font-size: 16px;
            margin-bottom: 30px;
            color: #4a5568;
            line-height: 1.7;
        }
        .button-container {
            text-align: center;
            margin: 40px 0;
        }
        .verify-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .verify-button:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        .alternative-link {
            margin-top: 30px;
            padding: 20px;
            background-color: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .alternative-link h3 {
            margin: 0 0 10px;
            color: #2d3748;
            font-size: 16px;
        }
        .alternative-link p {
            margin: 0;
            color: #718096;
            font-size: 14px;
            word-break: break-all;
        }
        .expiry-info {
            background-color: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 16px;
            margin: 30px 0;
        }
        .expiry-info h3 {
            margin: 0 0 8px;
            color: #c53030;
            font-size: 16px;
        }
        .expiry-info p {
            margin: 0;
            color: #742a2a;
            font-size: 14px;
        }
        .footer {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #718096;
            font-size: 14px;
        }
        .footer p {
            margin: 5px 0;
        }
        .security-notice {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-notice h3 {
            margin: 0 0 8px;
            color: #276749;
            font-size: 16px;
        }
        .security-notice p {
            margin: 0;
            color: #22543d;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            h1 {
                font-size: 24px;
            }
            .verify-button {
                padding: 14px 28px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Y</div>
            <h1>Verify Your Email Address</h1>
            <p class="subtitle">Complete your {{ site_name }} registration</p>
        </div>

        <div class="content">
            <div class="greeting">
                Hello {{ user.first_name|default:user.username }},
            </div>

            <div class="message">
                Thank you for registering with {{ site_name }}! To complete your account setup and ensure the security of your account, please verify your email address by clicking the button below.
            </div>

            <div class="button-container">
                <a href="{{ verification_url }}" class="verify-button">
                    Verify Email Address
                </a>
            </div>

            <div class="expiry-info">
                <h3>⏰ Important: Time Sensitive</h3>
                <p>This verification link will expire on {{ expires_at|date:"F j, Y" }} at {{ expires_at|time:"g:i A" }}. Please verify your email before this time.</p>
            </div>

            <div class="alternative-link">
                <h3>Can't click the button?</h3>
                <p>Copy and paste this link into your browser:</p>
                <p>{{ verification_url }}</p>
            </div>

            <div class="security-notice">
                <h3>🔒 Security Notice</h3>
                <p>If you didn't create an account with {{ site_name }}, you can safely ignore this email. Your email address will not be added to our system.</p>
            </div>
        </div>

        <div class="footer">
            <p><strong>{{ site_name }}</strong></p>
            <p>Syrian Community for Data Science and AI</p>
            <p>This is an automated message, please do not reply to this email.</p>
            <p>If you need help, contact our support team.</p>
        </div>
    </div>
</body>
</html>
