/**
 * INTEGRATED AI API SERVICE
 * Comprehensive integration with backend AI functionality
 * Replaces all fragmented AI services with unified backend connection
 */

import { apiRequest } from './api';

// ===== TYPES =====
export interface AISession {
  id: number;
  session_type: string;
  started_at: string;
  ended_at?: string;
  is_active: boolean;
}

export interface AIInteraction {
  id: number;
  session_id: number;
  user_input: string;
  ai_response: string;
  interaction_type: string;
  metadata: Record<string, any>;
  created_at: string;
}

export interface AIRecommendation {
  id: number;
  business_idea_id: number;
  recommendation_type: string;
  title: string;
  description: string;
  priority: string;
  is_completed: boolean;
  created_at: string;
}

export interface PredictiveAnalytics {
  analysis_type: string;
  result: Record<string, any>;
  confidence_score?: number;
  timestamp: string;
}

export interface AIStatus {
  is_available: boolean;
  service: string;
  version: string;
  features: Record<string, boolean>;
  endpoints: Record<string, string>;
}

export interface AIFeatures {
  chat: boolean;
  business_analysis: boolean;
  predictive_analytics: boolean;
  computer_vision: boolean;
  voice_ai: boolean;
  recommendations: boolean;
}

// ===== CORE AI SERVICES =====

/**
 * Chat with AI - Connected to backend AI service
 */
export const chatWithAI = async (
  message: string,
  sessionType: string = 'general',
  language: string = 'auto',
  settings?: {
    temperature?: number;
    max_tokens?: number;
    model?: string;
    enable_thinking?: boolean;
    creativity_level?: number;
    response_length?: number;
  }
): Promise<{
  response: string;
  session_id?: number;
  interaction_id?: number;
  success: boolean;
  data?: any;
}> => {
  try {
    // ✅ GET USER INFO FROM LOCAL STORAGE
    const getUserInfo = () => {
      try {
        const userStr = localStorage.getItem('user');
        return userStr ? JSON.parse(userStr) : null;
      } catch {
        return null;
      }
    };
    const userInfo = getUserInfo();

    // ✅ SEND CHAT_TYPE, USER CONTEXT, AND AI SETTINGS TO BACKEND
    const requestBody: any = {
      message,
      language,
      chat_type: sessionType,  // ✅ USE sessionType AS chat_type FOR SYRIAN CONTEXT
      user_context: {
        // ✅ USER CONTEXT FOR PERSONALIZED RESPONSES
        user_name: userInfo?.first_name || userInfo?.username || 'المستخدم',
        user_role: userInfo?.role || 'user',
        user_id: userInfo?.id,
        platform: 'ياسمين للذكاء الاصطناعي وعلوم البيانات',
        database_context: 'Syrian AI & Data Science Community Platform'
      }
    };

    // ✅ ADD AI SETTINGS IF PROVIDED
    if (settings) {
      requestBody.ai_settings = {
        temperature: settings.temperature,
        max_tokens: settings.max_tokens,
        model: settings.model,
        enable_thinking: settings.enable_thinking,
        creativity_level: settings.creativity_level,
        response_length: settings.response_length
      };
    }

    const response: any = await apiRequest('/ai/chat/', 'POST', requestBody);

    // Handle backend response format
    if (response.success) {
      // Backend returns: { success: true, message: "...", service: "gemini", ... }
      const aiMessage = response.message || response.data?.message || response.data?.content || 'No response received';

      return {
        response: aiMessage,
        session_id: Date.now(), // Generate local session ID
        interaction_id: Date.now() + Math.random(),
        success: true,
        data: response
      };
    } else {
      // Handle error responses
      const errorMessage = response.error || response.message || 'AI service error';
      throw new Error(errorMessage);
    }
  } catch (error) {
    console.error('AI Chat API Error:', error);
    throw error;
  }
};

/**
 * Start new AI session
 */
export const startAISession = async (sessionType: string): Promise<AISession> => {
  return apiRequest('/ai/sessions/', 'POST', {
    session_type: sessionType
  });
};

/**
 * Get AI session history
 */
export const getAISessionHistory = async (sessionId?: number): Promise<AISession[]> => {
  const url = sessionId ? `/ai/sessions/${sessionId}/` : '/ai/sessions/';
  return apiRequest(url, 'GET');
};

/**
 * Get AI interactions for a session
 */
export const getAIInteractions = async (sessionId: number): Promise<AIInteraction[]> => {
  return apiRequest(`/ai/sessions/${sessionId}/interactions/`, 'GET');
};

// ===== BUSINESS AI FEATURES =====

/**
 * Analyze business idea with AI
 */
export const analyzeBusinessIdea = async (
  businessIdea: string,
  language: string = 'auto'
): Promise<{
  analysis: string;
  recommendations: AIRecommendation[];
  viability_score: number;
}> => {
  return apiRequest('/ai/analyze-business/', 'POST', {
    business_idea: businessIdea,
    language
  });
};

/**
 * Generate business plan with AI
 */
export const generateBusinessPlan = async (
  businessIdea: string,
  sections: string[] = []
): Promise<{
  business_plan: Record<string, string>;
  template_id: number;
}> => {
  return apiRequest('/ai/generate-business-plan/', 'POST', {
    business_idea: businessIdea,
    sections
  });
};

/**
 * Get AI recommendations for business idea
 */
export const getAIRecommendations = async (
  businessIdeaId: number
): Promise<AIRecommendation[]> => {
  return apiRequest(`/ai/recommendations/${businessIdeaId}/`, 'GET');
};

// ===== ADVANCED AI FEATURES =====

/**
 * Predictive Analytics
 */
export const getPredictiveAnalytics = async (
  analysisType: string,
  data: Record<string, any>
): Promise<PredictiveAnalytics> => {
  return apiRequest('/ai/predictive-analytics/', 'POST', {
    analysis_type: analysisType,
    data
  });
};

/**
 * Computer Vision Analysis
 */
export const analyzeImage = async (
  imageFile: File,
  analysisType: string = 'document_analysis'
): Promise<{
  analysis_type: string;
  result: Record<string, any>;
  confidence_score: number;
}> => {
  const formData = new FormData();
  formData.append('image', imageFile);
  formData.append('analysis_type', analysisType);

  return apiRequest('/ai/computer-vision/', 'POST', formData);
};

/**
 * Voice AI Processing
 */
export const processVoiceInput = async (
  audioFile: File,
  language: string = 'auto'
): Promise<{
  transcription: string;
  language_detected: string;
  confidence_score: number;
}> => {
  const formData = new FormData();
  formData.append('audio', audioFile);
  formData.append('language', language);

  return apiRequest('/ai/voice-ai/', 'POST', formData);
};

// ===== AI DASHBOARD & ANALYTICS =====

/**
 * Get AI dashboard statistics
 */
export const getAIDashboardStats = async (): Promise<{
  total_sessions: number;
  total_interactions: number;
  active_sessions: number;
  recommendations_generated: number;
  success_rate: number;
  popular_features: Record<string, number>;
}> => {
  return apiRequest('/ai/dashboard-stats/', 'GET');
};

/**
 * Get real-time AI statistics
 */
export const getRealTimeAIStats = async (): Promise<{
  current_active_users: number;
  requests_per_minute: number;
  average_response_time: number;
  system_load: number;
}> => {
  return apiRequest('/ai/real-time-stats/', 'GET');
};

/**
 * Get AI features available to user
 */
export const getAIFeatures = async (): Promise<AIFeatures> => {
  return apiRequest('/ai/features/', 'GET');
};

/**
 * Get recent AI activity
 */
export const getRecentAIActivity = async (limit: number = 10): Promise<AIInteraction[]> => {
  return apiRequest(`/ai/recent-activity/?limit=${limit}`, 'GET');
};

// ===== AI STATUS & HEALTH =====

/**
 * Get AI service status
 */
export const getAIStatus = async (): Promise<AIStatus> => {
  try {
    console.log('🔍 Checking AI service status...');
    const response = await apiRequest('/ai/status/', 'GET');
    console.log('✅ AI Status Response:', response);

    // ✅ ENSURE PROPER RESPONSE FORMAT
    return {
      is_available: response.is_available === true,
      service: response.service || response.service_type || 'ai-service',
      ...response
    };
  } catch (error) {
    console.error('❌ AI Status Error:', error);
    return {
      is_available: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    } as AIStatus;
  }
};

/**
 * Test AI service connectivity
 */
export const testAIService = async (): Promise<{
  success: boolean;
  response_time: number;
  service_health: string;
}> => {
  return apiRequest('/ai/test/', 'GET');
};

// ===== LANGUAGE & DETECTION =====

/**
 * Detect language of text
 */
export const detectLanguage = async (text: string): Promise<{
  language: string;
  confidence: number;
  supported_languages: string[];
}> => {
  return apiRequest('/ai/detect-language/', 'POST', { text });
};

// ===== MODELS & CAPABILITIES =====

/**
 * Get available Gemini models
 */
export const getAvailableModels = async (options?: {
  useCase?: string;
  latestOnly?: boolean;
}): Promise<any> => {
  const params = new URLSearchParams();
  if (options?.useCase) params.append('use_case', options.useCase);
  if (options?.latestOnly) params.append('latest_only', 'true');

  const url = `/ai/models/${params.toString() ? '?' + params.toString() : ''}`;
  return apiRequest(url, 'GET');
};

// ===== EXPORT UNIFIED API =====
export const integratedAiApi = {
  // Core chat
  chat: chatWithAI,
  startSession: startAISession,
  getSessionHistory: getAISessionHistory,
  getInteractions: getAIInteractions,
  
  // Business AI
  analyzeBusinessIdea,
  generateBusinessPlan,
  getRecommendations: getAIRecommendations,
  
  // Advanced features
  predictiveAnalytics: getPredictiveAnalytics,
  analyzeImage,
  processVoice: processVoiceInput,
  
  // Dashboard & analytics
  getDashboardStats: getAIDashboardStats,
  getRealTimeStats: getRealTimeAIStats,
  getFeatures: getAIFeatures,
  getRecentActivity: getRecentAIActivity,
  
  // Status & health
  getStatus: getAIStatus,
  test: testAIService,
  detectLanguage,

  // Models & capabilities
  getAvailableModels
};

export default integratedAiApi;
