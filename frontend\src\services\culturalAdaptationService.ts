/**
 * Cultural Adaptation Service
 * Handles MENA-specific cultural adaptations and business logic
 */

import { format, isWithinInterval, parseISO } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

export interface CulturalEvent {
  id: string;
  name: string;
  nameAr: string;
  date: string;
  type: 'islamic' | 'national' | 'business';
  impact: 'low' | 'medium' | 'high';
  description: string;
  descriptionAr: string;
  businessImpact: string;
  businessImpactAr: string;
  region?: string[];
}

export interface BusinessHours {
  country: string;
  timezone: string;
  workingDays: string[];
  workingHours: {
    start: string;
    end: string;
  };
  ramadanHours?: {
    start: string;
    end: string;
  };
  culturalNotes: string;
  culturalNotesAr: string;
}

export interface CulturalPreferences {
  country: string;
  language: 'ar' | 'en' | 'both';
  currency: string;
  dateFormat: string;
  numberFormat: 'western' | 'arabic';
  calendarType: 'gregorian' | 'hijri' | 'both';
  businessEtiquette: {
    greetings: string[];
    greetingsAr: string[];
    meetingStyle: string;
    meetingStyleAr: string;
    communicationStyle: string;
    communicationStyleAr: string;
  };
  religiousConsiderations: {
    prayerTimes: boolean;
    halalRequirements: boolean;
    islamicFinance: boolean;
    genderConsiderations: boolean;
  };
}

class CulturalAdaptationService {
  private culturalEvents: CulturalEvent[] = [
    {
      id: 'ramadan-2024',
      name: 'Ramadan',
      nameAr: 'رمضان',
      date: '2024-03-10',
      type: 'islamic',
      impact: 'high',
      description: 'Holy month of fasting for Muslims',
      descriptionAr: 'الشهر الكريم للصيام عند المسلمين',
      businessImpact: 'Reduced working hours, different meeting schedules',
      businessImpactAr: 'ساعات عمل مخفضة، مواعيد اجتماعات مختلفة',
      region: ['saudi', 'uae', 'egypt', 'jordan', 'lebanon']
    },
    {
      id: 'eid-fitr-2024',
      name: 'Eid Al-Fitr',
      nameAr: 'عيد الفطر',
      date: '2024-04-10',
      type: 'islamic',
      impact: 'high',
      description: 'Festival marking the end of Ramadan',
      descriptionAr: 'عيد يمثل نهاية شهر رمضان',
      businessImpact: 'Public holiday, business closure for 3-5 days',
      businessImpactAr: 'عطلة رسمية، إغلاق الأعمال لمدة 3-5 أيام',
      region: ['saudi', 'uae', 'egypt', 'jordan', 'lebanon']
    },
    {
      id: 'eid-adha-2024',
      name: 'Eid Al-Adha',
      nameAr: 'عيد الأضحى',
      date: '2024-06-17',
      type: 'islamic',
      impact: 'high',
      description: 'Festival of Sacrifice',
      descriptionAr: 'عيد الأضحى المبارك',
      businessImpact: 'Extended holiday period, pilgrimage season',
      businessImpactAr: 'فترة عطلة ممتدة، موسم الحج',
      region: ['saudi', 'uae', 'egypt', 'jordan', 'lebanon']
    },
    {
      id: 'saudi-national-day',
      name: 'Saudi National Day',
      nameAr: 'اليوم الوطني السعودي',
      date: '2024-09-23',
      type: 'national',
      impact: 'medium',
      description: 'Saudi Arabia National Day',
      descriptionAr: 'اليوم الوطني للمملكة العربية السعودية',
      businessImpact: 'Public holiday in Saudi Arabia',
      businessImpactAr: 'عطلة رسمية في المملكة العربية السعودية',
      region: ['saudi']
    },
    {
      id: 'uae-national-day',
      name: 'UAE National Day',
      nameAr: 'اليوم الوطني الإماراتي',
      date: '2024-12-02',
      type: 'national',
      impact: 'medium',
      description: 'United Arab Emirates National Day',
      descriptionAr: 'اليوم الوطني لدولة الإمارات العربية المتحدة',
      businessImpact: 'Public holiday in UAE',
      businessImpactAr: 'عطلة رسمية في دولة الإمارات',
      region: ['uae']
    }
  ];

  private businessHours: Record<string, BusinessHours> = {
    saudi: {
      country: 'Saudi Arabia',
      timezone: 'Asia/Riyadh',
      workingDays: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'],
      workingHours: {
        start: '08:00',
        end: '17:00'
      },
      ramadanHours: {
        start: '09:00',
        end: '15:00'
      },
      culturalNotes: 'Friday and Saturday are weekends. Prayer times affect meeting schedules.',
      culturalNotesAr: 'الجمعة والسبت عطلة نهاية الأسبوع. أوقات الصلاة تؤثر على مواعيد الاجتماعات.'
    },
    uae: {
      country: 'United Arab Emirates',
      timezone: 'Asia/Dubai',
      workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      workingHours: {
        start: '09:00',
        end: '18:00'
      },
      ramadanHours: {
        start: '09:00',
        end: '15:00'
      },
      culturalNotes: 'Saturday and Sunday are weekends. Multicultural business environment.',
      culturalNotesAr: 'السبت والأحد عطلة نهاية الأسبوع. بيئة عمل متعددة الثقافات.'
    },
    egypt: {
      country: 'Egypt',
      timezone: 'Africa/Cairo',
      workingDays: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'],
      workingHours: {
        start: '09:00',
        end: '17:00'
      },
      ramadanHours: {
        start: '10:00',
        end: '15:00'
      },
      culturalNotes: 'Friday and Saturday are weekends. Traditional business culture.',
      culturalNotesAr: 'الجمعة والسبت عطلة نهاية الأسبوع. ثقافة عمل تقليدية.'
    },
    jordan: {
      country: 'Jordan',
      timezone: 'Asia/Amman',
      workingDays: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday'],
      workingHours: {
        start: '08:30',
        end: '16:30'
      },
      ramadanHours: {
        start: '09:00',
        end: '14:00'
      },
      culturalNotes: 'Friday and Saturday are weekends. Relationship-based business culture.',
      culturalNotesAr: 'الجمعة والسبت عطلة نهاية الأسبوع. ثقافة عمل تعتمد على العلاقات.'
    },
    lebanon: {
      country: 'Lebanon',
      timezone: 'Asia/Beirut',
      workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
      workingHours: {
        start: '08:00',
        end: '17:00'
      },
      ramadanHours: {
        start: '09:00',
        end: '15:00'
      },
      culturalNotes: 'Saturday and Sunday are weekends. French and Arabic business influence.',
      culturalNotesAr: 'السبت والأحد عطلة نهاية الأسبوع. تأثير فرنسي وعربي في الأعمال.'
    }
  };

  private culturalPreferences: Record<string, CulturalPreferences> = {
    saudi: {
      country: 'Saudi Arabia',
      language: 'ar',
      currency: 'SAR',
      dateFormat: 'dd/MM/yyyy',
      numberFormat: 'arabic',
      calendarType: 'both',
      businessEtiquette: {
        greetings: ['Formal handshakes', 'As-salamu alaykum', 'Respectful titles'],
        greetingsAr: ['مصافحة رسمية', 'السلام عليكم', 'ألقاب محترمة'],
        meetingStyle: 'Formal, relationship-building important',
        meetingStyleAr: 'رسمي، بناء العلاقات مهم',
        communicationStyle: 'Indirect, respectful, hierarchical',
        communicationStyleAr: 'غير مباشر، محترم، هرمي'
      },
      religiousConsiderations: {
        prayerTimes: true,
        halalRequirements: true,
        islamicFinance: true,
        genderConsiderations: true
      }
    },
    uae: {
      country: 'United Arab Emirates',
      language: 'both',
      currency: 'AED',
      dateFormat: 'dd/MM/yyyy',
      numberFormat: 'western',
      calendarType: 'gregorian',
      businessEtiquette: {
        greetings: ['Handshakes common', 'Multicultural greetings', 'Business cards important'],
        greetingsAr: ['المصافحة شائعة', 'تحيات متعددة الثقافات', 'بطاقات العمل مهمة'],
        meetingStyle: 'International business style, punctual',
        meetingStyleAr: 'أسلوب أعمال دولي، دقيق في المواعيد',
        communicationStyle: 'Direct but respectful, multicultural',
        communicationStyleAr: 'مباشر لكن محترم، متعدد الثقافات'
      },
      religiousConsiderations: {
        prayerTimes: true,
        halalRequirements: false,
        islamicFinance: false,
        genderConsiderations: false
      }
    }
  };

  /**
   * Get cultural events for a specific region and time period
   */
  getCulturalEvents(region: string, startDate?: string, endDate?: string): CulturalEvent[] {
    let events = this.culturalEvents.filter(event => 
      !event.region || event.region.includes(region)
    );

    if (startDate && endDate) {
      events = events.filter(event => {
        const eventDate = parseISO(event.date);
        return isWithinInterval(eventDate, {
          start: parseISO(startDate),
          end: parseISO(endDate)
        });
      });
    }

    return events;
  }

  /**
   * Get business hours for a specific country
   */
  getBusinessHours(country: string): BusinessHours | null {
    return this.businessHours[country] || null;
  }

  /**
   * Get cultural preferences for a specific country
   */
  getCulturalPreferences(country: string): CulturalPreferences | null {
    return this.culturalPreferences[country] || null;
  }

  /**
   * Check if a date falls during Ramadan
   */
  isRamadanPeriod(date: Date): boolean {
    const ramadanEvent = this.culturalEvents.find(event => event.id === 'ramadan-2024');
    if (!ramadanEvent) return false;

    const ramadanStart = parseISO(ramadanEvent.date);
    const ramadanEnd = new Date(ramadanStart);
    ramadanEnd.setDate(ramadanEnd.getDate() + 29); // Ramadan is approximately 30 days

    return isWithinInterval(date, { start: ramadanStart, end: ramadanEnd });
  }

  /**
   * Get appropriate business hours based on current period (normal vs Ramadan)
   */
  getContextualBusinessHours(country: string, date: Date = new Date()): BusinessHours | null {
    const businessHours = this.getBusinessHours(country);
    if (!businessHours) return null;

    if (this.isRamadanPeriod(date) && businessHours.ramadanHours) {
      return {
        ...businessHours,
        workingHours: businessHours.ramadanHours
      };
    }

    return businessHours;
  }

  /**
   * Format date according to cultural preferences
   */
  formatDate(date: Date, country: string, language: 'ar' | 'en' = 'en'): string {
    const preferences = this.getCulturalPreferences(country);
    const locale = language === 'ar' ? ar : enUS;
    
    if (preferences?.dateFormat) {
      return format(date, preferences.dateFormat, { locale });
    }
    
    return format(date, 'dd/MM/yyyy', { locale });
  }

  /**
   * Format numbers according to cultural preferences
   */
  formatNumber(number: number, country: string): string {
    const preferences = this.getCulturalPreferences(country);
    
    if (preferences?.numberFormat === 'arabic') {
      // Convert to Arabic-Indic numerals
      return number.toString().replace(/\d/g, (digit) => {
        const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return arabicNumerals[parseInt(digit)];
      });
    }
    
    return number.toLocaleString('en-US');
  }

  /**
   * Get appropriate greeting based on cultural context
   */
  getGreeting(country: string, language: 'ar' | 'en' = 'en', timeOfDay: 'morning' | 'afternoon' | 'evening' = 'morning'): string {
    if (language === 'ar') {
      switch (timeOfDay) {
        case 'morning':
          return 'صباح الخير';
        case 'afternoon':
          return 'مساء الخير';
        case 'evening':
          return 'مساء الخير';
        default:
          return 'السلام عليكم';
      }
    } else {
      switch (timeOfDay) {
        case 'morning':
          return 'Good morning';
        case 'afternoon':
          return 'Good afternoon';
        case 'evening':
          return 'Good evening';
        default:
          return 'Hello';
      }
    }
  }

  /**
   * Check if Islamic finance compliance is required for a country
   */
  requiresIslamicFinanceCompliance(country: string): boolean {
    const preferences = this.getCulturalPreferences(country);
    return preferences?.religiousConsiderations.islamicFinance || false;
  }

  /**
   * Get prayer time considerations for scheduling
   */
  getPrayerTimeConsiderations(country: string): boolean {
    const preferences = this.getCulturalPreferences(country);
    return preferences?.religiousConsiderations.prayerTimes || false;
  }

  /**
   * Get upcoming cultural events that might affect business
   */
  getUpcomingEvents(country: string, daysAhead: number = 30): CulturalEvent[] {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + daysAhead);

    return this.getCulturalEvents(
      country,
      today.toISOString(),
      futureDate.toISOString()
    ).filter(event => event.impact === 'high' || event.impact === 'medium');
  }

  /**
   * Get business etiquette recommendations
   */
  getBusinessEtiquette(country: string, language: 'ar' | 'en' = 'en'): any {
    const preferences = this.getCulturalPreferences(country);
    if (!preferences) return null;

    const etiquette = preferences.businessEtiquette;
    
    return {
      greetings: language === 'ar' ? etiquette.greetingsAr : etiquette.greetings,
      meetingStyle: language === 'ar' ? etiquette.meetingStyleAr : etiquette.meetingStyle,
      communicationStyle: language === 'ar' ? etiquette.communicationStyleAr : etiquette.communicationStyle
    };
  }
}

// Export singleton instance
export const culturalAdaptationService = new CulturalAdaptationService();

/**
 * Arabic Accessibility Service
 * Enhanced accessibility features for Arabic/RTL interfaces
 */
export class ArabicAccessibilityService {
  /**
   * Generate ARIA labels for RTL interfaces
   */
  generateAriaLabel(text: string, language: 'ar' | 'en', context?: string): string {
    if (language === 'ar') {
      const contextMap: Record<string, string> = {
        button: 'زر',
        link: 'رابط',
        input: 'حقل إدخال',
        select: 'قائمة اختيار',
        checkbox: 'مربع اختيار',
        radio: 'زر اختيار',
        tab: 'تبويب',
        menu: 'قائمة',
        dialog: 'مربع حوار',
        alert: 'تنبيه'
      };

      const contextLabel = context ? contextMap[context] || context : '';
      return contextLabel ? `${contextLabel}: ${text}` : text;
    }

    return text;
  }

  /**
   * Get screen reader announcements for Arabic
   */
  getScreenReaderAnnouncement(action: string, language: 'ar' | 'en'): string {
    const announcements = {
      ar: {
        loading: 'جاري التحميل',
        loaded: 'تم التحميل',
        error: 'حدث خطأ',
        success: 'تم بنجاح',
        saved: 'تم الحفظ',
        deleted: 'تم الحذف',
        updated: 'تم التحديث',
        submitted: 'تم الإرسال',
        opened: 'تم الفتح',
        closed: 'تم الإغلاق',
        expanded: 'تم التوسيع',
        collapsed: 'تم الطي',
        selected: 'تم الاختيار',
        deselected: 'تم إلغاء الاختيار'
      },
      en: {
        loading: 'Loading',
        loaded: 'Loaded',
        error: 'Error occurred',
        success: 'Success',
        saved: 'Saved',
        deleted: 'Deleted',
        updated: 'Updated',
        submitted: 'Submitted',
        opened: 'Opened',
        closed: 'Closed',
        expanded: 'Expanded',
        collapsed: 'Collapsed',
        selected: 'Selected',
        deselected: 'Deselected'
      }
    };

    return announcements[language][action as keyof typeof announcements.ar] || action;
  }

  /**
   * Generate keyboard navigation instructions for Arabic interfaces
   */
  getKeyboardInstructions(language: 'ar' | 'en'): Record<string, string> {
    if (language === 'ar') {
      return {
        tab: 'استخدم Tab للانتقال إلى العنصر التالي',
        shiftTab: 'استخدم Shift+Tab للانتقال إلى العنصر السابق',
        enter: 'اضغط Enter للتفعيل',
        space: 'اضغط مسافة للاختيار',
        escape: 'اضغط Escape للإغلاق',
        arrows: 'استخدم أسهم الاتجاه للتنقل',
        home: 'اضغط Home للانتقال إلى البداية',
        end: 'اضغط End للانتقال إلى النهاية'
      };
    }

    return {
      tab: 'Use Tab to navigate to next element',
      shiftTab: 'Use Shift+Tab to navigate to previous element',
      enter: 'Press Enter to activate',
      space: 'Press Space to select',
      escape: 'Press Escape to close',
      arrows: 'Use arrow keys to navigate',
      home: 'Press Home to go to beginning',
      end: 'Press End to go to end'
    };
  }

  /**
   * Validate color contrast for Arabic text
   */
  validateColorContrast(backgroundColor: string, textColor: string, fontSize: number): {
    isValid: boolean;
    ratio: number;
    recommendation: string;
  } {
    // Simplified contrast calculation (in real implementation, use proper color contrast libraries)
    const bgLuminance = this.getLuminance(backgroundColor);
    const textLuminance = this.getLuminance(textColor);

    const ratio = (Math.max(bgLuminance, textLuminance) + 0.05) /
                  (Math.min(bgLuminance, textLuminance) + 0.05);

    // Arabic text often needs higher contrast due to complex letterforms
    const requiredRatio = fontSize >= 18 ? 3.5 : 4.8; // Slightly higher than WCAG for Arabic

    return {
      isValid: ratio >= requiredRatio,
      ratio: Math.round(ratio * 100) / 100,
      recommendation: ratio < requiredRatio ?
        'Consider increasing contrast for better Arabic text readability' :
        'Contrast is sufficient for Arabic text'
    };
  }

  private getLuminance(color: string): number {
    // Simplified luminance calculation
    // In real implementation, use proper color conversion
    const hex = color.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16) / 255;
    const g = parseInt(hex.substring(2, 4), 16) / 255;
    const b = parseInt(hex.substring(4, 6), 16) / 255;

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /**
   * Generate focus management for RTL interfaces
   */
  getFocusManagement(language: 'ar' | 'en'): {
    focusOrder: string;
    focusTrapping: string;
    focusIndicators: string;
  } {
    if (language === 'ar') {
      return {
        focusOrder: 'ترتيب التركيز من اليمين إلى اليسار',
        focusTrapping: 'حبس التركيز داخل المكونات التفاعلية',
        focusIndicators: 'مؤشرات تركيز واضحة ومرئية'
      };
    }

    return {
      focusOrder: 'Focus order follows RTL reading pattern',
      focusTrapping: 'Focus trapped within interactive components',
      focusIndicators: 'Clear and visible focus indicators'
    };
  }
}

// Export accessibility service instance
export const arabicAccessibilityService = new ArabicAccessibilityService();
