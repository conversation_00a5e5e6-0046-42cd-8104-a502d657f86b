/**
 * AI Template Generator Component
 * Generates custom business plan templates using AI based on user input
 */

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Sparkles,
  Brain,
  Zap,
  Target,
  TrendingUp,
  Users,
  DollarSign,
  Globe,
  Leaf,
  Shield,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  RefreshCw
} from 'lucide-react';
import { RTLText, RTLFlex } from '../common';
import { useLanguage } from '../../hooks/useLanguage';
import { businessPlanTemplatesAPI } from '../../services/templateCustomizationApi';
import { integratedAiApi } from '../../services/integratedAiApi';

interface AITemplateGeneratorProps {
  onTemplateGenerated: (template: any) => void;
  onClose: () => void;
}

interface GenerationStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  completed: boolean;
}

const AITemplateGenerator: React.FC<AITemplateGeneratorProps> = ({ onTemplateGenerated,
  onClose
 }) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const [currentStep, setCurrentStep] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generatedTemplate, setGeneratedTemplate] = useState<any>(null);

  // Form data
  const [formData, setFormData] = useState({
    businessDescription: '',
    industry: '',
    businessType: '',
    targetMarket: '',
    fundingStage: 'startup',
    specialRequirements: [] as string[],
    businessGoals: '',
    timeframe: '6-months',
    teamSize: 'small',
    budget: 'low'
  });

  const industries = [
    { value: 'technology', label: t('industries.technology'), icon: Brain },
    { value: 'healthcare', label: t('industries.healthcare'), icon: Shield },
    { value: 'finance', label: t('industries.finance'), icon: DollarSign },
    { value: 'education', label: t('industries.education'), icon: Users },
    { value: 'ecommerce', label: t('industries.ecommerce'), icon: Globe },
    { value: 'manufacturing', label: t('industries.manufacturing'), icon: TrendingUp },
    { value: 'sustainability', label: t('industries.sustainability'), icon: Leaf },
    { value: 'consulting', label: t('industries.consulting'), icon: Target }
  ];

  const businessTypes = [
    'SaaS/Software',
    'Mobile App',
    'E-commerce',
    'Consulting',
    'Restaurant',
    'Healthcare',
    'Manufacturing',
    'Real Estate',
    'Education',
    'Non-Profit',
    'FinTech',
    'Franchise'
  ];

  const specialRequirements = [
    { id: 'international', label: t('requirements.international') },
    { id: 'sustainability', label: t('requirements.sustainability') },
    { id: 'regulatory', label: t('requirements.regulatory') },
    { id: 'scalability', label: t('requirements.scalability') },
    { id: 'innovation', label: t('requirements.innovation') },
    { id: 'social_impact', label: t('requirements.socialImpact') }
  ];

  const generationSteps: GenerationStep[] = [
    {
      id: 'analyze',
      title: t('aiGeneration.steps.analyze'),
      description: t('aiGeneration.steps.analyzeDesc'),
      icon: Brain,
      completed: false
    },
    {
      id: 'select_template',
      title: t('aiGeneration.steps.selectTemplate'),
      description: t('aiGeneration.steps.selectTemplateDesc'),
      icon: Target,
      completed: false
    },
    {
      id: 'customize',
      title: t('aiGeneration.steps.customize'),
      description: t('aiGeneration.steps.customizeDesc'),
      icon: Sparkles,
      completed: false
    },
    {
      id: 'generate_content',
      title: t('aiGeneration.steps.generateContent'),
      description: t('aiGeneration.steps.generateContentDesc'),
      icon: Zap,
      completed: false
    },
    {
      id: 'finalize',
      title: t('aiGeneration.steps.finalize'),
      description: t('aiGeneration.steps.finalizeDesc'),
      icon: CheckCircle,
      completed: false
    }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSpecialRequirementToggle = (requirementId: string) => {
    setFormData(prev => ({
      ...prev,
      specialRequirements: prev.specialRequirements.includes(requirementId)
        ? prev.specialRequirements.filter(id => id !== requirementId)
        : [...prev.specialRequirements, requirementId]
    }));
  };

  const generateTemplate = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      // Step 1: Analyze business requirements
      setGenerationProgress(20);
      generationSteps[0].completed = true;
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 2: Select base template
      setGenerationProgress(40);
      generationSteps[1].completed = true;
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 3: Generate template using real AI API
      setGenerationProgress(60);
      generationSteps[2].completed = true;

      // Use both template API and AI service for enhanced generation
      const [generatedTemplate, aiRecommendations] = await Promise.all([
        businessPlanTemplatesAPI.generateTemplate(formData.industry),
        centralizedAiApi.generateIntelligentContent({
          content_type: 'business_plan_template',
          context: {
            industry: formData.industry,
            businessType: formData.businessType,
            businessDescription: formData.businessDescription,
            targetMarket: formData.targetMarket,
            specialRequirements: formData.specialRequirements
          }
        }).catch(err => {
          console.warn('AI recommendations failed:', err);
          return null;
        })
      ]);

      // Step 4: Customize content
      setGenerationProgress(80);
      generationSteps[3].completed = true;
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 5: Finalize
      setGenerationProgress(100);
      generationSteps[4].completed = true;

      // Create enhanced template object
      const template = {
        ...generatedTemplate,
        id: generatedTemplate.id || Date.now(),
        name: generatedTemplate.name || `AI-Generated ${formData.businessType} Business Plan`,
        description: generatedTemplate.description || `Custom business plan for ${formData.businessDescription}`,
        type: 'ai_generated',
        industry: formData.industry,
        businessType: formData.businessType,
        targetMarket: formData.targetMarket,
        specialRequirements: formData.specialRequirements,
        aiAnalysis: {
          complexityLevel: 'medium',
          recommendedSections: Object.keys(generatedTemplate.sections || {}).length,
          estimatedTime: '6-8 hours',
          successProbability: 85
        },
        recommendations: aiRecommendations?.success && aiRecommendations.data?.content ?
          aiRecommendations.data.content.split('\n').filter(line => line.trim()) :
          [
            t('aiGeneration.recommendations.marketResearch', 'Conduct thorough market research'),
            t('aiGeneration.recommendations.financialProjections', 'Develop detailed financial projections'),
            t('aiGeneration.recommendations.competitiveAnalysis', 'Perform competitive analysis')
          ]
      };

      setGeneratedTemplate(template);
      setCurrentStep(3);
    } catch (error) {
      console.error('Error generating template:', error);
      setError('Failed to generate AI template. Please try again later.');
      setCurrentStep(1);
    } finally {
      setIsGenerating(false);
    }
  };

  const generateTemplateSections = () => {
    const baseSections = [
      { id: 'executive_summary', title: t('sections.executiveSummary'), required: true },
      { id: 'market_analysis', title: t('sections.marketAnalysis'), required: true },
      { id: 'competitive_analysis', title: t('sections.competitiveAnalysis'), required: true },
      { id: 'financial_projections', title: t('sections.financialProjections'), required: true }
    ];

    // Add industry-specific sections
    if (formData.industry === 'technology') {
      baseSections.push(
        { id: 'technology_stack', title: t('sections.technologyStack'), required: false },
        { id: 'product_roadmap', title: t('sections.productRoadmap'), required: false }
      );
    }

    if (formData.specialRequirements.includes('international')) {
      baseSections.push(
        { id: 'international_expansion', title: t('sections.internationalExpansion'), required: false }
      );
    }

    if (formData.specialRequirements.includes('sustainability')) {
      baseSections.push(
        { id: 'sustainability_plan', title: t('sections.sustainabilityPlan'), required: false }
      );
    }

    return baseSections;
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <RTLText as="h3" className="text-lg font-semibold mb-4">
          {t('aiGeneration.step1.title')}
        </RTLText>

        {/* Business Description */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">
            {t('aiGeneration.businessDescription')}
          </label>
          <textarea
            value={formData.businessDescription}
            onChange={(e) => handleInputChange('businessDescription', e.target.value)}
            placeholder={t('aiGeneration.businessDescriptionPlaceholder')}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            rows={3}
          />
        </div>

        {/* Industry Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">
            {t('aiGeneration.industry')}
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {industries.map(industry => {
              const Icon = industry.icon;
              return (
                <button
                  key={industry.value}
                  onClick={() => handleInputChange('industry', industry.value)}
                  className={`p-3 rounded-lg border transition-colors ${
                    formData.industry === industry.value
                      ? 'border-purple-500 bg-purple-600/20 text-purple-400'
                      : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'}
                  }`}
                >
                  <Icon size={20} className="mx-auto mb-2" />
                  <div className="text-xs">{industry.label}</div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Business Type */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">
            {t('aiGeneration.businessType')}
          </label>
          <select
            value={formData.businessType}
            onChange={(e) => handleInputChange('businessType', e.target.value)}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value="">{t('aiGeneration.selectBusinessType')}</option>
            {businessTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        {/* Target Market */}
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">
            {t('aiGeneration.targetMarket')}
          </label>
          <input
            type="text"
            value={formData.targetMarket}
            onChange={(e) => handleInputChange('targetMarket', e.target.value)}
            placeholder={t('aiGeneration.targetMarketPlaceholder')}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <RTLText as="h3" className="text-lg font-semibold mb-4">
        {t('aiGeneration.step2.title')}
      </RTLText>

      {/* Funding Stage */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">
          {t('aiGeneration.fundingStage')}
        </label>
        <div className="grid grid-cols-3 gap-3">
          {['startup', 'growth', 'expansion'].map(stage => (
            <button
              key={stage}
              onClick={() => handleInputChange('fundingStage', stage)}
              className={`p-3 rounded-lg border transition-colors ${
                formData.fundingStage === stage
                  ? 'border-purple-500 bg-purple-600/20 text-purple-400'
                  : 'border-gray-700 bg-gray-800/50 text-gray-300 hover:border-gray-600'}
              }`}
            >
              {t(`aiGeneration.fundingStages.${stage}`)}
            </button>
          ))}
        </div>
      </div>

      {/* Special Requirements */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">
          {t('aiGeneration.specialRequirements')}
        </label>
        <div className="grid grid-cols-2 gap-3">
          {specialRequirements.map(req => (
            <label key={req.id} className={`flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg cursor-pointer hover:bg-gray-700/50 ${isRTL ? "flex-row-reverse" : ""}`}>
              <input
                type="checkbox"
                checked={formData.specialRequirements.includes(req.id)}
                onChange={() => handleSpecialRequirementToggle(req.id)}
                className="rounded border-gray-600 text-purple-600 focus:ring-purple-500"
              />
              <span className="text-sm">{req.label}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );

  const renderGenerationProgress = () => (
    <div className="space-y-6">
      <RTLText as="h3" className="text-lg font-semibold mb-4">
        {t('aiGeneration.generating')}
      </RTLText>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className={`flex justify-between text-sm mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <span>{t('aiGeneration.progress')}</span>
          <span>{Math.round(generationProgress)}%</span>
        </div>
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div
            className="bg-purple-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${generationProgress}%` }}
          />
        </div>
      </div>

      {/* Generation Steps */}
      <div className="space-y-4">
        {generationSteps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === Math.floor((generationProgress / 100) * generationSteps.length);

          return (
            <RTLFlex key={step.id} className="items-center p-4 bg-gray-800/50 rounded-lg">
              <div className={`p-2 rounded-lg ${
                step.completed ? 'bg-green-600/20 text-green-400' :
                isActive ? 'bg-purple-600/20 text-purple-400' :
                'bg-gray-700/50 text-gray-500'}
              }`}>
                {step.completed ? <CheckCircle size={20} /> : <Icon size={20} />}
              </div>
              <div className={isRTL ? 'mr-4' : 'ml-4'}>
                <div className={`font-medium ${
                  step.completed ? 'text-green-400' :
                  isActive ? 'text-purple-400' :
                  'text-gray-400'}
                }`}>
                  {step.title}
                </div>
                <div className="text-sm text-gray-500">{step.description}</div>
              </div>
              {isActive && !step.completed && (
                <RefreshCw size={16} className="animate-spin text-purple-400 ml-auto" />
              )}
            </RTLFlex>
          );
        })}
      </div>
    </div>
  );

  const renderResults = () => (
    <div className="space-y-6">
      <RTLText as="h3" className="text-lg font-semibold mb-4">
        {t('aiGeneration.templateGenerated')}
      </RTLText>

      {generatedTemplate && (
        <div className="bg-gray-800/50 rounded-lg p-6">
          <RTLText as="h4" className="text-xl font-semibold mb-2">
            {generatedTemplate.name}
          </RTLText>
          <p className="text-gray-300 mb-4">{generatedTemplate.description}</p>

          {/* AI Analysis */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                {generatedTemplate.aiAnalysis.complexityLevel}
              </div>
              <div className="text-sm text-gray-400">{t('aiGeneration.complexity')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {generatedTemplate.aiAnalysis.recommendedSections}
              </div>
              <div className="text-sm text-gray-400">{t('aiGeneration.sections')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {generatedTemplate.aiAnalysis.estimatedTime}
              </div>
              <div className="text-sm text-gray-400">{t('aiGeneration.estimatedTime')}</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-400">
                {generatedTemplate.aiAnalysis.successProbability}%
              </div>
              <div className="text-sm text-gray-400">{t('aiGeneration.successRate')}</div>
            </div>
          </div>

          {/* Sections Preview */}
          <div className="mb-6">
            <h5 className="font-medium mb-3">{t('aiGeneration.includedSections')}</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {generatedTemplate.sections.map((section: any) => (
                <div key={section.id} className={`flex items-center p-2 bg-gray-700/50 rounded ${isRTL ? "flex-row-reverse" : ""}`}>
                  <CheckCircle size={16} className={`text-green-400 mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  <span className="text-sm">{section.title}</span>
                  {section.required && (
                    <span className="ml-auto text-xs text-orange-400">{t('common.required')}</span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Recommendations */}
          <div className="mb-6">
            <h5 className="font-medium mb-3">{t('aiGeneration.recommendations')}</h5>
            <div className="space-y-2">
              {generatedTemplate.recommendations.map((rec: string, index: number) => (
                <div key={index} className={`flex items-start p-2 bg-blue-600/10 rounded ${isRTL ? "flex-row-reverse" : ""}`}>
                  <AlertCircle size={16} className={`text-blue-400 mr-2 mt-0.5 flex-shrink-0 ${isRTL ? "flex-row-reverse" : ""}`} />
                  <span className="text-sm text-blue-300">{rec}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-800">
          <RTLFlex className="items-center justify-between">
            <RTLFlex className="items-center">
              <Sparkles className={`text-purple-400 mr-3 ${isRTL ? "space-x-reverse" : ""}`} size={24} />
              <RTLText as="h2" className="text-xl font-semibold">
                {t('aiGeneration.title')}
              </RTLText>
            </RTLFlex>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              ×
            </button>
          </RTLFlex>

          {/* Step Indicator */}
          <div className={`flex items-center mt-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            {[1, 2, 3].map((step, index) => (
              <React.Fragment key={step}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep >= step
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700 text-gray-400'}
                }`}>
                  {step}
                </div>
                {index < 2 && (
                  <div className={`flex-1 h-1 mx-2 ${
                    currentStep > step ? 'bg-purple-600' : 'bg-gray-700'}
                  }`} />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {isGenerating && renderGenerationProgress()}
          {currentStep === 3 && !isGenerating && renderResults()}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-800">
          <RTLFlex className="justify-between">
            <button
              onClick={currentStep === 1 ? onClose : () => setCurrentStep(currentStep - 1)}
              className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              disabled={isGenerating}
            >
              {currentStep === 1 ? t('common.cancel') : t('common.back')}
            </button>

            <RTLFlex className="space-x-3">
              {currentStep < 2 && (
                <button
                  onClick={() => setCurrentStep(currentStep + 1)}
                  disabled={!formData.businessDescription || !formData.industry || !formData.businessType}
                  className={`px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700 disabled:text-gray-400 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  {t('common.next')}
                  <ArrowRight size={16} className={`ml-2 ${isRTL ? "space-x-reverse" : ""}`} />
                </button>
              )}

              {currentStep === 2 && (
                <button
                  onClick={generateTemplate}
                  disabled={isGenerating}
                  className={`px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <Sparkles size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  {t('aiGeneration.generateTemplate')}
                </button>
              )}

              {currentStep === 3 && !isGenerating && (
                <button
                  onClick={() => onTemplateGenerated(generatedTemplate)}
                  className={`px-6 py-2 bg-green-600 hover:bg-green-700 rounded-lg text-white transition-colors flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
                >
                  <CheckCircle size={16} className={`mr-2 ${isRTL ? "space-x-reverse" : ""}`} />
                  {t('aiGeneration.useTemplate')}
                </button>
              )}
            </RTLFlex>
          </RTLFlex>
        </div>
      </div>
    </div>
  );
};

export default AITemplateGenerator;
