import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * Specialized Error Boundary for lazy-loaded components
 * Prevents DOM cleanup issues during component unmounting
 */
class LazyErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const componentName = this.props.componentName || 'LazyComponent';
    
    // Log error safely
    if (process.env.NODE_ENV === 'development') {
      console.warn(`LazyErrorBoundary caught error in ${componentName}:`, error, errorInfo);
    }

    // Don't let lazy component errors crash the app
    this.setState({ error });
  }

  componentWillUnmount() {
    // Clean up any pending state updates
    this.setState = () => {};
  }

  render() {
    if (this.state.hasError) {
      // Return fallback UI or null to prevent DOM issues
      return this.props.fallback || null;
    }

    return this.props.children;
  }
}

export default LazyErrorBoundary;
