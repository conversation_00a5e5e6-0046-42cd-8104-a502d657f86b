#!/usr/bin/env python3
"""
Test the login API endpoint
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_login():
    """Test the login endpoint"""
    print("[LOGIN] Testing login API endpoint...")
    
    # Test admin login
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/token/",
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"[SUCCESS] Login successful")
            print(f"   Access Token: {result.get('access', 'N/A')[:50]}...")
            print(f"   Refresh Token: {result.get('refresh', 'N/A')[:50]}...")
            return result.get('access')
        else:
            print(f"[ERROR] Login failed")
            return None
            
    except Exception as e:
        print(f"[ERROR] Error: {e}")
        return None

def test_protected_endpoint(token):
    """Test a protected endpoint with the token"""
    if not token:
        print("[SKIP] No token available, skipping protected endpoint test")
        return
        
    print(f"\n[PROTECTED] Testing protected endpoint...")
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/users/approvals/stats/",
            headers={
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"[SUCCESS] Protected endpoint accessible")
            print(f"   Stats: {result}")
        else:
            print(f"[ERROR] Protected endpoint failed: {response.text}")
            
    except Exception as e:
        print(f"[ERROR] Error: {e}")

if __name__ == "__main__":
    token = test_login()
    test_protected_endpoint(token)
    
    print(f"\n[INFO] API Endpoints:")
    print(f"   Backend: {BASE_URL}")
    print(f"   Login: {BASE_URL}/api/auth/token/")
    print(f"   Approvals: {BASE_URL}/api/users/approvals/")
    print(f"   Frontend should be: http://localhost:3002")
    print(f"\n[CREDENTIALS]:")
    print(f"   Username: admin")
    print(f"   Password: admin123")
