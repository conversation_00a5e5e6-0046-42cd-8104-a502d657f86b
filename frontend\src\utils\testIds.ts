/**
 * Centralized test IDs for consistent testing across the application
 * This helps with Cypress E2E testing and component testing
 */

export const TEST_IDS = {
  // Authentication
  LOGIN: {
    EMAIL_INPUT: 'email-input',
    PASSWORD_INPUT: 'password-input',
    LOGIN_BUTTON: 'login-button',
    FORGOT_PASSWORD_LINK: 'forgot-password-link',
    SIGNUP_LINK: 'signup-link',
    ERROR_MESSAGE: 'error-message',
    LOADING_SPINNER: 'loading-spinner',
    REMEMBER_ME_CHECKBOX: 'remember-me-checkbox',
  },

  // Navigation
  SIDEBAR: {
    CONTAINER: 'sidebar',
    TOGGLE: 'sidebar-toggle',
    USER_INFO: 'sidebar-user-info',
    NAV_DASHBOARD: 'nav-dashboard',
    NAV_BUSINESS_PLANS: 'nav-business-plans',
    NAV_MENTORSHIP: 'nav-mentorship',
    NAV_FUNDING: 'nav-funding',
    NAV_PROFILE: 'nav-profile',
    NAV_SETTINGS: 'nav-settings',
    NAV_USER_MANAGEMENT: 'nav-user-management',
    NAV_ANALYTICS: 'nav-analytics',
  },

  // Dashboard
  DASHBOARD: {
    MAIN_CONTENT: 'main-content',
    STATS_CARD: 'stats-card',
    RECENT_ACTIVITY: 'recent-activity',
    QUICK_ACTIONS: 'quick-actions',
    CHART_CONTAINER: 'chart-container',
  },

  // Business Plans
  BUSINESS_PLANS: {
    LIST: 'business-plans-list',
    CARD: 'business-plan-card',
    CREATE_BUTTON: 'create-business-plan-button',
    TITLE_INPUT: 'title-input',
    DESCRIPTION_INPUT: 'description-input',
    INDUSTRY_INPUT: 'industry-input',
    STAGE_INPUT: 'stage-input',
    SUBMIT_BUTTON: 'submit-button',
    SAVE_BUTTON: 'save-button',
    DELETE_BUTTON: 'delete-button',
    EDIT_BUTTON: 'edit-button',
  },

  // Forms
  FORM: {
    SUBMIT_BUTTON: 'submit-button',
    CANCEL_BUTTON: 'cancel-button',
    RESET_BUTTON: 'reset-button',
    SAVE_BUTTON: 'save-button',
    ERROR_MESSAGE: 'error-message',
    SUCCESS_MESSAGE: 'success-message',
  },

  // Common UI
  UI: {
    LOADING_SPINNER: 'loading-spinner',
    ERROR_MESSAGE: 'error-message',
    SUCCESS_MESSAGE: 'success-message',
    MODAL: 'modal',
    MODAL_CLOSE: 'modal-close',
    TOAST: 'toast',
    DROPDOWN: 'dropdown',
    SEARCH_INPUT: 'search-input',
  },

  // Mobile
  MOBILE: {
    MENU_BUTTON: 'mobile-menu-button',
    MENU: 'mobile-menu',
    SIDEBAR: 'mobile-sidebar',
    OVERLAY: 'sidebar-overlay',
  },

  // User Profile
  PROFILE: {
    CONTAINER: 'user-profile',
    LOGOUT_BUTTON: 'logout-button',
    EDIT_BUTTON: 'edit-profile-button',
    AVATAR: 'user-avatar',
    NAME: 'user-name',
    EMAIL: 'user-email',
    ROLE_BADGE: 'user-role-badge',
  },

  // Language
  LANGUAGE: {
    SELECTOR: 'language-selector',
    ENGLISH: 'language-en',
    ARABIC: 'language-ar',
  },

  // Performance
  PERFORMANCE: {
    MONITOR: 'performance-monitor',
    METRICS: 'performance-metrics',
  },
} as const;

/**
 * Helper function to get test ID
 */
export const getTestId = (category: keyof typeof TEST_IDS, key: string): string => {
  const categoryObj = TEST_IDS[category] as Record<string, string>;
  return categoryObj[key] || `${category.toLowerCase()}-${key.toLowerCase()}`;
};

/**
 * Helper function to create data-testid prop
 */
export const testId = (id: string) => ({ 'data-testid': id });

/**
 * Helper function to create data-testid prop from TEST_IDS
 */
export const testIdFromCategory = (category: keyof typeof TEST_IDS, key: string) => 
  testId(getTestId(category, key));
