# 🤖 Gemini AI Setup Guide

## Overview
Your application is configured to use **Google Gemini AI only** (no OpenAI). The backend is properly set up, but you need a valid Gemini API key.

## Current Status
- ✅ **Backend Configuration**: Properly configured for Gemini only
- ✅ **AI Service**: Ready to use Gemini API
- ❌ **API Key**: Current key is expired and needs renewal
- ✅ **Frontend Integration**: Connected to backend AI service

## Step 1: Get a New Gemini API Key

### 1.1 Visit Google AI Studio
Go to: **https://aistudio.google.com/app/apikey**

### 1.2 Sign in with Google Account
- Use your Google account
- Accept the terms of service

### 1.3 Create API Key
1. Click **"Create API Key"**
2. Select **"Create API key in new project"** (recommended)
3. Copy the generated API key (starts with `AIzaSy...`)

### 1.4 Important Notes
- ⚠️ **Keep your API key secure** - don't share it publicly
- 💰 **Free tier available** - Gemini has generous free usage limits
- 📊 **Monitor usage** - Check your usage in Google AI Studio

## Step 2: Update Your Configuration

### 2.1 Update .env File
Edit `backend/.env` and replace the expired key:

```env
# Gemini AI API key
GEMINI_API_KEY=YOUR_NEW_API_KEY_HERE
```

### 2.2 Restart Backend Server
After updating the API key, restart your backend server:

```bash
# Stop the current server (Ctrl+C)
# Then restart:
cd backend
python manage.py runserver
```

### 2.3 Initialize AI Configuration
Run the initialization command:

```bash
cd backend
python manage.py init_ai_config
```

## Step 3: Test the Configuration

### 3.1 Test Backend AI Service
```bash
# Test the AI service directly
curl -X GET http://127.0.0.1:8000/api/ai/test/
```

### 3.2 Test Frontend Chat
1. Go to http://localhost:3000/
2. Login with any user account
3. Click "AI Assistant"
4. Send a test message
5. You should get real AI responses from Gemini

## Step 4: Verify Everything Works

### 4.1 Check Connection Status
In the chat interface, you should see:
- 🔄 "Connecting to backend AI service..." (briefly)
- ✅ "Connected to AI service. Welcome [Your Role]!"
- Real AI welcome message from Gemini

### 4.2 Test AI Responses
Send messages like:
- "Hello, how can you help me?"
- "I need help with my business plan"
- "What are the latest trends in entrepreneurship?"

### 4.3 Verify Backend Integration
- Look for "✓ Backend" tags on AI responses
- Check that responses are intelligent and contextual
- Verify role-specific functionality

## Gemini API Features Available

### 🤖 Core AI Capabilities
- **Universal Chat**: Intelligent conversations
- **Language Detection**: Auto-detects user language
- **Business Analysis**: AI-powered business insights
- **Text Analysis**: Content analysis and feedback
- **Role-based Responses**: Tailored to user roles

### 🤖 Latest 2025 Models Available

#### **Gemini 2.5 Series (Recommended)**
- **gemini-2.5-pro**: Most powerful thinking model with maximum accuracy
- **gemini-2.5-flash**: Best price-performance with thinking capabilities (default)
- **gemini-2.5-flash-lite**: Cost-efficient with high throughput

#### **Gemini 2.0 Series**
- **gemini-2.0-flash**: Next-gen features and speed
- **gemini-2.0-flash-lite**: Cost efficiency and low latency

#### **Specialized Models**
- **gemini-2.5-flash-preview-tts**: Text-to-speech generation
- **gemini-2.5-pro-preview-tts**: High-quality speech synthesis
- **gemini-2.0-flash-preview-image-generation**: Image creation and editing

#### **Live API Models**
- **gemini-live-2.5-flash-preview**: Real-time voice/video interactions
- **gemini-2.0-flash-live-001**: Bidirectional conversations

#### **Legacy Models (Deprecated Sept 2025)**
- **gemini-1.5-flash**: Fast responses (legacy)
- **gemini-1.5-pro**: Advanced reasoning (legacy)
- **gemini-1.5-flash-8b**: High volume tasks (legacy)

### 🔧 Configuration Options
- **Temperature**: 0.7 (balanced creativity/accuracy)
- **Max Tokens**: 4000 (long responses)
- **Rate Limiting**: Built-in protection
- **Caching**: Improved performance

## Troubleshooting

### Issue: "API key expired"
**Solution**: Get a new API key from Google AI Studio

### Issue: "API key invalid"
**Solutions**:
1. Check the API key format (should start with `AIzaSy`)
2. Ensure no extra spaces in the .env file
3. Restart the backend server after changes

### Issue: "Rate limit exceeded"
**Solutions**:
1. Wait a few minutes and try again
2. Check your usage in Google AI Studio
3. Consider upgrading to paid tier if needed

### Issue: "Service unavailable"
**Solutions**:
1. Check internet connection
2. Verify Google AI services are operational
3. Check backend server logs for errors

## Advanced Configuration

### Database Configuration
The system can store AI configuration in the database for easier management:

```bash
# Access Django admin
python manage.py createsuperuser
# Then go to: http://127.0.0.1:8000/admin/
# Navigate to: Core > AI Configurations
```

### Environment Variables
All configuration options:

```env
# Required
GEMINI_API_KEY=your-api-key-here

# Optional (defaults shown)
GEMINI_MODEL=gemini-1.5-flash
GEMINI_MAX_TOKENS=4000
GEMINI_TEMPERATURE=0.7
```

## Security Best Practices

### 🔒 API Key Security
- ✅ Store in environment variables only
- ✅ Never commit to version control
- ✅ Use different keys for dev/prod
- ✅ Rotate keys regularly

### 🛡️ Rate Limiting
- ✅ Built-in rate limiting enabled
- ✅ User-specific limits
- ✅ Graceful error handling

### 📊 Monitoring
- ✅ Usage tracking
- ✅ Error logging
- ✅ Performance metrics

## Support

### 📚 Documentation
- **Gemini API Docs**: https://ai.google.dev/docs
- **Google AI Studio**: https://aistudio.google.com/
- **Pricing**: https://ai.google.dev/pricing

### 🆘 Getting Help
1. Check the backend server logs
2. Test the API key in Google AI Studio
3. Verify network connectivity
4. Check rate limits and quotas

---

## Quick Start Checklist

- [ ] Get new Gemini API key from https://aistudio.google.com/app/apikey
- [ ] Update `backend/.env` with new API key
- [ ] Restart backend server
- [ ] Run `python manage.py init_ai_config`
- [ ] Test at http://localhost:3000/
- [ ] Verify AI responses work
- [ ] Check "✓ Backend" tags appear

**Once completed, you'll have a fully functional AI chat system using only Gemini API!** 🚀
