#!/usr/bin/env python3
"""
API Endpoint Testing Script
Tests all the newly created API endpoints to ensure they work with real data
"""

import os
import sys
import django
from django.test import Client
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

def create_test_user():
    """Create a test user for API testing"""
    try:
        # Create superuser for admin endpoints
        admin_user = User.objects.create_superuser(
            username='test_admin',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create regular user
        regular_user = User.objects.create_user(
            username='test_user',
            email='<EMAIL>',
            password='testpass123'
        )
        
        print("✅ Test users created successfully")
        return admin_user, regular_user
    except Exception as e:
        print(f"❌ Error creating test users: {e}")
        return None, None

def test_api_endpoint(client, endpoint, user=None, expected_status=200):
    """Test a single API endpoint"""
    try:
        if user:
            client.force_login(user)
        
        response = client.get(endpoint)
        
        if response.status_code == expected_status:
            print(f"✅ {endpoint} - Status: {response.status_code}")
            
            # Try to parse JSON response
            try:
                data = response.json()
                if isinstance(data, dict):
                    if 'results' in data:
                        print(f"   📊 Results count: {len(data['results'])}")
                    elif 'error' in data:
                        print(f"   ⚠️  Error: {data['error']}")
                    else:
                        print(f"   📋 Response keys: {list(data.keys())}")
                else:
                    print(f"   📋 Response type: {type(data)}")
            except:
                print(f"   📄 Non-JSON response")
                
        else:
            print(f"❌ {endpoint} - Status: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Raw response: {response.content[:200]}")
                
    except Exception as e:
        print(f"❌ {endpoint} - Exception: {e}")

def main():
    """Main testing function"""
    print("🚀 Starting API Endpoint Testing...")
    print("=" * 50)
    
    # Create test users
    admin_user, regular_user = create_test_user()
    if not admin_user or not regular_user:
        print("❌ Failed to create test users. Exiting.")
        return
    
    # Create test client
    client = Client()
    
    print("\n📋 Testing Admin Endpoints (require admin user):")
    print("-" * 40)
    
    admin_endpoints = [
        '/api/admin/stats/',
        '/api/admin/users/',
        '/api/admin/activity/',
        '/api/admin/moderation-analytics/',
        '/api/admin/audit-logs/',
        '/api/admin/system-health/',
    ]
    
    for endpoint in admin_endpoints:
        test_api_endpoint(client, endpoint, admin_user)
    
    print("\n📋 Testing Role-Specific Endpoints:")
    print("-" * 40)
    
    role_endpoints = [
        '/api/roles/moderator/dashboard-stats/',
        '/api/roles/mentor/dashboard-stats/',
        '/api/roles/investor/dashboard-stats/',
        '/api/roles/moderator/recent-activity/',
        '/api/roles/mentor/recent-activity/',
    ]
    
    for endpoint in role_endpoints:
        test_api_endpoint(client, endpoint, admin_user)
    
    print("\n📋 Testing Public/Authenticated Endpoints:")
    print("-" * 40)
    
    public_endpoints = [
        '/api/market-intelligence/',
        '/api/analytics/overview/',
        '/api/recent-activity/',
    ]
    
    for endpoint in public_endpoints:
        test_api_endpoint(client, endpoint, regular_user)
    
    print("\n📋 Testing Incubator Endpoints:")
    print("-" * 40)
    
    incubator_endpoints = [
        '/api/incubator/business-ideas/',
        '/api/incubator/business-plan-templates/',
        '/api/incubator/funding-opportunities/',
        '/api/incubator/resources/',
    ]
    
    for endpoint in incubator_endpoints:
        test_api_endpoint(client, endpoint, regular_user)
    
    print("\n🎉 API Endpoint Testing Complete!")
    print("=" * 50)

if __name__ == '__main__':
    main()
