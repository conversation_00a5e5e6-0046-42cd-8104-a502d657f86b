/**
 * Error Boundary Recovery Tests
 * Tests error boundary functionality and DOM manipulation fixes
 */

import { test, expect, Page } from '@playwright/test';

test.describe('Error Boundary Recovery', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    
    // Navigate to the app
    await page.goto('/');
    
    // Wait for the app to load
    await page.waitForLoadState('networkidle');
  });

  test('should not have duplicate error boundaries causing DOM conflicts', async () => {
    // Check that there's only one global error boundary in the DOM
    const errorBoundaries = await page.locator('[data-testid*="error-boundary"]').count();
    
    // Should have component-level error boundaries but not duplicates at root
    expect(errorBoundaries).toBeLessThanOrEqual(5); // Reasonable number for component boundaries
    
    // Check console for DOM manipulation errors
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Navigate around the app to trigger potential DOM issues
    await page.goto('/dashboard');
    await page.waitForTimeout(1000);
    await page.goto('/business-plans');
    await page.waitForTimeout(1000);
    await page.goto('/');
    
    // Check for the specific DOM error we fixed
    const domErrors = consoleErrors.filter(error => 
      error.includes('removeChild') || 
      error.includes('node to be removed is not a child')
    );
    
    expect(domErrors).toHaveLength(0);
  });

  test('should handle component errors gracefully', async () => {
    // Try to trigger a component error by navigating to a potentially problematic route
    await page.goto('/business-plans');
    await page.waitForLoadState('networkidle');
    
    // Check if any error boundaries are displayed
    const componentErrorBoundary = page.locator('[data-testid="component-error-boundary"]');
    
    // If error boundary is shown, it should have retry functionality
    if (await componentErrorBoundary.isVisible()) {
      const retryButton = page.locator('[data-testid="component-error-retry"]');
      expect(await retryButton.isVisible()).toBe(true);
      
      // Test retry functionality
      await retryButton.click();
      await page.waitForTimeout(1000);
    }
    
    // App should still be functional
    expect(await page.locator('body').isVisible()).toBe(true);
  });

  test('should show helpful error messages for API connectivity issues', async () => {
    // Intercept API calls to simulate backend down
    await page.route('**/api/**', route => {
      route.abort('failed');
    });
    
    // Try to access a page that requires API calls
    await page.goto('/register');
    await page.waitForLoadState('networkidle');
    
    // Fill out registration form
    await page.fill('input[name="firstName"]', 'Test');
    await page.fill('input[name="lastName"]', 'User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="username"]', 'testuser');
    await page.fill('input[name="password"]', 'testpass123');
    await page.fill('input[name="confirmPassword"]', 'testpass123');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Should show helpful error message about backend server
    await expect(page.locator('text=/backend server.*running.*port 8000/i')).toBeVisible({ timeout: 10000 });
  });

  test('should recover from lazy component loading errors', async () => {
    // Navigate to a page with lazy-loaded components
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Check for lazy error boundaries
    const lazyErrorBoundaries = page.locator('[data-testid*="lazy-error"]');
    
    // If lazy components fail to load, they should fail gracefully
    const lazyErrorCount = await lazyErrorBoundaries.count();
    
    // App should still be functional even if some lazy components fail
    expect(await page.locator('body').isVisible()).toBe(true);
    expect(await page.locator('nav, header, main').first().isVisible()).toBe(true);
  });

  test('should maintain app stability during navigation', async () => {
    const consoleErrors: string[] = [];
    const jsErrors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      jsErrors.push(error.message);
    });
    
    // Navigate through various routes rapidly
    const routes = ['/', '/dashboard', '/business-plans', '/templates', '/ai', '/settings'];
    
    for (const route of routes) {
      await page.goto(route);
      await page.waitForTimeout(500); // Brief wait between navigations
    }
    
    // Check for critical errors
    const criticalErrors = [...consoleErrors, ...jsErrors].filter(error =>
      error.includes('removeChild') ||
      error.includes('Cannot read properties of null') ||
      error.includes('Cannot read properties of undefined') ||
      error.includes('Maximum call stack')
    );
    
    expect(criticalErrors).toHaveLength(0);
    
    // App should still be responsive
    expect(await page.locator('body').isVisible()).toBe(true);
  });

  test('should handle authentication errors without crashing', async () => {
    // Intercept auth API to return 401
    await page.route('**/api/auth/**', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Authentication required' })
      });
    });
    
    // Try to access protected route
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Should redirect to login or show auth error, but not crash
    const currentUrl = page.url();
    const isOnLoginPage = currentUrl.includes('/login') || currentUrl.includes('/auth');
    const hasAuthError = await page.locator('text=/authentication/i').isVisible();
    
    expect(isOnLoginPage || hasAuthError).toBe(true);
    
    // App should still be functional
    expect(await page.locator('body').isVisible()).toBe(true);
  });
});
