/**
 * ✅ CLEAN REDUX-BASED UNIVERSAL SIDEBAR
 * Simple, unified sidebar using only Redux state management
 * Replaces all old duplicate sidebar components
 */

import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { logout } from '../../store/authSlice';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import useReduxRoles from '../../hooks/useReduxRoles';
import {
  Home, User, Settings, Bot, LogOut, X, Menu,
  Shield, BarChart3, Users, FileText, Calendar,
  TrendingUp, Briefcase, DollarSign, BookOpen
} from 'lucide-react';

interface UniversalSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  onToggle?: () => void;
  variant?: 'desktop' | 'mobile';
  isCollapsed?: boolean;
}

// Simple icon mapping
const iconMap: Record<string, React.ComponentType<any>> = {
  Home, User, Settings, Bot, LogOut, Shield, BarChart3, Users,
  FileText, Calendar, TrendingUp, Briefcase, DollarSign, BookOpen
};

const UniversalSidebar: React.FC<UniversalSidebarProps> = ({
  isOpen = false,
  onClose,
  onToggle,
  variant = 'desktop',
  isCollapsed = false
}) => {
  const { user } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  // ✅ SIMPLE REDUX INTEGRATION WITH ERROR HANDLING
  const { primaryRole, getSidebarNavigation } = useReduxRoles();

  // ✅ ARABIC TRANSLATION HELPER
  const getArabicTranslation = (key: string, isArabic: boolean) => {
    const translations: Record<string, { ar: string; en: string }> = {
      'Dashboard': { ar: 'لوحة التحكم', en: 'Dashboard' },
      'Profile': { ar: 'الملف الشخصي', en: 'Profile' },
      'Settings': { ar: 'الإعدادات', en: 'Settings' },
      'AI Chat': { ar: 'محادثة الذكاء الاصطناعي', en: 'AI Chat' },
      'Users': { ar: 'المستخدمون', en: 'Users' },
      'Reports': { ar: 'التقارير', en: 'Reports' },
      'Analytics': { ar: 'التحليلات', en: 'Analytics' },
      'Messages': { ar: 'الرسائل', en: 'Messages' },
      'Notifications': { ar: 'الإشعارات', en: 'Notifications' },
      'Help': { ar: 'المساعدة', en: 'Help' },
      // Role translations
      'role_user': { ar: 'مستخدم', en: 'User' },
      'role_admin': { ar: 'مدير', en: 'Admin' },
      'role_super_admin': { ar: 'مدير عام', en: 'Super Admin' },
      'role_mentor': { ar: 'مرشد', en: 'Mentor' },
      'role_investor': { ar: 'مستثمر', en: 'Investor' },
      'role_entrepreneur': { ar: 'رائد أعمال', en: 'Entrepreneur' },
      'role_moderator': { ar: 'مشرف', en: 'Moderator' },
      'Logout': { ar: 'تسجيل الخروج', en: 'Logout' }
    };

    const translation = translations[key];
    if (translation) {
      return isArabic ? translation.ar : translation.en;
    }

    // Fallback to i18n or original key
    return t(key, key);
  };

  // ✅ SAFE NAVIGATION WITH ARABIC TRANSLATIONS
  const navItems = React.useMemo(() => {
    try {
      const items = getSidebarNavigation() || [];
      return items.map(item => ({
        ...item,
        name: getArabicTranslation(item.name, isRTL)
      }));
    } catch (error) {
      console.error('Error getting sidebar navigation:', error);
      return [];
    }
  }, [getSidebarNavigation, isRTL, getArabicTranslation]);

  // ✅ ENHANCED HANDLERS WITH KEYBOARD SUPPORT
  const handleNavigation = (path: string) => {
    navigate(path);
    if (variant === 'mobile' && onClose) {
      onClose();
    }
  };

  const handleLogout = async () => {
    try {
      await dispatch(logout()).unwrap();
      navigate('/login');
      if (variant === 'mobile' && onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // ✅ KEYBOARD SUPPORT
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (variant === 'mobile' && isOpen && event.key === 'Escape') {
        onClose?.();
      }
    };

    if (variant === 'mobile' && isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [variant, isOpen, onClose]);

  const isActive = (path: string) => location.pathname === path;

  // ✅ UNIFIED MODERN STYLING - No role-specific colors needed

  // ✅ MOBILE SIDEBAR WITH LOGIN PAGE STYLING
  if (variant === 'mobile') {
    return (
      <>
        {/* Enhanced backdrop with blur */}
        {isOpen && (
          <div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 transition-all duration-300"
            onClick={onClose}
            onKeyDown={(e) => e.key === 'Escape' && onClose?.()}
          />
        )}
        {/* Mobile sidebar with gradient background like login page */}
        <div className={`fixed top-0 ${isRTL ? 'right-0' : 'left-0'} h-full w-80 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 shadow-2xl transform transition-all duration-300 ease-in-out z-50 ${isRTL ? 'border-l border-purple-700/30' : 'border-r border-purple-700/30'} ${
          isOpen ? 'translate-x-0' : isRTL ? 'translate-x-full' : '-translate-x-full'
        }`}>
          <SidebarContent />
        </div>
      </>
    );
  }

  // ✅ DESKTOP SIDEBAR WITH LOGIN PAGE STYLING
  return (
    <div className={`${isCollapsed ? 'w-16' : 'w-80'} bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 shadow-2xl transition-all duration-300 ease-in-out flex-shrink-0 ${isRTL ? 'border-l border-purple-700/30' : 'border-r border-purple-700/30'}`}>
      <SidebarContent />
    </div>
  );

  function SidebarContent() {
    return (
      <div className="h-full flex flex-col">
        {/* ✅ HEADER WITH LOGIN PAGE STYLING */}
        <div className="p-6 border-b border-white/10 bg-white/5 backdrop-blur-sm">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            {!isCollapsed && (
              <div className={isRTL ? 'text-right' : 'text-left'}>
                <h1 className="text-xl font-bold text-white mb-1">
                  {isRTL ? 'ياسمين AI' : 'Yasmeen AI'}
                </h1>
                <p className="text-sm text-purple-200 capitalize">
                  {getArabicTranslation(`role_${primaryRole}`, isRTL) || primaryRole}
                </p>
              </div>
            )}
            {variant === 'mobile' && (
              <button
                onClick={onClose}
                className="p-2 hover:bg-white/20 rounded-lg transition-all duration-200 text-white"
                aria-label={t('sidebar.close', 'Close sidebar')}
              >
                <X className="w-5 h-5" />
              </button>
            )}
            {variant === 'desktop' && onToggle && (
              <button
                onClick={onToggle}
                className="p-2 hover:bg-white/20 rounded-lg transition-all duration-200 text-white"
                aria-label={t('sidebar.toggle', 'Toggle sidebar')}
              >
                <Menu className="w-5 h-5" />
              </button>
            )}
          </div>
        </div>

        {/* ✅ USER INFO WITH MODERN STYLING */}
        {!isCollapsed && (
          <div className="p-4 border-b border-white/10 bg-white/5">
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
              <div className={`w-12 h-12 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full flex items-center justify-center shadow-lg ring-2 ring-white/20 ${isRTL ? 'ml-3' : 'mr-3'}`}>
                <User className="w-7 h-7 text-white" />
              </div>
              <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                <p className="font-semibold text-white text-sm">
                  {user?.first_name} {user?.last_name}
                </p>
                <p className="text-xs text-purple-200">@{user?.username}</p>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white/10 text-purple-200 mt-1 backdrop-blur-sm ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
                  <Shield className={`w-3 h-3 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                  {t(`roles.${primaryRole}`, primaryRole)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* ✅ NAVIGATION WITH MODERN STYLING AND ANIMATIONS */}
        <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
          {navItems.map((item) => {
            const IconComponent = iconMap[item.icon] || Home;
            const active = isActive(item.path);

            return (
              <button
                key={item.id}
                onClick={() => handleNavigation(item.path)}
                className={`w-full flex items-center px-4 py-3 rounded-xl transition-all duration-300 group relative overflow-hidden ${
                  isRTL ? 'flex-row-reverse' : 'flex-row'
                } ${
                  active
                    ? 'bg-white/20 text-white shadow-lg backdrop-blur-sm border border-white/30'
                    : 'text-purple-200 hover:bg-white/10 hover:text-white'
                }`}
                title={item.name}
              >
                {/* Active indicator */}
                {active && (
                  <div className={`absolute ${isRTL ? 'right-0 rounded-l-full' : 'left-0 rounded-r-full'} top-0 bottom-0 w-1 bg-gradient-to-b from-purple-400 to-blue-400`} />
                )}

                <IconComponent className={`w-5 h-5 transition-all duration-300 group-hover:scale-110 ${
                  active ? 'text-white' : 'text-purple-300'
                } ${isRTL ? 'ml-3' : 'mr-3'}`} />

                {!isCollapsed && (
                  <span className={`font-medium text-sm transition-all duration-300 flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {item.name}
                  </span>
                )}

                {active && !isCollapsed && (
                  <div className={`${isRTL ? 'mr-auto' : 'ml-auto'} w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 animate-pulse`} />
                )}
              </button>
            );
          })}
        </nav>

        {/* ✅ LOGOUT WITH MODERN STYLING */}
        <div className="p-4 border-t border-white/10 bg-white/5">
          <button
            onClick={handleLogout}
            className={`w-full flex items-center px-4 py-3 text-red-300 hover:bg-red-500/20 hover:text-red-200 rounded-xl transition-all duration-300 group border border-transparent hover:border-red-400/30 backdrop-blur-sm ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}
            title={t('sidebar.logout', 'Logout')}
          >
            <LogOut className={`w-5 h-5 transition-all duration-300 group-hover:scale-110 group-hover:rotate-12 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            {!isCollapsed && (
              <span className={`font-medium text-sm flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                {getArabicTranslation('Logout', isRTL)}
              </span>
            )}
          </button>
        </div>
      </div>
    );
  }
};

export default UniversalSidebar;
