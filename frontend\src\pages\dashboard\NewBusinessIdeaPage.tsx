/**
 * New Business Idea Page
 * Create and submit new business ideas with AI assistance
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { 
  Lightbulb, 
  Save, 
  ArrowLeft, 
  Bot, 
  Target, 
  Users, 
  DollarSign,
  TrendingUp,
  FileText,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface BusinessIdeaForm {
  title: string;
  description: string;
  targetMarket: string;
  problemSolved: string;
  solution: string;
  revenueModel: string;
  competitiveAdvantage: string;
  marketSize: string;
  tags: string[];
}

const NewBusinessIdeaPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [formData, setFormData] = useState<BusinessIdeaForm>({
    title: '',
    description: '',
    targetMarket: '',
    problemSolved: '',
    solution: '',
    revenueModel: '',
    competitiveAdvantage: '',
    marketSize: '',
    tags: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAIAssistant, setShowAIAssistant] = useState(false);

  const handleInputChange = (field: keyof BusinessIdeaForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTagsChange = (tagsString: string) => {
    const tags = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    setFormData(prev => ({
      ...prev,
      tags
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // TODO: Implement API call to save business idea
      console.log('Submitting business idea:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate back to business ideas list
      navigate('/dashboard/business-ideas');
    } catch (error) {
      console.error('Error submitting business idea:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateAISuggestions = () => {
    setShowAIAssistant(true);
    // TODO: Implement AI suggestions
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => navigate('/dashboard/business-ideas')}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
            {t('businessIdeas.backToList', 'Back to Business Ideas')}
          </button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Lightbulb className={`text-yellow-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={32} />
                {t('businessIdeas.new.title', 'Create New Business Idea')}
              </h1>
              <p className="text-gray-600 mt-2">
                {t('businessIdeas.new.subtitle', 'Transform your vision into a structured business concept')}
              </p>
            </div>
            
            <button
              onClick={generateAISuggestions}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              <Bot size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('ai.getHelp', 'AI Assistant')}
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <FileText className={`text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={24} />
              {t('businessIdeas.form.basicInfo', 'Basic Information')}
            </h2>
            
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessIdeas.form.title', 'Business Idea Title')} *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('businessIdeas.form.titlePlaceholder', 'Enter a compelling title for your business idea')}
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessIdeas.form.description', 'Description')} *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('businessIdeas.form.descriptionPlaceholder', 'Describe your business idea in detail')}
                  required
                />
              </div>
            </div>
          </div>

          {/* Market Analysis */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <Target className={`text-green-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={24} />
              {t('businessIdeas.form.marketAnalysis', 'Market Analysis')}
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessIdeas.form.targetMarket', 'Target Market')}
                </label>
                <textarea
                  value={formData.targetMarket}
                  onChange={(e) => handleInputChange('targetMarket', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('businessIdeas.form.targetMarketPlaceholder', 'Who is your target audience?')}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessIdeas.form.marketSize', 'Market Size')}
                </label>
                <input
                  type="text"
                  value={formData.marketSize}
                  onChange={(e) => handleInputChange('marketSize', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('businessIdeas.form.marketSizePlaceholder', 'Estimated market size')}
                />
              </div>
            </div>
          </div>

          {/* Problem & Solution */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <CheckCircle className={`text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={24} />
              {t('businessIdeas.form.problemSolution', 'Problem & Solution')}
            </h2>
            
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessIdeas.form.problemSolved', 'Problem Being Solved')}
                </label>
                <textarea
                  value={formData.problemSolved}
                  onChange={(e) => handleInputChange('problemSolved', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('businessIdeas.form.problemSolvedPlaceholder', 'What problem does your idea solve?')}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessIdeas.form.solution', 'Your Solution')}
                </label>
                <textarea
                  value={formData.solution}
                  onChange={(e) => handleInputChange('solution', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('businessIdeas.form.solutionPlaceholder', 'How does your idea solve the problem?')}
                />
              </div>
            </div>
          </div>

          {/* Business Model */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <DollarSign className={`text-green-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={24} />
              {t('businessIdeas.form.businessModel', 'Business Model')}
            </h2>
            
            <div className="grid grid-cols-1 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessIdeas.form.revenueModel', 'Revenue Model')}
                </label>
                <textarea
                  value={formData.revenueModel}
                  onChange={(e) => handleInputChange('revenueModel', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('businessIdeas.form.revenueModelPlaceholder', 'How will you make money?')}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessIdeas.form.competitiveAdvantage', 'Competitive Advantage')}
                </label>
                <textarea
                  value={formData.competitiveAdvantage}
                  onChange={(e) => handleInputChange('competitiveAdvantage', e.target.value)}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder={t('businessIdeas.form.competitiveAdvantagePlaceholder', 'What makes you different?')}
                />
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              {t('businessIdeas.form.tags', 'Tags')}
            </h2>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('businessIdeas.form.tagsLabel', 'Add relevant tags (comma-separated)')}
              </label>
              <input
                type="text"
                value={formData.tags.join(', ')}
                onChange={(e) => handleTagsChange(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder={t('businessIdeas.form.tagsPlaceholder', 'e.g., technology, healthcare, fintech')}
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => navigate('/dashboard/business-ideas')}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              {t('common.cancel', 'Cancel')}
            </button>
            
            <button
              type="submit"
              disabled={isSubmitting || !formData.title || !formData.description}
              className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  {t('common.saving', 'Saving...')}
                </>
              ) : (
                <>
                  <Save size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('businessIdeas.form.submit', 'Create Business Idea')}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewBusinessIdeaPage;
