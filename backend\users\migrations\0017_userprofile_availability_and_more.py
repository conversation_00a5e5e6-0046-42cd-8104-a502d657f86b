# Generated by Django 5.2.1 on 2025-07-23 03:05

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0016_userprofile_requested_role_name"),
    ]

    operations = [
        migrations.AddField(
            model_name="userprofile",
            name="availability",
            field=models.CharField(
                blank=True,
                choices=[
                    ("1-2-hours", "1-2 hours per week"),
                    ("3-5-hours", "3-5 hours per week"),
                    ("5+-hours", "5+ hours per week"),
                    ("flexible", "Flexible"),
                ],
                help_text="Time availability for mentoring",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="business_description",
            field=models.TextField(
                blank=True,
                help_text="Description of the business or startup idea",
                max_length=1000,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="business_name",
            field=models.Char<PERSON>ield(
                blank=True,
                help_text="Name of the business or startup",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="business_stage",
            field=models.CharField(
                blank=True,
                choices=[
                    ("idea", "Idea"),
                    ("prototype", "Prototype"),
                    ("mvp", "MVP"),
                    ("growth", "Growth"),
                    ("established", "Established"),
                ],
                help_text="Current stage of the business",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="funding_needed",
            field=models.CharField(
                blank=True,
                choices=[
                    ("none", "No funding needed"),
                    ("under-50k", "Under $50K"),
                    ("50k-250k", "$50K - $250K"),
                    ("250k-1m", "$250K - $1M"),
                    ("over-1m", "Over $1M"),
                ],
                help_text="Amount of funding needed",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="goals",
            field=models.TextField(
                blank=True,
                help_text="Goals and objectives on the platform",
                max_length=1000,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="interests",
            field=models.TextField(
                blank=True,
                help_text="Areas of interest in entrepreneurship",
                max_length=1000,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="investment_criteria",
            field=models.TextField(
                blank=True,
                help_text="Investment criteria and requirements",
                max_length=1000,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="investment_range",
            field=models.CharField(
                blank=True,
                choices=[
                    ("10k-50k", "$10K - $50K"),
                    ("50k-250k", "$50K - $250K"),
                    ("250k-1m", "$250K - $1M"),
                    ("1m-5m", "$1M - $5M"),
                    ("over-5m", "Over $5M"),
                ],
                help_text="Typical investment range",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="investment_stage",
            field=models.CharField(
                blank=True,
                choices=[
                    ("pre-seed", "Pre-seed"),
                    ("seed", "Seed"),
                    ("series-a", "Series A"),
                    ("series-b", "Series B"),
                    ("growth", "Growth"),
                ],
                help_text="Preferred investment stage",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="mentor_experience",
            field=models.CharField(
                blank=True,
                choices=[
                    ("1-3", "1-3 years"),
                    ("4-7", "4-7 years"),
                    ("8-15", "8-15 years"),
                    ("15+", "15+ years"),
                ],
                help_text="Years of professional experience for mentoring",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="mentorship_areas",
            field=models.TextField(
                blank=True,
                help_text="Areas where mentor can provide guidance",
                max_length=1000,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="preferred_industries",
            field=models.TextField(
                blank=True,
                help_text="Industries of interest for investment",
                max_length=1000,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="expertise",
            field=models.CharField(
                blank=True, help_text="Areas of expertise", max_length=200, null=True
            ),
        ),
        migrations.AlterField(
            model_name="userrole",
            name="name",
            field=models.CharField(
                choices=[
                    ("super_admin", "Super Administrator"),
                    ("admin", "Administrator"),
                    ("moderator", "Moderator"),
                    ("entrepreneur", "Entrepreneur"),
                    ("mentor", "Mentor"),
                    ("investor", "Investor"),
                    ("user", "Regular User"),
                ],
                max_length=50,
                unique=True,
            ),
        ),
    ]
