/**
 * Application Functionality Tests
 * Tests core app functionality after error boundary fixes
 */

import { test, expect, Page } from '@playwright/test';

test.describe('Application Functionality', () => {
  let page: Page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
  });

  test('should load the homepage without errors', async () => {
    const consoleErrors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Check that the page loaded successfully
    expect(await page.locator('body').isVisible()).toBe(true);
    
    // Check for critical console errors
    const criticalErrors = consoleErrors.filter(error =>
      !error.includes('favicon') && // Ignore favicon errors
      !error.includes('manifest') && // Ignore manifest errors
      !error.includes('service-worker') // Ignore SW errors in tests
    );
    
    expect(criticalErrors).toHaveLength(0);
  });

  test('should navigate between main sections', async () => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test navigation to different sections
    const navigationTests = [
      { selector: 'a[href*="/dashboard"], button:has-text("Dashboard")', expectedUrl: '/dashboard' },
      { selector: 'a[href*="/business-plans"], button:has-text("Business Plans")', expectedUrl: '/business-plans' },
      { selector: 'a[href*="/templates"], button:has-text("Templates")', expectedUrl: '/templates' },
    ];

    for (const nav of navigationTests) {
      try {
        // Try to find and click navigation element
        const navElement = page.locator(nav.selector).first();
        if (await navElement.isVisible({ timeout: 5000 })) {
          await navElement.click();
          await page.waitForLoadState('networkidle');
          
          // Verify navigation worked
          const currentUrl = page.url();
          expect(currentUrl).toContain(nav.expectedUrl);
          
          // Verify page loaded without errors
          expect(await page.locator('body').isVisible()).toBe(true);
        }
      } catch (error) {
        console.log(`Navigation test skipped for ${nav.expectedUrl}: ${error.message}`);
      }
    }
  });

  test('should handle registration form', async () => {
    await page.goto('/register');
    await page.waitForLoadState('networkidle');

    // Check if registration form is present
    const registrationForm = page.locator('form, [data-testid*="register"]').first();
    
    if (await registrationForm.isVisible({ timeout: 5000 })) {
      // Fill out basic registration fields if they exist
      const fields = [
        { name: 'firstName', value: 'Test' },
        { name: 'lastName', value: 'User' },
        { name: 'email', value: '<EMAIL>' },
        { name: 'username', value: 'testuser' },
        { name: 'password', value: 'testpass123' },
        { name: 'confirmPassword', value: 'testpass123' }
      ];

      for (const field of fields) {
        const input = page.locator(`input[name="${field.name}"]`);
        if (await input.isVisible({ timeout: 2000 })) {
          await input.fill(field.value);
        }
      }

      // Try to submit (expect it to fail due to backend, but shouldn't crash)
      const submitButton = page.locator('button[type="submit"], button:has-text("Register")').first();
      if (await submitButton.isVisible()) {
        await submitButton.click();
        await page.waitForTimeout(2000);
        
        // Should show error message but not crash
        expect(await page.locator('body').isVisible()).toBe(true);
      }
    }
  });

  test('should handle login form', async () => {
    await page.goto('/login');
    await page.waitForLoadState('networkidle');

    // Check if login form is present
    const loginForm = page.locator('form, [data-testid*="login"]').first();
    
    if (await loginForm.isVisible({ timeout: 5000 })) {
      // Fill out login fields if they exist
      const usernameField = page.locator('input[name="username"], input[name="email"], input[type="email"]').first();
      const passwordField = page.locator('input[name="password"], input[type="password"]').first();
      
      if (await usernameField.isVisible({ timeout: 2000 })) {
        await usernameField.fill('<EMAIL>');
      }
      
      if (await passwordField.isVisible({ timeout: 2000 })) {
        await passwordField.fill('testpass123');
      }

      // Try to submit (expect it to fail due to backend, but shouldn't crash)
      const submitButton = page.locator('button[type="submit"], button:has-text("Login")').first();
      if (await submitButton.isVisible()) {
        await submitButton.click();
        await page.waitForTimeout(2000);
        
        // Should show error message but not crash
        expect(await page.locator('body').isVisible()).toBe(true);
      }
    }
  });

  test('should handle responsive design', async () => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    expect(await page.locator('body').isVisible()).toBe(true);

    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000);
    expect(await page.locator('body').isVisible()).toBe(true);

    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    expect(await page.locator('body').isVisible()).toBe(true);
  });

  test('should handle language switching if available', async () => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Look for language switcher
    const languageSwitcher = page.locator('[data-testid*="language"], button:has-text("العربية"), button:has-text("English")').first();
    
    if (await languageSwitcher.isVisible({ timeout: 5000 })) {
      await languageSwitcher.click();
      await page.waitForTimeout(1000);
      
      // App should still be functional after language switch
      expect(await page.locator('body').isVisible()).toBe(true);
    }
  });

  test('should handle theme switching if available', async () => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Look for theme switcher
    const themeSwitcher = page.locator('[data-testid*="theme"], button:has-text("Dark"), button:has-text("Light")').first();
    
    if (await themeSwitcher.isVisible({ timeout: 5000 })) {
      await themeSwitcher.click();
      await page.waitForTimeout(1000);
      
      // App should still be functional after theme switch
      expect(await page.locator('body').isVisible()).toBe(true);
    }
  });

  test('should load without memory leaks during navigation', async () => {
    // Navigate through multiple pages to test for memory leaks
    const routes = ['/', '/dashboard', '/business-plans', '/templates', '/settings'];
    
    for (let i = 0; i < 3; i++) { // Repeat navigation cycle
      for (const route of routes) {
        await page.goto(route);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(500);
        
        // Verify page is still responsive
        expect(await page.locator('body').isVisible()).toBe(true);
      }
    }
  });

  test('should handle browser back/forward navigation', async () => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Navigate to different pages
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    await page.goto('/business-plans');
    await page.waitForLoadState('networkidle');
    
    // Test back navigation
    await page.goBack();
    await page.waitForLoadState('networkidle');
    expect(page.url()).toContain('/dashboard');
    
    // Test forward navigation
    await page.goForward();
    await page.waitForLoadState('networkidle');
    expect(page.url()).toContain('/business-plans');
    
    // App should remain functional
    expect(await page.locator('body').isVisible()).toBe(true);
  });
});
