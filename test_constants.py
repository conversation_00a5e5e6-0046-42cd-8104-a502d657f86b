"""
CENTRALIZED TEST CONSTANTS
Single source of truth for all role-related constants in test files
Mirrors the frontend roleConstants.ts structure
"""

# ===== ROLE DEFINITIONS =====
ALL_ROLES = ['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin']

BUSINESS_ROLES = ['user', 'entrepreneur', 'mentor', 'investor']

ADMIN_ROLES = ['admin', 'super_admin']

ELEVATED_ROLES = ['moderator', 'admin', 'super_admin']

# Single role arrays for specific features
ENTREPRENEUR_ONLY = ['entrepreneur']
MENTOR_ONLY = ['mentor']
INVESTOR_ONLY = ['investor']
MODERATOR_ONLY = ['moderator']
SUPER_ADMIN_ONLY = ['super_admin']

# Extended business roles (includes admins for oversight)
BUSINESS_ROLES_EXTENDED = ['entrepreneur', 'mentor', 'investor', 'admin', 'super_admin']

# Content access roles (business + moderators)
CONTENT_ACCESS_ROLES = ['user', 'entrepreneur', 'mentor', 'investor', 'moderator']

# Analytics access roles (business + elevated)
ANALYTICS_ACCESS_ROLES = ['user', 'entrepreneur', 'mentor', 'investor', 'admin', 'moderator']

# ===== ROUTE MAPPINGS =====
ROLE_DASHBOARD_ROUTES = {
    'super_admin': '/super_admin',
    'admin': '/admin',
    'moderator': '/dashboard/moderation',
    'entrepreneur': '/dashboard/entrepreneur',
    'mentor': '/dashboard/mentorship',
    'investor': '/dashboard/investments',
    'user': '/dashboard'
}

ROLE_AI_ROUTES = {
    'super_admin': '/super_admin/ai',
    'admin': '/admin/ai',
    'moderator': '/moderator/ai',
    'entrepreneur': '/entrepreneur/ai',
    'mentor': '/mentor/ai',
    'investor': '/investor/ai',
    'user': '/user/ai'
}

# ===== ROUTE GROUPS =====
COMMON_ROUTES = ['/dashboard', '/profile', '/settings', '/dashboard/forums']

BUSINESS_ROUTES = [
    '/dashboard/business-ideas',
    '/dashboard/business-plans', 
    '/dashboard/incubator',
    '/dashboard/posts',
    '/dashboard/events',
    '/dashboard/resources',
    '/dashboard/analytics'
]

ADMIN_ROUTES = [
    '/admin',
    '/admin/users',
    '/admin/analytics', 
    '/admin/settings',
    '/admin/ai-system'
]

SUPER_ADMIN_ROUTES = [
    '/super_admin',
    '/super_admin/system-control',
    '/super_admin/monitoring'
]

MODERATION_ROUTES = [
    '/dashboard/moderation',
    '/dashboard/moderation/content',
    '/dashboard/moderation/users',
    '/dashboard/moderation/forum',
    '/dashboard/moderation/reports',
    '/dashboard/moderation/analytics'
]

# ===== EXPECTED AI ROUTES FOR VERIFICATION =====
EXPECTED_AI_ROUTES = [
    "'/user/ai'",
    "'/entrepreneur/ai'", 
    "'/mentor/ai'",
    "'/investor/ai'",
    "'/moderator/ai'",
    "'/admin/ai'",
    "'/super_admin/ai'"
]

EXPECTED_AI_ROUTE_MAPPINGS = [
    "path: '/user/ai'",
    "path: '/entrepreneur/ai'",
    "path: '/mentor/ai'", 
    "path: '/investor/ai'",
    "path: '/moderator/ai'",
    "path: '/admin/ai'",
    "path: '/super_admin/ai'"
]

# ===== UTILITY FUNCTIONS =====

def get_ai_route_for_role(role, sub_path=''):
    """Get AI route for a specific role and sub-path"""
    base_path = ROLE_AI_ROUTES.get(role, ROLE_AI_ROUTES['user'])
    return f"{base_path}{sub_path}"

def get_dashboard_route_for_role(role):
    """Get dashboard route for a specific role"""
    return ROLE_DASHBOARD_ROUTES.get(role, ROLE_DASHBOARD_ROUTES['user'])

def is_role_in_group(role, group):
    """Check if role is in a specific group"""
    return role in group

def get_all_ai_routes_for_role(role):
    """Get all AI routes for a specific role"""
    base_route = ROLE_AI_ROUTES.get(role, ROLE_AI_ROUTES['user'])
    return [
        base_route,
        f"{base_route}/dashboard",
        f"{base_route}/analytics",
        f"{base_route}/intelligence"
    ]

def is_ai_route(path):
    """Check if a route is an AI route"""
    return any(path.startswith(route) for route in ROLE_AI_ROUTES.values())

def get_role_from_ai_route(path):
    """Get role from AI route path"""
    for role, route in ROLE_AI_ROUTES.items():
        if path.startswith(route):
            return role
    return None

def has_elevated_permissions(role):
    """Check if role has elevated permissions"""
    return role in ELEVATED_ROLES

def is_business_role(role):
    """Check if role is business-focused"""
    return role in BUSINESS_ROLES

def is_admin_role(role):
    """Check if role is admin-level"""
    return role in ADMIN_ROLES
