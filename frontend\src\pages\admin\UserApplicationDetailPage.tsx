/**
 * User Application Detail Page
 * Detailed view for reviewing individual user applications and role requests
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../../components/ui/ArabicOptimizedComponents';
import { 
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Users,
  DollarSign,
  Shield,
  ExternalLink,
  MessageSquare,
  FileText,
  Star,
  Award
} from 'lucide-react';

interface UserDetail {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  location: string;
  registeredAt: string;
  emailVerified: boolean;
  status: 'active' | 'pending' | 'suspended';
  lastActive?: string;
  profilePicture?: string;
  roleApplications: Array<{
    role: 'user' | 'mentor' | 'investor' | 'moderator';
    status: 'active' | 'pending' | 'rejected';
    appliedAt: string;
    approvedAt?: string;
    approvedBy?: string;
    rejectedAt?: string;
    rejectedBy?: string;
    rejectionReason?: string;
    additionalInfo: {
      company?: string;
      industry?: string;
      experience?: string;
      linkedinProfile?: string;
      investmentCriteria?: string;
      minimumInvestment?: number;
      maximumInvestment?: number;
      yearsOfExperience?: number;
      moderationExperience?: string;
      motivation?: string;
      qualifications?: string;
      portfolioUrl?: string;
    };
  }>;
  adminNotes: Array<{
    id: string;
    adminId: string;
    adminName: string;
    note: string;
    createdAt: string;
    type: 'info' | 'warning' | 'approval' | 'rejection';
  }>;
}

const UserApplicationDetailPage: React.FC = () => {
  const { userId } = useParams();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [user, setUser] = useState<UserDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedApplication, setSelectedApplication] = useState<string>('');
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showRejectionModal, setShowRejectionModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [adminNote, setAdminNote] = useState('');

  useEffect(() => {
    loadUserDetail();
  }, [userId]);

  const loadUserDetail = async () => {
    try {
      // Mock data - replace with actual API call
      const mockUser: UserDetail = {
        id: userId || '1',
        firstName: 'Ahmed',
        lastName: 'Al-Rashid',
        email: '<EMAIL>',
        phone: '+966501234567',
        location: 'Riyadh, Saudi Arabia',
        registeredAt: '2024-01-20T10:30:00Z',
        emailVerified: true,
        status: 'active',
        lastActive: '2024-01-22T15:45:00Z',
        roleApplications: [
          {
            role: 'user',
            status: 'active',
            appliedAt: '2024-01-20T10:30:00Z',
            approvedAt: '2024-01-20T10:30:00Z',
            additionalInfo: {
              company: 'FinTech Revolution',
              industry: 'fintech'
            }
          },
          {
            role: 'mentor',
            status: 'pending',
            appliedAt: '2024-01-20T10:30:00Z',
            additionalInfo: {
              yearsOfExperience: 10,
              experience: 'Senior Software Engineer with 10+ years in FinTech. Led multiple successful product launches and mentored junior developers.',
              linkedinProfile: 'https://linkedin.com/in/ahmed-alrashid'
            }
          }
        ],
        adminNotes: [
          {
            id: '1',
            adminId: 'admin1',
            adminName: 'Sarah Admin',
            note: 'Strong technical background. LinkedIn profile verified.',
            createdAt: '2024-01-21T09:00:00Z',
            type: 'info'
          }
        ]
      };

      setUser(mockUser);
      // Set first pending application as selected
      const pendingApp = mockUser.roleApplications.find(app => app.status === 'pending');
      if (pendingApp) {
        setSelectedApplication(pendingApp.role);
      }
    } catch (error) {
      console.error('Error loading user detail:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApproveApplication = async (role: string) => {
    try {
      // Approve application - replace with actual API call
      console.log('Approving application:', userId, role, adminNote);
      
      if (user) {
        setUser({
          ...user,
          roleApplications: user.roleApplications.map(app => 
            app.role === role 
              ? { 
                  ...app, 
                  status: 'active' as const, 
                  approvedAt: new Date().toISOString(),
                  approvedBy: 'current-admin'
                }
              : app
          ),
          adminNotes: [
            ...user.adminNotes,
            {
              id: Date.now().toString(),
              adminId: 'current-admin',
              adminName: 'Current Admin',
              note: adminNote || `Approved ${role} application`,
              createdAt: new Date().toISOString(),
              type: 'approval'
            }
          ]
        });
      }
      
      setShowApprovalModal(false);
      setAdminNote('');
    } catch (error) {
      console.error('Error approving application:', error);
    }
  };

  const handleRejectApplication = async (role: string) => {
    try {
      // Reject application - replace with actual API call
      console.log('Rejecting application:', userId, role, rejectionReason);
      
      if (user) {
        setUser({
          ...user,
          roleApplications: user.roleApplications.map(app => 
            app.role === role 
              ? { 
                  ...app, 
                  status: 'rejected' as const, 
                  rejectedAt: new Date().toISOString(),
                  rejectedBy: 'current-admin',
                  rejectionReason
                }
              : app
          ),
          adminNotes: [
            ...user.adminNotes,
            {
              id: Date.now().toString(),
              adminId: 'current-admin',
              adminName: 'Current Admin',
              note: `Rejected ${role} application: ${rejectionReason}`,
              createdAt: new Date().toISOString(),
              type: 'rejection'
            }
          ]
        });
      }
      
      setShowRejectionModal(false);
      setRejectionReason('');
    } catch (error) {
      console.error('Error rejecting application:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'mentor':
        return <Users className="w-5 h-5" />;
      case 'investor':
        return <DollarSign className="w-5 h-5" />;
      case 'moderator':
        return <Shield className="w-5 h-5" />;
      default:
        return <User className="w-5 h-5" />;
    }
  };

  const getRoleColor = (role: string, status: string) => {
    if (status === 'pending') return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    if (status === 'rejected') return 'bg-red-100 text-red-800 border-red-200';
    if (status === 'active') return 'bg-green-100 text-green-800 border-green-200';
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getRoleTitle = (role: string) => {
    const titles = {
      en: {
        user: 'Regular User',
        mentor: 'Mentor',
        investor: 'Investor',
        moderator: 'Moderator'
      },
      ar: {
        user: 'مستخدم عادي',
        mentor: 'موجه',
        investor: 'مستثمر',
        moderator: 'مشرف'
      }
    };
    return titles[language][role as keyof typeof titles.en] || role;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
            {language === 'ar' ? 'لم يتم العثور على المستخدم' : 'User Not Found'}
          </ArabicTypography>
          <ArabicButton onClick={() => navigate('/admin/users')}>
            {language === 'ar' ? 'العودة للمستخدمين' : 'Back to Users'}
          </ArabicButton>
        </div>
      </div>
    );
  }

  const selectedApp = user.roleApplications.find(app => app.role === selectedApplication);

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <ArabicButton
              variant="ghost"
              onClick={() => navigate('/admin/users')}
              icon={<ArrowLeft className="w-4 h-4" />}
              className={isRTL ? 'ml-4' : 'mr-4'}
            >
              {language === 'ar' ? 'رجوع' : 'Back'}
            </ArabicButton>
            <div>
              <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                {user.firstName} {user.lastName}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'تفاصيل المستخدم وطلبات الأدوار' : 'User details and role applications'}
              </ArabicTypography>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* User Profile */}
          <div className="lg:col-span-1">
            <ArabicCard title={language === 'ar' ? 'الملف الشخصي' : 'Profile'}>
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">
                    {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                  </span>
                </div>
                <ArabicTypography variant="h4" className="font-bold">
                  {user.firstName} {user.lastName}
                </ArabicTypography>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold mt-2 ${
                  user.status === 'active' ? 'bg-green-100 text-green-800' :
                  user.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                } font-arabic`}>
                  {language === 'ar' 
                    ? (user.status === 'active' ? 'نشط' : 
                       user.status === 'pending' ? 'معلق' : 'معلق')
                    : user.status
                  }
                </span>
              </div>

              <div className="space-y-4">
                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Mail className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <div>
                    <ArabicTypography variant="body2" className="font-semibold">
                      {user.email}
                    </ArabicTypography>
                    {user.emailVerified ? (
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-3 h-3 text-green-500 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <ArabicTypography variant="caption" className="text-green-600">
                          {language === 'ar' ? 'مؤكد' : 'Verified'}
                        </ArabicTypography>
                      </div>
                    ) : (
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <AlertCircle className={`w-3 h-3 text-red-500 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <ArabicTypography variant="caption" className="text-red-600">
                          {language === 'ar' ? 'غير مؤكد' : 'Unverified'}
                        </ArabicTypography>
                      </div>
                    )}
                  </div>
                </div>

                {user.phone && (
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Phone className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                    <ArabicTypography variant="body2">
                      {user.phone}
                    </ArabicTypography>
                  </div>
                )}

                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <MapPin className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <ArabicTypography variant="body2">
                    {user.location}
                  </ArabicTypography>
                </div>

                <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Calendar className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                  <div>
                    <ArabicTypography variant="body2" className="font-semibold">
                      {language === 'ar' ? 'تاريخ التسجيل' : 'Registered'}
                    </ArabicTypography>
                    <ArabicTypography variant="caption" color="secondary">
                      {new Date(user.registeredAt).toLocaleDateString()}
                    </ArabicTypography>
                  </div>
                </div>

                {user.lastActive && (
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Clock className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                    <div>
                      <ArabicTypography variant="body2" className="font-semibold">
                        {language === 'ar' ? 'آخر نشاط' : 'Last Active'}
                      </ArabicTypography>
                      <ArabicTypography variant="caption" color="secondary">
                        {new Date(user.lastActive).toLocaleString()}
                      </ArabicTypography>
                    </div>
                  </div>
                )}
              </div>
            </ArabicCard>

            {/* Role Applications Overview */}
            <ArabicCard title={language === 'ar' ? 'طلبات الأدوار' : 'Role Applications'} className="mt-6">
              <div className="space-y-3">
                {user.roleApplications.map((app, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedApplication(app.role)}
                    className={`w-full p-3 rounded-lg border-2 transition-all ${
                      selectedApplication === app.role 
                        ? getRoleColor(app.role, app.status)
                        : 'border-gray-200 hover:border-gray-300'
                    } ${isRTL ? 'text-right' : 'text-left'}`}
                  >
                    <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        {getRoleIcon(app.role)}
                        <span className={`font-semibold ${isRTL ? 'mr-2' : 'ml-2'} font-arabic`}>
                          {getRoleTitle(app.role)}
                        </span>
                      </div>
                      
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        {app.status === 'pending' && <Clock className="w-4 h-4 text-yellow-600" />}
                        {app.status === 'active' && <CheckCircle className="w-4 h-4 text-green-600" />}
                        {app.status === 'rejected' && <XCircle className="w-4 h-4 text-red-600" />}
                      </div>
                    </div>
                    
                    <ArabicTypography variant="caption" color="secondary" className="mt-1">
                      {language === 'ar' ? 'تقدم في:' : 'Applied:'} {new Date(app.appliedAt).toLocaleDateString()}
                    </ArabicTypography>
                  </button>
                ))}
              </div>
            </ArabicCard>
          </div>

          {/* Application Details */}
          <div className="lg:col-span-2">
            {selectedApp && (
              <ArabicCard title={`${getRoleTitle(selectedApp.role)} ${language === 'ar' ? 'طلب' : 'Application'}`}>
                <div className="space-y-6">
                  {/* Application Status */}
                  <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      {getRoleIcon(selectedApp.role)}
                      <ArabicTypography variant="h4" className={`font-bold ${isRTL ? 'mr-3' : 'ml-3'}`}>
                        {getRoleTitle(selectedApp.role)}
                      </ArabicTypography>
                    </div>
                    
                    <span className={`px-4 py-2 rounded-full text-sm font-semibold border ${getRoleColor(selectedApp.role, selectedApp.status)} font-arabic`}>
                      {language === 'ar' 
                        ? (selectedApp.status === 'pending' ? 'معلق' :
                           selectedApp.status === 'active' ? 'نشط' :
                           selectedApp.status === 'rejected' ? 'مرفوض' : selectedApp.status)
                        : selectedApp.status
                      }
                    </span>
                  </div>

                  {/* Application Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {selectedApp.additionalInfo.company && (
                      <div>
                        <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-1">
                          {language === 'ar' ? 'الشركة' : 'Company'}
                        </ArabicTypography>
                        <ArabicTypography variant="body1">
                          {selectedApp.additionalInfo.company}
                        </ArabicTypography>
                      </div>
                    )}

                    {selectedApp.additionalInfo.industry && (
                      <div>
                        <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-1">
                          {language === 'ar' ? 'الصناعة' : 'Industry'}
                        </ArabicTypography>
                        <ArabicTypography variant="body1">
                          {selectedApp.additionalInfo.industry}
                        </ArabicTypography>
                      </div>
                    )}

                    {selectedApp.additionalInfo.yearsOfExperience && (
                      <div>
                        <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-1">
                          {language === 'ar' ? 'سنوات الخبرة' : 'Years of Experience'}
                        </ArabicTypography>
                        <ArabicTypography variant="body1">
                          {selectedApp.additionalInfo.yearsOfExperience}+ {language === 'ar' ? 'سنوات' : 'years'}
                        </ArabicTypography>
                      </div>
                    )}

                    {selectedApp.additionalInfo.minimumInvestment && (
                      <div>
                        <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-1">
                          {language === 'ar' ? 'الحد الأدنى للاستثمار' : 'Minimum Investment'}
                        </ArabicTypography>
                        <ArabicTypography variant="body1">
                          ${selectedApp.additionalInfo.minimumInvestment.toLocaleString()}
                        </ArabicTypography>
                      </div>
                    )}

                    {selectedApp.additionalInfo.maximumInvestment && (
                      <div>
                        <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-1">
                          {language === 'ar' ? 'الحد الأقصى للاستثمار' : 'Maximum Investment'}
                        </ArabicTypography>
                        <ArabicTypography variant="body1">
                          ${selectedApp.additionalInfo.maximumInvestment.toLocaleString()}
                        </ArabicTypography>
                      </div>
                    )}
                  </div>

                  {/* Long text fields */}
                  {selectedApp.additionalInfo.experience && (
                    <div>
                      <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-2">
                        {language === 'ar' ? 'الخبرة' : 'Experience'}
                      </ArabicTypography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <ArabicTypography variant="body1">
                          {selectedApp.additionalInfo.experience}
                        </ArabicTypography>
                      </div>
                    </div>
                  )}

                  {selectedApp.additionalInfo.investmentCriteria && (
                    <div>
                      <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-2">
                        {language === 'ar' ? 'معايير الاستثمار' : 'Investment Criteria'}
                      </ArabicTypography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <ArabicTypography variant="body1">
                          {selectedApp.additionalInfo.investmentCriteria}
                        </ArabicTypography>
                      </div>
                    </div>
                  )}

                  {selectedApp.additionalInfo.moderationExperience && (
                    <div>
                      <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-2">
                        {language === 'ar' ? 'خبرة الإشراف' : 'Moderation Experience'}
                      </ArabicTypography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <ArabicTypography variant="body1">
                          {selectedApp.additionalInfo.moderationExperience}
                        </ArabicTypography>
                      </div>
                    </div>
                  )}

                  {selectedApp.additionalInfo.motivation && (
                    <div>
                      <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-2">
                        {language === 'ar' ? 'الدافع' : 'Motivation'}
                      </ArabicTypography>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <ArabicTypography variant="body1">
                          {selectedApp.additionalInfo.motivation}
                        </ArabicTypography>
                      </div>
                    </div>
                  )}

                  {/* External Links */}
                  {selectedApp.additionalInfo.linkedinProfile && (
                    <div>
                      <ArabicTypography variant="body2" className="font-semibold text-gray-700 mb-2">
                        {language === 'ar' ? 'ملف LinkedIn' : 'LinkedIn Profile'}
                      </ArabicTypography>
                      <a
                        href={selectedApp.additionalInfo.linkedinProfile}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`inline-flex items-center text-blue-600 hover:text-blue-700 ${isRTL ? 'flex-row-reverse' : ''}`}
                      >
                        <ExternalLink className={`w-4 h-4 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        {language === 'ar' ? 'عرض الملف الشخصي' : 'View Profile'}
                      </a>
                    </div>
                  )}

                  {/* Rejection Reason */}
                  {selectedApp.status === 'rejected' && selectedApp.rejectionReason && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <ArabicTypography variant="body2" className="font-semibold text-red-800 mb-2">
                        {language === 'ar' ? 'سبب الرفض' : 'Rejection Reason'}
                      </ArabicTypography>
                      <ArabicTypography variant="body1" className="text-red-700">
                        {selectedApp.rejectionReason}
                      </ArabicTypography>
                    </div>
                  )}

                  {/* Action Buttons */}
                  {selectedApp.status === 'pending' && (
                    <div className={`flex space-x-4 pt-6 border-t ${isRTL ? 'space-x-reverse' : ''}`}>
                      <ArabicButton
                        onClick={() => {
                          setSelectedApplication(selectedApp.role);
                          setShowApprovalModal(true);
                        }}
                        icon={<CheckCircle className="w-4 h-4" />}
                        className="flex-1"
                      >
                        {language === 'ar' ? 'موافقة' : 'Approve'}
                      </ArabicButton>
                      
                      <ArabicButton
                        variant="outline"
                        onClick={() => {
                          setSelectedApplication(selectedApp.role);
                          setShowRejectionModal(true);
                        }}
                        icon={<XCircle className="w-4 h-4" />}
                        className="flex-1"
                      >
                        {language === 'ar' ? 'رفض' : 'Reject'}
                      </ArabicButton>
                    </div>
                  )}
                </div>
              </ArabicCard>
            )}

            {/* Admin Notes */}
            <ArabicCard title={language === 'ar' ? 'ملاحظات الإدارة' : 'Admin Notes'} className="mt-6">
              <div className="space-y-4">
                {user.adminNotes.map((note) => (
                  <div key={note.id} className={`p-4 rounded-lg border ${
                    note.type === 'approval' ? 'bg-green-50 border-green-200' :
                    note.type === 'rejection' ? 'bg-red-50 border-red-200' :
                    note.type === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                    'bg-blue-50 border-blue-200'
                  }`}>
                    <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <ArabicTypography variant="body2" className="font-semibold">
                        {note.adminName}
                      </ArabicTypography>
                      <ArabicTypography variant="caption" color="secondary">
                        {new Date(note.createdAt).toLocaleString()}
                      </ArabicTypography>
                    </div>
                    <ArabicTypography variant="body1" className="mt-2">
                      {note.note}
                    </ArabicTypography>
                  </div>
                ))}

                {user.adminNotes.length === 0 && (
                  <div className="text-center py-8">
                    <MessageSquare className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <ArabicTypography variant="body1" color="secondary">
                      {language === 'ar' ? 'لا توجد ملاحظات إدارية' : 'No admin notes yet'}
                    </ArabicTypography>
                  </div>
                )}
              </div>
            </ArabicCard>
          </div>
        </div>
      </div>

      {/* Approval Modal */}
      {showApprovalModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <ArabicTypography variant="h4" className="text-gray-900 font-bold mb-4">
              {language === 'ar' ? 'موافقة على الطلب' : 'Approve Application'}
            </ArabicTypography>
            
            <ArabicTypography variant="body1" className="mb-4">
              {language === 'ar' 
                ? `هل أنت متأكد من الموافقة على طلب ${getRoleTitle(selectedApplication)}؟`
                : `Are you sure you want to approve the ${getRoleTitle(selectedApplication)} application?`
              }
            </ArabicTypography>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                {language === 'ar' ? 'ملاحظة (اختياري)' : 'Note (Optional)'}
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                rows={3}
                value={adminNote}
                onChange={(e) => setAdminNote(e.target.value)}
                placeholder={language === 'ar' ? 'أضف ملاحظة...' : 'Add a note...'}
                dir={isRTL ? 'rtl' : 'ltr'}
              />
            </div>
            
            <div className={`flex justify-end space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <ArabicButton
                variant="outline"
                onClick={() => {
                  setShowApprovalModal(false);
                  setAdminNote('');
                }}
              >
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </ArabicButton>
              
              <ArabicButton
                onClick={() => handleApproveApplication(selectedApplication)}
                icon={<CheckCircle className="w-4 h-4" />}
              >
                {language === 'ar' ? 'موافقة' : 'Approve'}
              </ArabicButton>
            </div>
          </div>
        </div>
      )}

      {/* Rejection Modal */}
      {showRejectionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <ArabicTypography variant="h4" className="text-gray-900 font-bold mb-4">
              {language === 'ar' ? 'رفض الطلب' : 'Reject Application'}
            </ArabicTypography>
            
            <ArabicTypography variant="body1" className="mb-4">
              {language === 'ar' 
                ? `هل أنت متأكد من رفض طلب ${getRoleTitle(selectedApplication)}؟`
                : `Are you sure you want to reject the ${getRoleTitle(selectedApplication)} application?`
              }
            </ArabicTypography>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                {language === 'ar' ? 'سبب الرفض' : 'Rejection Reason'}
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                rows={3}
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder={language === 'ar' ? 'اذكر سبب الرفض...' : 'Provide rejection reason...'}
                required
                dir={isRTL ? 'rtl' : 'ltr'}
              />
            </div>
            
            <div className={`flex justify-end space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <ArabicButton
                variant="outline"
                onClick={() => {
                  setShowRejectionModal(false);
                  setRejectionReason('');
                }}
              >
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </ArabicButton>
              
              <ArabicButton
                variant="outline"
                onClick={() => handleRejectApplication(selectedApplication)}
                disabled={!rejectionReason.trim()}
                icon={<XCircle className="w-4 h-4" />}
              >
                {language === 'ar' ? 'رفض' : 'Reject'}
              </ArabicButton>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserApplicationDetailPage;
