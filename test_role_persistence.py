#!/usr/bin/env python3
"""
Test script to verify role persistence and switching between different user types
Tests the complete role flow including login, role detection, and frontend state management
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8000"

def test_role_persistence_flow(username, password, expected_role):
    """Test complete role persistence flow for a user"""
    print(f"\n🔄 Testing role persistence for: {username} (Expected: {expected_role})")
    
    try:
        # Step 1: Login and get tokens
        login_response = requests.post(
            f"{BASE_URL}/api/auth/login/",
            json={
                "username": username,
                "password": password
            },
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Login Status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"   ❌ Login failed: {login_response.text}")
            return {'success': False, 'error': 'Login failed'}
        
        login_data = login_response.json()
        access_token = login_data.get('access')
        user_data = login_data.get('user', {})
        
        print(f"   ✅ Login successful!")
        print(f"   🏷️  Initial Role: {user_data.get('user_role', 'Not set')}")
        print(f"   👤 User ID: {user_data.get('id')}")
        print(f"   📧 Email: {user_data.get('email')}")
        
        # Step 2: Test token refresh (simulates session persistence)
        refresh_token = login_data.get('refresh')
        if refresh_token:
            refresh_response = requests.post(
                f"{BASE_URL}/api/auth/token/refresh/",
                json={"refresh": refresh_token},
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Token Refresh Status: {refresh_response.status_code}")
            
            if refresh_response.status_code == 200:
                new_access_token = refresh_response.json().get('access')
                print(f"   ✅ Token refresh successful!")
                access_token = new_access_token  # Use new token
            else:
                print(f"   ⚠️  Token refresh failed: {refresh_response.text}")
        
        # Step 3: Get current user (simulates frontend role detection)
        user_response = requests.get(
            f"{BASE_URL}/api/auth/user/",
            headers={
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            }
        )
        
        print(f"   Get Current User Status: {user_response.status_code}")
        
        if user_response.status_code == 200:
            current_user = user_response.json()
            detected_role = current_user.get('user_role')
            
            print(f"   🔍 Detected Role: {detected_role}")
            print(f"   🔧 Is Staff: {current_user.get('is_staff', False)}")
            print(f"   🔧 Is Superuser: {current_user.get('is_superuser', False)}")
            
            # Check profile data
            profile = current_user.get('profile', {})
            if profile:
                print(f"   📋 Profile Primary Role: {profile.get('primary_role', 'Not set')}")
                print(f"   📋 Profile Active Roles: {profile.get('active_roles', [])}")
            
            # Verify role matches expectation
            role_correct = detected_role == expected_role
            
            if role_correct:
                print(f"   ✅ Role detection CORRECT!")
            else:
                print(f"   ❌ Role detection INCORRECT!")
                print(f"      Expected: {expected_role}")
                print(f"      Detected: {detected_role}")
            
            # Step 4: Test role-based endpoint access
            test_endpoints = [
                ('/api/auth/user/', 'User profile'),
                ('/api/users/profile/', 'User profile update'),
            ]
            
            endpoint_results = []
            for endpoint, description in test_endpoints:
                endpoint_response = requests.get(
                    f"{BASE_URL}{endpoint}",
                    headers={
                        'Authorization': f'Bearer {access_token}',
                        'Content-Type': 'application/json'
                    }
                )
                
                endpoint_results.append({
                    'endpoint': endpoint,
                    'status': endpoint_response.status_code,
                    'accessible': endpoint_response.status_code in [200, 201]
                })
            
            print(f"   🔗 Endpoint Access Test:")
            for result in endpoint_results:
                status_icon = "✅" if result['accessible'] else "❌"
                print(f"      {status_icon} {result['endpoint']}: {result['status']}")
            
            return {
                'success': True,
                'username': username,
                'expected_role': expected_role,
                'detected_role': detected_role,
                'role_correct': role_correct,
                'login_data': user_data,
                'current_user_data': current_user,
                'endpoint_access': endpoint_results,
                'token_refresh_works': refresh_response.status_code == 200 if refresh_token else False
            }
            
        else:
            print(f"   ❌ Failed to get current user: {user_response.text}")
            return {
                'success': False,
                'username': username,
                'error': f'Get user failed: {user_response.text}'
            }
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {
            'success': False,
            'username': username,
            'error': str(e)
        }

def main():
    """Test role persistence for all user types"""
    print("🚀 Starting Role Persistence and Switching Tests")
    print("=" * 60)
    
    # Test users with their expected roles
    test_users = [
        {'username': 'test_user_1753259713', 'password': 'TestPassword123', 'expected_role': 'user'},
        {'username': 'test_entrepreneur_1753259715', 'password': 'TestPassword123', 'expected_role': 'entrepreneur'},
        {'username': 'test_mentor_1753259717', 'password': 'TestPassword123', 'expected_role': 'mentor'},
        {'username': 'test_investor_1753259719', 'password': 'TestPassword123', 'expected_role': 'investor'},
    ]
    
    results = []
    
    for user_info in test_users:
        result = test_role_persistence_flow(
            user_info['username'], 
            user_info['password'],
            user_info['expected_role']
        )
        results.append(result)
        time.sleep(2)  # Delay between tests
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ROLE PERSISTENCE TEST SUMMARY")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    correct_roles = [r for r in successful_tests if r.get('role_correct', False)]
    working_tokens = [r for r in successful_tests if r.get('token_refresh_works', False)]
    
    print(f"Total Tests: {len(results)}")
    print(f"Successful Logins: {len(successful_tests)}")
    print(f"Failed Tests: {len(failed_tests)}")
    print(f"Correct Role Detection: {len(correct_roles)}")
    print(f"Working Token Refresh: {len(working_tokens)}")
    
    if len(correct_roles) == len(successful_tests) and len(failed_tests) == 0:
        print("\n🎉 ALL TESTS PASSED! Role persistence is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
    
    # Detailed results
    print("\n📋 DETAILED RESULTS:")
    for result in results:
        if result['success']:
            role_icon = "✅" if result.get('role_correct', False) else "❌"
            token_icon = "🔄" if result.get('token_refresh_works', False) else "⚠️"
            print(f"   {role_icon} {result['username']}: "
                  f"Role={result.get('detected_role', 'Unknown')} "
                  f"{token_icon} Token Refresh")
        else:
            print(f"   ❌ {result['username']}: FAILED - {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
