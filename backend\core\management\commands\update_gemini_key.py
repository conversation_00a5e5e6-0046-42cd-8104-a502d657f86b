"""
Django management command to update Gemini API key
Usage: python manage.py update_gemini_key YOUR_NEW_API_KEY
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import os
import google.generativeai as genai
from core.ai_config import get_gemini_config


class Command(BaseCommand):
    help = 'Update Gemini API key and test the connection'

    def add_arguments(self, parser):
        parser.add_argument(
            'api_key',
            type=str,
            help='Your new Gemini API key (starts with AIzaSy...)'
        )
        parser.add_argument(
            '--test-only',
            action='store_true',
            help='Only test the API key without updating configuration'
        )

    def handle(self, *args, **options):
        api_key = options['api_key']
        test_only = options['test_only']

        # Validate API key format
        if not api_key.startswith('AIzaSy'):
            raise CommandError('Invalid API key format. Gemini API keys should start with "AIzaSy"')

        if len(api_key) < 30:
            raise CommandError('API key seems too short. Please check your key.')

        self.stdout.write('🔑 Testing Gemini API key...')

        # Test the API key
        try:
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            # Test with a simple prompt
            response = model.generate_content("Hello! Please respond with 'API key is working correctly.'")
            
            if response and response.text:
                self.stdout.write(
                    self.style.SUCCESS('✅ API key test successful!')
                )
                self.stdout.write(f'   Response: {response.text[:100]}...')
            else:
                raise Exception('No response received from Gemini')

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ API key test failed: {str(e)}')
            )
            self.stdout.write('   Please check your API key and try again.')
            self.stdout.write('   Get a new key from: https://aistudio.google.com/app/apikey')
            return

        if test_only:
            self.stdout.write('✅ Test completed successfully. API key is valid.')
            return

        # Update .env file
        env_path = os.path.join(settings.BASE_DIR, '.env')
        
        try:
            # Read current .env file
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    lines = f.readlines()
            else:
                lines = []

            # Update or add GEMINI_API_KEY
            updated = False
            for i, line in enumerate(lines):
                if line.startswith('GEMINI_API_KEY='):
                    lines[i] = f'GEMINI_API_KEY={api_key}\n'
                    updated = True
                    break

            if not updated:
                lines.append(f'GEMINI_API_KEY={api_key}\n')

            # Write back to .env file
            with open(env_path, 'w') as f:
                f.writelines(lines)

            self.stdout.write(
                self.style.SUCCESS('✅ Updated .env file with new API key')
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to update .env file: {str(e)}')
            )
            return

        # Update database configuration
        try:
            from core.models import AIConfiguration
            from django.contrib.auth.models import User

            # Get or create admin user for configuration
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                admin_user = User.objects.filter(is_staff=True).first()

            # Update or create database configuration
            config, created = AIConfiguration.objects.get_or_create(
                provider='gemini',
                key='api_key',
                defaults={
                    'value': api_key,
                    'config_type': 'api_key',
                    'is_active': True,
                    'is_sensitive': True,
                    'description': 'Gemini API key for AI services',
                    'created_by': admin_user,
                    'updated_by': admin_user
                }
            )

            if not created:
                config.value = api_key
                config.updated_by = admin_user
                config.is_active = True
                config.save()

            self.stdout.write(
                self.style.SUCCESS('✅ Updated database configuration')
            )

        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️ Could not update database config: {str(e)}')
            )
            self.stdout.write('   The .env file has been updated, which is sufficient.')

        # Reload AI configuration
        try:
            gemini_config = get_gemini_config()
            gemini_config.reload_config()
            
            if gemini_config.is_available:
                self.stdout.write(
                    self.style.SUCCESS('✅ AI service reloaded and available!')
                )
            else:
                self.stdout.write(
                    self.style.WARNING('⚠️ AI service reloaded but not available. Check logs.')
                )

        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'⚠️ Could not reload AI config: {str(e)}')
            )
            self.stdout.write('   Please restart the Django server to apply changes.')

        # Final instructions
        self.stdout.write('')
        self.stdout.write(self.style.SUCCESS('🎉 Gemini API key update complete!'))
        self.stdout.write('')
        self.stdout.write('Next steps:')
        self.stdout.write('1. Restart your Django server if needed')
        self.stdout.write('2. Test the AI chat at http://localhost:3000/')
        self.stdout.write('3. Look for "✓ Backend" tags on AI responses')
        self.stdout.write('')
        self.stdout.write('If you encounter issues:')
        self.stdout.write('- Check the server logs for errors')
        self.stdout.write('- Verify your API key at https://aistudio.google.com/')
        self.stdout.write('- Run: python manage.py init_ai_config')
