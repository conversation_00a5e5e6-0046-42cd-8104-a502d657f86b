# PowerShell script to start the backend server
Write-Host "🚀 Starting Django Backend Server..." -ForegroundColor Green

# Get the current directory
$currentDir = Get-Location
Write-Host "Current directory: $currentDir" -ForegroundColor Yellow

# Navigate to backend directory
$backendPath = Join-Path $currentDir "backend"
Write-Host "Backend path: $backendPath" -ForegroundColor Yellow

if (Test-Path $backendPath) {
    Set-Location $backendPath
    Write-Host "✅ Found backend directory" -ForegroundColor Green
    
    # Check if manage.py exists
    if (Test-Path "manage.py") {
        Write-Host "✅ Found manage.py" -ForegroundColor Green
        Write-Host "Starting Django development server on port 8000..." -ForegroundColor Cyan
        
        # Start the Django server
        python manage.py runserver 8000
    } else {
        Write-Host "❌ manage.py not found in backend directory" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Backend directory not found: $backendPath" -ForegroundColor Red
    Write-Host "Available directories:" -ForegroundColor Yellow
    Get-ChildItem -Directory | Select-Object Name
}

# Keep the window open
Read-Host "Press Enter to exit"
