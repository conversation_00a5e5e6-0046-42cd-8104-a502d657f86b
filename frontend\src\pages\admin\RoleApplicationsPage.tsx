/**
 * Role Applications Page
 * Dedicated interface for managing role applications by type
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Users, 
  DollarSign, 
  Shield, 
  CheckCircle, 
  XCircle, 
  Clock,
  Eye,
  Calendar,
  Mail,
  MapPin,
  ExternalLink,
  Filter,
  Search,
  AlertTriangle
} from 'lucide-react';

interface RoleApplication {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  userLocation: string;
  role: 'mentor' | 'investor' | 'moderator';
  status: 'pending' | 'approved' | 'rejected';
  appliedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  priority: 'high' | 'medium' | 'low';
  additionalInfo: {
    yearsOfExperience?: number;
    experience?: string;
    linkedinProfile?: string;
    investmentCriteria?: string;
    minimumInvestment?: number;
    maximumInvestment?: number;
    moderationExperience?: string;
    motivation?: string;
    qualifications?: string;
  };
}

const RoleApplicationsPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [applications, setApplications] = useState<RoleApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<RoleApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'mentor' | 'investor' | 'moderator'>('mentor');
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedApplications, setSelectedApplications] = useState<string[]>([]);

  useEffect(() => {
    loadApplications();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [applications, activeTab, statusFilter, searchQuery]);

  const loadApplications = async () => {
    try {
      // Mock data - replace with actual API call
      const mockApplications: RoleApplication[] = [
        {
          id: '1',
          userId: 'user1',
          userName: 'Ahmed Al-Rashid',
          userEmail: '<EMAIL>',
          userLocation: 'Riyadh, Saudi Arabia',
          role: 'mentor',
          status: 'pending',
          appliedAt: '2024-01-20T10:30:00Z',
          priority: 'high',
          additionalInfo: {
            yearsOfExperience: 10,
            experience: 'Senior Software Engineer with 10+ years in FinTech. Led multiple successful product launches.',
            linkedinProfile: 'https://linkedin.com/in/ahmed-alrashid'
          }
        },
        {
          id: '2',
          userId: 'user2',
          userName: 'Sarah Johnson',
          userEmail: '<EMAIL>',
          userLocation: 'Dubai, UAE',
          role: 'investor',
          status: 'pending',
          appliedAt: '2024-01-21T14:20:00Z',
          priority: 'medium',
          additionalInfo: {
            minimumInvestment: 50000,
            maximumInvestment: 1000000,
            investmentCriteria: 'Early-stage FinTech and HealthTech startups in MENA region.',
            linkedinProfile: 'https://linkedin.com/in/sarah-johnson'
          }
        },
        {
          id: '3',
          userId: 'user3',
          userName: 'Omar Hassan',
          userEmail: '<EMAIL>',
          userLocation: 'Cairo, Egypt',
          role: 'moderator',
          status: 'pending',
          appliedAt: '2024-01-22T08:15:00Z',
          priority: 'medium',
          additionalInfo: {
            moderationExperience: 'Managed online communities for 5+ years. Experience with content moderation and conflict resolution.',
            motivation: 'Passionate about building healthy startup ecosystems.',
            qualifications: 'Community Management Certification, Psychology Background'
          }
        }
      ];

      setApplications(mockApplications);
    } catch (error) {
      console.error('Error loading applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = applications.filter(app => app.role === activeTab);

    if (statusFilter) {
      filtered = filtered.filter(app => app.status === statusFilter);
    }

    if (searchQuery) {
      filtered = filtered.filter(app =>
        app.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        app.userEmail.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredApplications(filtered);
  };

  const handleBulkApprove = async () => {
    try {
      // Bulk approve - replace with actual API call
      console.log('Bulk approving applications:', selectedApplications);
      
      setApplications(prev => prev.map(app => 
        selectedApplications.includes(app.id)
          ? { ...app, status: 'approved' as const, reviewedAt: new Date().toISOString(), reviewedBy: 'current-admin' }
          : app
      ));
      
      setSelectedApplications([]);
    } catch (error) {
      console.error('Error bulk approving:', error);
    }
  };

  const handleBulkReject = async () => {
    try {
      // Bulk reject - replace with actual API call
      console.log('Bulk rejecting applications:', selectedApplications);
      
      setApplications(prev => prev.map(app => 
        selectedApplications.includes(app.id)
          ? { ...app, status: 'rejected' as const, reviewedAt: new Date().toISOString(), reviewedBy: 'current-admin' }
          : app
      ));
      
      setSelectedApplications([]);
    } catch (error) {
      console.error('Error bulk rejecting:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'mentor':
        return <Users className="w-5 h-5" />;
      case 'investor':
        return <DollarSign className="w-5 h-5" />;
      case 'moderator':
        return <Shield className="w-5 h-5" />;
      default:
        return <Users className="w-5 h-5" />;
    }
  };

  const getRoleTitle = (role: string) => {
    const titles = {
      en: { mentor: 'Mentor', investor: 'Investor', moderator: 'Moderator' },
      ar: { mentor: 'موجه', investor: 'مستثمر', moderator: 'مشرف' }
    };
    return titles[language][role as keyof typeof titles.en] || role;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const roleStats = {
    mentor: applications.filter(app => app.role === 'mentor'),
    investor: applications.filter(app => app.role === 'investor'),
    moderator: applications.filter(app => app.role === 'moderator')
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div>
            <ArabicTypography variant="h1" className="text-gray-900 font-bold">
              {language === 'ar' ? 'طلبات الأدوار' : 'Role Applications'}
            </ArabicTypography>
            <ArabicTypography variant="body1" color="secondary">
              {language === 'ar' 
                ? 'مراجعة والموافقة على طلبات الأدوار المختلفة'
                : 'Review and approve different role applications'
              }
            </ArabicTypography>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Role Tabs */}
        <div className="mb-8">
          <div className={`flex border-b ${isRTL ? 'flex-row-reverse' : ''}`}>
            {(['mentor', 'investor', 'moderator'] as const).map((role) => (
              <button
                key={role}
                onClick={() => setActiveTab(role)}
                className={`flex items-center px-6 py-3 font-medium text-sm border-b-2 transition-colors ${isRTL ? 'flex-row-reverse' : ''} ${
                  activeTab === role
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                } font-arabic`}
              >
                <span className={isRTL ? 'ml-2' : 'mr-2'}>{getRoleIcon(role)}</span>
                {getRoleTitle(role)}
                <span className={`ml-2 px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs ${isRTL ? 'mr-2 ml-0' : ''}`}>
                  {roleStats[role].filter(app => app.status === 'pending').length}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Filters and Actions */}
        <ArabicCard className="mb-6">
          <div className={`flex flex-col md:flex-row gap-4 items-center justify-between ${isRTL ? 'md:flex-row-reverse' : ''}`}>
            <div className={`flex flex-col md:flex-row gap-4 flex-1 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
              <div className="flex-1">
                <div className="relative">
                  <Search className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} w-4 h-4 text-gray-400`} />
                  <input
                    type="text"
                    placeholder={language === 'ar' ? 'البحث عن المتقدمين...' : 'Search applicants...'}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className={`w-full ${isRTL ? 'pr-10 pl-3' : 'pl-10 pr-3'} py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic`}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  />
                </div>
              </div>
              
              <div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  dir={isRTL ? 'rtl' : 'ltr'}
                >
                  <option value="">{language === 'ar' ? 'جميع الحالات' : 'All Statuses'}</option>
                  <option value="pending">{language === 'ar' ? 'معلق' : 'Pending'}</option>
                  <option value="approved">{language === 'ar' ? 'موافق عليه' : 'Approved'}</option>
                  <option value="rejected">{language === 'ar' ? 'مرفوض' : 'Rejected'}</option>
                </select>
              </div>
            </div>

            {selectedApplications.length > 0 && (
              <div className={`flex space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                <ArabicButton
                  size="sm"
                  onClick={handleBulkApprove}
                  icon={<CheckCircle className="w-4 h-4" />}
                >
                  {language === 'ar' ? `موافقة (${selectedApplications.length})` : `Approve (${selectedApplications.length})`}
                </ArabicButton>
                
                <ArabicButton
                  size="sm"
                  variant="outline"
                  onClick={handleBulkReject}
                  icon={<XCircle className="w-4 h-4" />}
                >
                  {language === 'ar' ? `رفض (${selectedApplications.length})` : `Reject (${selectedApplications.length})`}
                </ArabicButton>
              </div>
            )}
          </div>
        </ArabicCard>

        {/* Applications List */}
        <ArabicCard>
          <div className="space-y-4">
            {filteredApplications.map((application) => (
              <div key={application.id} className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-colors">
                <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <input
                      type="checkbox"
                      checked={selectedApplications.includes(application.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedApplications([...selectedApplications, application.id]);
                        } else {
                          setSelectedApplications(selectedApplications.filter(id => id !== application.id));
                        }
                      }}
                      className={`mt-1 ${isRTL ? 'ml-4' : 'mr-4'}`}
                    />
                    
                    <div className={`w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                      <span className="text-sm font-bold text-blue-600">
                        {application.userName.split(' ').map(n => n.charAt(0)).join('')}
                      </span>
                    </div>
                    
                    <div className="flex-1">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <ArabicTypography variant="h5" className="font-semibold">
                          {application.userName}
                        </ArabicTypography>
                        
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getPriorityColor(application.priority)} ${isRTL ? 'mr-2' : 'ml-2'} font-arabic`}>
                          {language === 'ar' 
                            ? (application.priority === 'high' ? 'عالي' : 
                               application.priority === 'medium' ? 'متوسط' : 'منخفض')
                            : application.priority
                          }
                        </span>
                        
                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(application.status)} ${isRTL ? 'mr-2' : 'ml-2'} font-arabic`}>
                          {language === 'ar' 
                            ? (application.status === 'pending' ? 'معلق' :
                               application.status === 'approved' ? 'موافق عليه' : 'مرفوض')
                            : application.status
                          }
                        </span>
                      </div>
                      
                      <div className={`flex items-center mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Mail className={`w-3 h-3 text-gray-400 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <ArabicTypography variant="body2" color="secondary">
                          {application.userEmail}
                        </ArabicTypography>
                      </div>
                      
                      <div className={`flex items-center mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <MapPin className={`w-3 h-3 text-gray-400 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <ArabicTypography variant="body2" color="secondary">
                          {application.userLocation}
                        </ArabicTypography>
                      </div>
                      
                      <div className={`flex items-center mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Calendar className={`w-3 h-3 text-gray-400 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <ArabicTypography variant="body2" color="secondary">
                          {language === 'ar' ? 'تقدم في:' : 'Applied:'} {new Date(application.appliedAt).toLocaleDateString()}
                        </ArabicTypography>
                      </div>

                      {/* Role-specific information preview */}
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                        {application.role === 'mentor' && application.additionalInfo.yearsOfExperience && (
                          <ArabicTypography variant="body2">
                            <span className="font-semibold">{language === 'ar' ? 'الخبرة:' : 'Experience:'}</span> {application.additionalInfo.yearsOfExperience}+ {language === 'ar' ? 'سنوات' : 'years'}
                          </ArabicTypography>
                        )}
                        
                        {application.role === 'investor' && application.additionalInfo.minimumInvestment && (
                          <ArabicTypography variant="body2">
                            <span className="font-semibold">{language === 'ar' ? 'نطاق الاستثمار:' : 'Investment Range:'}</span> ${application.additionalInfo.minimumInvestment.toLocaleString()} - ${application.additionalInfo.maximumInvestment?.toLocaleString()}
                          </ArabicTypography>
                        )}
                        
                        {application.role === 'moderator' && application.additionalInfo.qualifications && (
                          <ArabicTypography variant="body2">
                            <span className="font-semibold">{language === 'ar' ? 'المؤهلات:' : 'Qualifications:'}</span> {application.additionalInfo.qualifications}
                          </ArabicTypography>
                        )}

                        {application.additionalInfo.linkedinProfile && (
                          <div className={`flex items-center mt-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                            <ExternalLink className={`w-3 h-3 text-blue-500 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                            <a
                              href={application.additionalInfo.linkedinProfile}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-700 text-sm"
                            >
                              {language === 'ar' ? 'ملف LinkedIn' : 'LinkedIn Profile'}
                            </a>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                    <ArabicButton
                      size="sm"
                      variant="outline"
                      onClick={() => navigate(`/admin/users/${application.userId}`)}
                      icon={<Eye className="w-3 h-3" />}
                    >
                      {language === 'ar' ? 'عرض' : 'View'}
                    </ArabicButton>
                    
                    {application.status === 'pending' && (
                      <>
                        <ArabicButton
                          size="sm"
                          onClick={() => {
                            // Quick approve
                            setApplications(prev => prev.map(app => 
                              app.id === application.id
                                ? { ...app, status: 'approved' as const, reviewedAt: new Date().toISOString() }
                                : app
                            ));
                          }}
                          icon={<CheckCircle className="w-3 h-3" />}
                        >
                          {language === 'ar' ? 'موافقة' : 'Approve'}
                        </ArabicButton>
                        
                        <ArabicButton
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            // Quick reject
                            setApplications(prev => prev.map(app => 
                              app.id === application.id
                                ? { ...app, status: 'rejected' as const, reviewedAt: new Date().toISOString() }
                                : app
                            ));
                          }}
                          icon={<XCircle className="w-3 h-3" />}
                        >
                          {language === 'ar' ? 'رفض' : 'Reject'}
                        </ArabicButton>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredApplications.length === 0 && (
            <div className="text-center py-12">
              {getRoleIcon(activeTab)}
              <div className="w-16 h-16 mx-auto mb-4 text-gray-300">
                {getRoleIcon(activeTab)}
              </div>
              <ArabicTypography variant="h4" className="text-gray-500 mb-2">
                {language === 'ar' ? 'لا توجد طلبات' : 'No applications found'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' 
                  ? `لا توجد طلبات ${getRoleTitle(activeTab)} ${statusFilter ? (statusFilter === 'pending' ? 'معلقة' : statusFilter === 'approved' ? 'موافق عليها' : 'مرفوضة') : ''}`
                  : `No ${statusFilter || ''} ${getRoleTitle(activeTab).toLowerCase()} applications found`
                }
              </ArabicTypography>
            </div>
          )}
        </ArabicCard>
      </div>
    </div>
  );
};

export default RoleApplicationsPage;
