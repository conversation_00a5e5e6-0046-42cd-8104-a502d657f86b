<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Flow Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="max-w-md w-full bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20">
        <h2 class="text-2xl font-bold text-white mb-6 text-center">Registration Flow Demo</h2>
        
        <!-- Error State Demo -->
        <div id="errorDemo" class="mb-6">
            <h3 class="text-lg font-semibold text-white mb-3">❌ Error State</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Username <span class="text-red-400">*</span>
                    </label>
                    <input
                        type="text"
                        value="test_entrepreneur"
                        class="w-full px-4 py-3 bg-white/20 border border-red-500 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-white placeholder-gray-400"
                        placeholder="Enter username"
                    />
                    <p class="text-red-400 text-sm mt-1">A user with that username already exists.</p>
                </div>
                
                <div class="bg-red-500/20 border border-red-500 rounded-lg p-4">
                    <p class="text-red-400">Registration failed: username: A user with that username already exists.</p>
                </div>
            </div>
        </div>

        <!-- Success State Demo -->
        <div id="successDemo" class="mb-6">
            <h3 class="text-lg font-semibold text-white mb-3">✅ Success State</h3>
            <div class="bg-green-500/20 border border-green-500 rounded-lg p-4">
                <div class="flex items-center mb-2">
                    <svg class="h-5 w-5 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-green-400">Account created successfully! Your application will be reviewed soon.</p>
                </div>
                <div class="text-center">
                    <p class="text-green-300 text-sm mb-3">
                        Redirecting to login page in <span id="countdown">5</span> seconds...
                    </p>
                    <button class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-colors duration-200">
                        Go to Login Now
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading State Demo -->
        <div id="loadingDemo">
            <h3 class="text-lg font-semibold text-white mb-3">⏳ Loading State</h3>
            <button
                disabled
                class="w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium text-white opacity-75 cursor-not-allowed flex items-center justify-center"
            >
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating Account...
            </button>
        </div>
    </div>

    <script>
        // Countdown demo
        let count = 5;
        const countdownElement = document.getElementById('countdown');
        
        setInterval(() => {
            count--;
            if (count <= 0) count = 5;
            countdownElement.textContent = count;
        }, 1000);
    </script>
</body>
</html>
