/**
 * ✅ SIMPLE AI Status Indicator
 * Shows AI status and links to existing AI chat - NO DUPLICATES
 */

import React, { useState } from 'react';
import { Bot, ChevronDown } from 'lucide-react';

interface AIStatusIndicatorProps {
  className?: string;
  variant?: 'navbar' | 'sidebar' | 'compact';
}

export const AIStatusIndicator: React.FC<AIStatusIndicatorProps> = ({
  className = '',
  variant = 'navbar'
}) => {
  const [showDropdown, setShowDropdown] = useState(false);
  
  // ✅ SIMPLE AI STATUS STATE
  const stats = { total_actions_today: 0 };
  const isAIRunning = true;

  const getStatusColor = () => {
    return isAIRunning ? 'bg-green-500' : 'bg-red-500';
  };

  // ✅ COMPACT VARIANT
  if (variant === 'compact') {
    return (
      <div className={`flex items-center space-x-1 ${className}`}>
        <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
        <span className="text-xs font-medium">{stats.total_actions_today}</span>
      </div>
    );
  }

  // ✅ SIDEBAR VARIANT
  if (variant === 'sidebar') {
    return (
      <div className={`p-3 bg-gray-50 dark:bg-gray-800 rounded-lg ${className}`}>
        <div className="flex items-center space-x-2">
          <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
          <Bot className="h-4 w-4 text-purple-500" />
          <span className="text-sm font-medium">AI Status</span>
        </div>
        <div className="text-xs text-gray-600 dark:text-gray-300 mt-1">
          {isAIRunning ? 'Connected' : 'Offline'}
        </div>
      </div>
    );
  }

  // ✅ NAVBAR VARIANT (DEFAULT) - SIMPLE DROPDOWN WITH LINKS
  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
      >
        <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
        <Bot className="h-4 w-4 text-purple-500" />
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          AI ({stats.total_actions_today})
        </span>
        <ChevronDown className="h-3 w-3 text-gray-500" />
      </button>

      {/* ✅ SIMPLE DROPDOWN WITH LINKS TO EXISTING AI CHAT */}
      {showDropdown && (
        <div className="absolute top-full right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
          <div className="p-4">
            {/* Header */}
            <div className="flex items-center space-x-2 mb-3">
              <Bot className="h-5 w-5 text-purple-500" />
              <span className="font-semibold text-gray-900 dark:text-white">مساعد ياسمين الذكي</span>
            </div>

            {/* Status */}
            <div className="flex items-center space-x-2 mb-4">
              <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {isAIRunning ? 'متصل' : 'غير متصل'}
              </span>
            </div>

            {/* Links to existing AI chat */}
            <div className="space-y-2">
              <a
                href="/user/ai-chat"
                className="block w-full bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-2 px-3 rounded text-center transition-colors"
                onClick={() => setShowDropdown(false)}
              >
                💬 محادثة الذكاء الاصطناعي
              </a>
              <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                {stats.total_actions_today} رسالة اليوم
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
