#!/usr/bin/env python3
"""
Database Validation Script
Validates that the database has sufficient real data for testing
and creates additional sample data if needed
"""

import os
import sys
import django
from django.utils import timezone
from datetime import timedelta
import random

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from incubator.models import BusinessIdea, IncubatorResource
from api.models import Post, Event, Resource, Tag
from users.models import UserProfile

def validate_users():
    """Validate user data"""
    print("👥 Validating Users...")
    
    user_count = User.objects.count()
    admin_count = User.objects.filter(is_superuser=True).count()
    staff_count = User.objects.filter(is_staff=True).count()
    
    print(f"   Total Users: {user_count}")
    print(f"   Admin Users: {admin_count}")
    print(f"   Staff Users: {staff_count}")
    
    if user_count < 5:
        print("   ⚠️  Low user count - creating additional users...")
        create_additional_users()
    else:
        print("   ✅ Sufficient users in database")
    
    return user_count >= 5

def create_additional_users():
    """Create additional users for testing"""
    additional_users = [
        {'username': 'entrepreneur1', 'email': '<EMAIL>', 'first_name': 'Alice', 'last_name': 'Cooper'},
        {'username': 'mentor1', 'email': '<EMAIL>', 'first_name': 'Bob', 'last_name': 'Smith'},
        {'username': 'investor1', 'email': '<EMAIL>', 'first_name': 'Carol', 'last_name': 'Johnson'},
        {'username': 'moderator1', 'email': '<EMAIL>', 'first_name': 'David', 'last_name': 'Wilson'},
    ]
    
    for user_data in additional_users:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'email': user_data['email'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
            print(f"      ✅ Created user: {user.username}")

def validate_business_ideas():
    """Validate business ideas data"""
    print("\n💡 Validating Business Ideas...")
    
    idea_count = BusinessIdea.objects.count()
    approved_count = BusinessIdea.objects.filter(status='approved').count()
    pending_count = BusinessIdea.objects.filter(status='pending').count()
    
    print(f"   Total Ideas: {idea_count}")
    print(f"   Approved Ideas: {approved_count}")
    print(f"   Pending Ideas: {pending_count}")
    
    if idea_count < 10:
        print("   ⚠️  Low business idea count - creating additional ideas...")
        create_additional_business_ideas()
    else:
        print("   ✅ Sufficient business ideas in database")
    
    return idea_count >= 10

def create_additional_business_ideas():
    """Create additional business ideas"""
    users = list(User.objects.all()[:5])  # Get first 5 users
    
    additional_ideas = [
        {
            'title': 'Smart Fitness Tracker',
            'description': 'Wearable device that tracks fitness metrics and provides personalized workout recommendations.',
            'industry': 'Health & Fitness',
            'current_stage': 'Prototype',
            'funding_needed': 400000,
            'status': 'approved'
        },
        {
            'title': 'Eco-Friendly Packaging Solutions',
            'description': 'Biodegradable packaging materials for e-commerce and retail businesses.',
            'industry': 'Sustainability',
            'current_stage': 'MVP',
            'funding_needed': 600000,
            'status': 'pending'
        },
        {
            'title': 'Virtual Reality Training Platform',
            'description': 'VR-based training platform for corporate skills development and safety training.',
            'industry': 'EdTech',
            'current_stage': 'Idea',
            'funding_needed': 800000,
            'status': 'approved'
        },
        {
            'title': 'Blockchain Supply Chain Tracker',
            'description': 'Blockchain-based solution for transparent supply chain tracking and verification.',
            'industry': 'Technology',
            'current_stage': 'Research',
            'funding_needed': 1200000,
            'status': 'pending'
        },
        {
            'title': 'AI-Powered Customer Service Bot',
            'description': 'Intelligent chatbot that provides 24/7 customer support with natural language processing.',
            'industry': 'AI/ML',
            'current_stage': 'MVP',
            'funding_needed': 350000,
            'status': 'approved'
        }
    ]
    
    for i, idea_data in enumerate(additional_ideas):
        user = users[i % len(users)]  # Distribute among users
        
        idea, created = BusinessIdea.objects.get_or_create(
            title=idea_data['title'],
            defaults={
                'user': user,
                'description': idea_data['description'],
                'industry': idea_data['industry'],
                'current_stage': idea_data['current_stage'],
                'funding_needed': idea_data['funding_needed'],
                'status': idea_data['status'],
                'created_at': timezone.now() - timedelta(days=random.randint(1, 60))
            }
        )
        
        if created:
            print(f"      ✅ Created business idea: {idea.title}")

def validate_posts():
    """Validate forum posts data"""
    print("\n📝 Validating Posts...")
    
    post_count = Post.objects.count()
    flagged_count = Post.objects.filter(is_flagged=True).count()
    
    print(f"   Total Posts: {post_count}")
    print(f"   Flagged Posts: {flagged_count}")
    
    if post_count < 15:
        print("   ⚠️  Low post count - creating additional posts...")
        create_additional_posts()
    else:
        print("   ✅ Sufficient posts in database")
    
    return post_count >= 15

def create_additional_posts():
    """Create additional forum posts"""
    users = list(User.objects.all()[:5])
    
    additional_posts = [
        {
            'title': 'Best practices for MVP development',
            'content': 'What are the key principles to follow when building a minimum viable product?',
            'is_flagged': False
        },
        {
            'title': 'Funding options for early-stage startups',
            'content': 'Looking for advice on different funding sources available for startups in the ideation phase.',
            'is_flagged': False
        },
        {
            'title': 'Market research techniques',
            'content': 'What are the most effective methods for conducting market research on a limited budget?',
            'is_flagged': False
        },
        {
            'title': 'Building a strong team',
            'content': 'How do you attract and retain top talent when you\'re a small startup?',
            'is_flagged': False
        },
        {
            'title': 'Spam content here',
            'content': 'Buy our amazing product now! Limited time offer!',
            'is_flagged': True
        }
    ]
    
    for i, post_data in enumerate(additional_posts):
        user = users[i % len(users)]
        
        post, created = Post.objects.get_or_create(
            title=post_data['title'],
            defaults={
                'author': user,
                'content': post_data['content'],
                'is_flagged': post_data['is_flagged'],
                'created_at': timezone.now() - timedelta(days=random.randint(1, 30))
            }
        )
        
        if created:
            print(f"      ✅ Created post: {post.title}")

def validate_resources():
    """Validate incubator resources"""
    print("\n📚 Validating Resources...")
    
    resource_count = IncubatorResource.objects.count()
    featured_count = IncubatorResource.objects.filter(is_featured=True).count()
    
    print(f"   Total Resources: {resource_count}")
    print(f"   Featured Resources: {featured_count}")
    
    if resource_count < 10:
        print("   ⚠️  Low resource count - creating additional resources...")
        create_additional_resources()
    else:
        print("   ✅ Sufficient resources in database")
    
    return resource_count >= 10

def create_additional_resources():
    """Create additional incubator resources"""
    additional_resources = [
        {
            'title': 'Startup Legal Checklist',
            'description': 'Comprehensive checklist covering legal requirements for new businesses.',
            'resource_type': 'template',
            'category': 'legal'
        },
        {
            'title': 'Financial Modeling for Startups',
            'description': 'Video series on creating financial models and projections.',
            'resource_type': 'video',
            'category': 'finance'
        },
        {
            'title': 'Customer Development Guide',
            'description': 'Step-by-step guide to customer development and validation.',
            'resource_type': 'article',
            'category': 'validation'
        },
        {
            'title': 'Marketing Strategy Template',
            'description': 'Template for developing comprehensive marketing strategies.',
            'resource_type': 'template',
            'category': 'marketing'
        },
        {
            'title': 'Technology Stack Selection',
            'description': 'Guide to choosing the right technology stack for your startup.',
            'resource_type': 'article',
            'category': 'technology'
        }
    ]
    
    for resource_data in additional_resources:
        resource, created = IncubatorResource.objects.get_or_create(
            title=resource_data['title'],
            defaults={
                'description': resource_data['description'],
                'resource_type': resource_data['resource_type'],
                'category': resource_data['category'],
                'is_featured': True,
                'created_at': timezone.now() - timedelta(days=random.randint(1, 90))
            }
        )
        
        if created:
            print(f"      ✅ Created resource: {resource.title}")

def generate_validation_report():
    """Generate database validation report"""
    print("\n" + "=" * 60)
    print("📊 DATABASE VALIDATION REPORT")
    print("=" * 60)
    
    # Get current counts
    user_count = User.objects.count()
    idea_count = BusinessIdea.objects.count()
    post_count = Post.objects.count()
    resource_count = IncubatorResource.objects.count()
    
    print(f"\n📈 Current Database Statistics:")
    print(f"   Users: {user_count}")
    print(f"   Business Ideas: {idea_count}")
    print(f"   Forum Posts: {post_count}")
    print(f"   Resources: {resource_count}")
    
    # Validation status
    validations = [
        ("Users", user_count >= 5),
        ("Business Ideas", idea_count >= 10),
        ("Posts", post_count >= 15),
        ("Resources", resource_count >= 10),
    ]
    
    print(f"\n✅ Validation Results:")
    all_valid = True
    for name, is_valid in validations:
        status = "✅ PASS" if is_valid else "❌ FAIL"
        print(f"   {status} {name}")
        if not is_valid:
            all_valid = False
    
    if all_valid:
        print(f"\n🎉 Database is ready for testing!")
        print("   All entities have sufficient data for API testing")
    else:
        print(f"\n⚠️  Database needs more data for comprehensive testing")
    
    print(f"\n🚀 Next Steps:")
    print("   1. Start Django server: python manage.py runserver")
    print("   2. Test API endpoints with real data")
    print("   3. Verify frontend components load real data")
    print("   4. Monitor API performance and response times")

def main():
    """Main validation function"""
    print("🚀 Starting Database Validation...")
    print("=" * 60)
    
    try:
        # Validate each data type
        validate_users()
        validate_business_ideas()
        validate_posts()
        validate_resources()
        
        # Generate report
        generate_validation_report()
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
