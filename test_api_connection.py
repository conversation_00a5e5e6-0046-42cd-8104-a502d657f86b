#!/usr/bin/env python3
"""
Test API connection and login endpoint
"""

import requests
import json

def test_api_connection():
    """Test if the API is accessible"""
    print("🔍 Testing API connection...")
    
    base_url = "http://localhost:8000"
    api_url = f"{base_url}/api"
    
    # Test basic connection
    try:
        response = requests.get(base_url, timeout=5)
        print(f"✅ Backend server is accessible: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server")
        return False
    except Exception as e:
        print(f"❌ Error connecting to backend: {e}")
        return False
    
    # Test API root
    try:
        response = requests.get(api_url, timeout=5)
        print(f"✅ API endpoint accessible: {response.status_code}")
    except Exception as e:
        print(f"⚠️ API root not accessible: {e}")
    
    return True

def test_login_endpoint():
    """Test the login endpoint directly"""
    print("\n🧪 Testing login endpoint...")
    
    login_url = "http://localhost:8000/api/auth/token/"
    
    # Test credentials that we know work
    test_credentials = [
        {"username": "admin", "password": "admin123"},
        {"username": "testentrepreneur", "password": "testpass123"},
        {"username": "testuser", "password": "testpass123"},
    ]
    
    for creds in test_credentials:
        try:
            response = requests.post(
                login_url,
                json=creds,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            print(f"\n👤 Testing {creds['username']}:")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ LOGIN SUCCESS")
                print(f"   User: {data.get('user', {}).get('username', 'Unknown')}")
                print(f"   Role: {data.get('user', {}).get('user_role', 'Unknown')}")
                print(f"   Access token: {'✅ Present' if data.get('access') else '❌ Missing'}")
            else:
                print(f"   ❌ LOGIN FAILED")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Error: {response.text}")
                    
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection error for {creds['username']}")
        except Exception as e:
            print(f"   ❌ Error testing {creds['username']}: {e}")

def test_pending_user():
    """Test login with pending user to see the error message"""
    print("\n⏳ Testing pending user (should show approval message)...")
    
    login_url = "http://localhost:8000/api/auth/token/"
    creds = {"username": "pendinguser", "password": "pending123"}
    
    try:
        response = requests.post(
            login_url,
            json=creds,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"👤 Testing {creds['username']}:")
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 403:
            try:
                error_data = response.json()
                print(f"   ✅ Expected 403 error received")
                print(f"   Message: {error_data.get('message', 'No message')}")
                print(f"   Status: {error_data.get('status', 'No status')}")
                print(f"   Days pending: {error_data.get('days_pending', 'Not provided')}")
            except:
                print(f"   Error parsing response: {response.text}")
        else:
            print(f"   ⚠️ Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error testing pending user: {e}")

if __name__ == '__main__':
    if test_api_connection():
        test_login_endpoint()
        test_pending_user()
    else:
        print("\n❌ Cannot proceed with API tests - backend server not accessible")
