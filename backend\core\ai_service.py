"""
Enhanced Centralized AI Service
Single source of truth for all AI operations with caching, rate limiting, and proper error handling
"""

import logging
import json
import hashlib
from typing import Dict, Any, Optional
from datetime import datetime
from django.core.cache import cache
from django.conf import settings

from .ai_config import get_ai_config, AIPrompts
from .ai_mock_service import get_mock_ai_service

logger = logging.getLogger(__name__)


class AIResponse:
    """Standardized AI response object"""
    def __init__(self, success: bool, data: Any = None, error: str = None,
                 service: str = 'unknown', cache_hit: bool = False,
                 user_id: Optional[int] = None):
        self.success = success
        self.data = data
        self.error = error
        self.service = service
        self.cache_hit = cache_hit
        self.user_id = user_id
        self.timestamp = datetime.now().isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        result = {
            'success': self.success,
            'service': self.service,
            'timestamp': self.timestamp,
            'cache_hit': self.cache_hit
        }

        if self.success and self.data:
            if isinstance(self.data, str):
                result['message'] = self.data
            else:
                result.update(self.data)

        if not self.success and self.error:
            result['error'] = self.error

        if self.user_id:
            result['user_id'] = self.user_id

        return result


class AICache:
    """AI response caching system"""

    @staticmethod
    def _generate_cache_key(operation: str, data: Dict[str, Any]) -> str:
        """Generate cache key for AI operations"""
        # Create a hash of the operation and data
        content = f"{operation}:{json.dumps(data, sort_keys=True)}"
        return f"ai_cache:{hashlib.md5(content.encode()).hexdigest()}"

    @staticmethod
    def get(operation: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get cached AI response"""
        try:
            cache_key = AICache._generate_cache_key(operation, data)
            return cache.get(cache_key)
        except Exception as e:
            logger.warning(f"Cache get error: {e}")
            return None

    @staticmethod
    def set(operation: str, data: Dict[str, Any], response: Dict[str, Any], timeout: int = 3600):
        """Cache AI response"""
        try:
            cache_key = AICache._generate_cache_key(operation, data)
            cache.set(cache_key, response, timeout)
        except Exception as e:
            logger.warning(f"Cache set error: {e}")


class AIRateLimiter:
    """Rate limiting for AI operations"""

    @staticmethod
    def check_rate_limit(user_id: Optional[int], operation: str) -> bool:
        """Check if user has exceeded rate limit"""
        if not user_id:
            return True  # Allow anonymous requests for now

        try:
            cache_key = f"ai_rate_limit:{user_id}:{operation}"
            current_count = cache.get(cache_key, 0)

            # Different limits for different operations
            limits = {
                'chat': 50,  # 50 chat messages per hour
                'business_analysis': 20,  # 20 analyses per hour
                'text_analysis': 30,  # 30 text analyses per hour
            }

            limit = limits.get(operation, 10)  # Default limit

            if current_count >= limit:
                return False

            # Increment counter
            cache.set(cache_key, current_count + 1, 3600)  # 1 hour timeout
            return True

        except Exception as e:
            logger.warning(f"Rate limit check error: {e}")
            return True  # Allow on error


class AIService:
    """
    Enhanced centralized AI service with caching, rate limiting, and proper error handling
    All AI functionality across the app uses this service
    """

    def __init__(self):
        self.config = get_ai_config()
        self.cache = AICache()
        self.rate_limiter = AIRateLimiter()

    def chat(self, message: str, language: str = 'en', user_id: Optional[int] = None,
             context: Optional[Dict[str, Any]] = None, chat_type: str = 'general',
             ai_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Universal chat function used across the entire application
        Enhanced with caching and rate limiting
        """
        try:
            # Check rate limit
            if not self.rate_limiter.check_rate_limit(user_id, 'chat'):
                return AIResponse(
                    success=False,
                    error="Rate limit exceeded. Please try again later.",
                    service='rate_limiter',
                    user_id=user_id
                ).to_dict()

            # Check cache first
            cache_data = {'message': message, 'language': language}
            cached_response = self.cache.get('chat', cache_data)
            if cached_response:
                cached_response['cache_hit'] = True
                return cached_response

            # ✅ PREPARE PROMPT WITH SYRIAN CONTEXT
            prompt = AIPrompts.prepare_syrian_chat_prompt(message, language, context, chat_type)

            # ✅ EXTRACT AI SETTINGS FOR GENERATION
            generation_kwargs = {}
            if ai_settings:
                if 'temperature' in ai_settings and ai_settings['temperature'] is not None:
                    generation_kwargs['temperature'] = float(ai_settings['temperature'])
                if 'max_tokens' in ai_settings and ai_settings['max_tokens'] is not None:
                    generation_kwargs['max_tokens'] = int(ai_settings['max_tokens'])
                logger.info(f"🎛️ Using custom AI settings: {generation_kwargs}")

            # ✅ USE MOCK SERVICE FOR DEVELOPMENT WHEN REAL AI IS NOT AVAILABLE
            if not self.config.is_available:
                logger.info("🔧 Real AI service not available, using mock service for development")
                mock_service = get_mock_ai_service()
                mock_response = mock_service.chat(message, 'general', language)

                if mock_response.get('success'):
                    response_data = {
                        'response': mock_response.get('response', ''),
                        'session_id': mock_response.get('session_id', ''),
                        'interaction_id': mock_response.get('interaction_id', ''),
                        'model': 'mock-development-service',
                        'language': language,
                        'is_mock': True
                    }

                    # Cache mock response
                    ai_response_obj = AIResponse(
                        success=True,
                        data=response_data,
                        service='mock-ai-service',
                        user_id=user_id
                    )

                    response_dict = ai_response_obj.to_dict()
                    self.cache.set('chat', cache_data, response_dict, timeout=300)  # Shorter cache for mock
                    return response_dict
                else:
                    return AIResponse(
                        success=False,
                        error=f"Mock AI service error: {mock_response.get('error', 'Unknown error')}",
                        service='mock-error',
                        user_id=user_id
                    ).to_dict()

            try:
                # ✅ GENERATE AI RESPONSE WITH CUSTOM SETTINGS
                ai_response = self.config.generate_content(prompt, **generation_kwargs)
                if not ai_response:
                    return AIResponse(
                        success=False,
                        error="AI service failed to generate response.",
                        service='error',
                        user_id=user_id
                    ).to_dict()
            except Exception as e:
                # Return proper error instead of masking with fallback
                logger.error(f"AI service error: {e}")
                return AIResponse(
                    success=False,
                    error=f"AI service error: {str(e)}",
                    service='error',
                    user_id=user_id
                ).to_dict()

            response_data = {
                'message': ai_response.strip(),
                'language': language
            }

            result = AIResponse(
                success=True,
                data=response_data,
                service='gemini',
                user_id=user_id
            ).to_dict()

            # Cache the response
            self.cache.set('chat', cache_data, result)

            return result

        except Exception as e:
            logger.error(f"Chat error: {e}")
            logger.error(f"Chat error type: {type(e).__name__}")
            import traceback
            logger.error(f"Chat error traceback: {traceback.format_exc()}")
            return AIResponse(
                success=False,
                error=f"AI service error: {str(e)}",
                service='error',
                user_id=user_id
            ).to_dict()
    
    def analyze_business(self, business_idea: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Universal business analysis function used across the entire application
        Enhanced with caching and rate limiting
        """
        try:
            # Check rate limit
            if not self.rate_limiter.check_rate_limit(user_id, 'business_analysis'):
                return AIResponse(
                    success=False,
                    error="Rate limit exceeded. Please try again later.",
                    service='rate_limiter',
                    user_id=user_id
                ).to_dict()

            # Check cache first
            cache_data = {'business_idea': business_idea, 'language': language}
            cached_response = self.cache.get('business_analysis', cache_data)
            if cached_response:
                cached_response['cache_hit'] = True
                return cached_response

            # Prepare prompt using centralized prompts
            prompt = AIPrompts.prepare_business_analysis_prompt(business_idea, language)

            # Use real AI service only
            if not self.config.is_available:
                return AIResponse(
                    success=False,
                    error="AI service is not available. Please check configuration.",
                    service='error',
                    user_id=user_id
                ).to_dict()

            ai_response = self.config.generate_content(prompt)
            if not ai_response:
                return AIResponse(
                    success=False,
                    error="AI service failed to generate business analysis.",
                    service='error',
                    user_id=user_id
                ).to_dict()

            response_data = {
                'analysis': ai_response.strip(),
                'language': language,
                'business_idea': business_idea[:100] + '...' if len(business_idea) > 100 else business_idea
            }

            result = AIResponse(
                success=True,
                data=response_data,
                service='gemini',
                user_id=user_id
            ).to_dict()

            # Cache the response (longer timeout for business analysis)
            self.cache.set('business_analysis', cache_data, result, timeout=7200)  # 2 hours

            return result

        except Exception as e:
            logger.error(f"Business analysis error: {e}")
            return AIResponse(
                success=False,
                error=str(e),
                service='error',
                user_id=user_id
            ).to_dict()
    
    def analyze_text(self, text: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Universal text analysis function used across the entire application
        Enhanced with caching and rate limiting
        """
        try:
            # Check rate limit
            if not self.rate_limiter.check_rate_limit(user_id, 'text_analysis'):
                return AIResponse(
                    success=False,
                    error="Rate limit exceeded. Please try again later.",
                    service='rate_limiter',
                    user_id=user_id
                ).to_dict()

            # Check cache first
            cache_data = {'text': text, 'language': language}
            cached_response = self.cache.get('text_analysis', cache_data)
            if cached_response:
                cached_response['cache_hit'] = True
                return cached_response

            # Prepare prompt using centralized prompts
            prompt = AIPrompts.prepare_text_analysis_prompt(text, language)

            # Use real AI service only
            if not self.config.is_available:
                return AIResponse(
                    success=False,
                    error="AI service is not available. Please check configuration.",
                    service='error',
                    user_id=user_id
                ).to_dict()

            ai_response = self.config.generate_content(prompt)
            if not ai_response:
                return AIResponse(
                    success=False,
                    error="AI service failed to generate text analysis.",
                    service='error',
                    user_id=user_id
                ).to_dict()

            response_data = {
                'analysis': ai_response.strip(),
                'language': language,
                'text_length': len(text)
            }

            result = AIResponse(
                success=True,
                data=response_data,
                service='gemini',
                user_id=user_id
            ).to_dict()

            # Cache the response
            self.cache.set('text_analysis', cache_data, result)

            return result

        except Exception as e:
            logger.error(f"Text analysis error: {e}")
            return AIResponse(
                success=False,
                error=str(e),
                service='error',
                user_id=user_id
            ).to_dict()
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get comprehensive AI service status with enhanced metrics
        """
        config_status = self.config.get_status()

        # ✅ CHECK MOCK SERVICE AVAILABILITY
        mock_available = True
        service_type = 'production' if self.config.is_available else 'development-mock'

        return {
            **config_status,
            'is_available': True,  # Always available (real or mock)
            'service_type': service_type,
            'mock_service_available': mock_available,
            'centralized_service': True,
            'version': '2.0.0',  # Enhanced version
            'endpoints': {
                'chat': 'Universal chat with caching and rate limiting',
                'business_analysis': 'Enhanced business analysis with intelligent caching',
                'text_analysis': 'Smart text analysis with context awareness',
                'status': 'Comprehensive service status and health metrics',
                'intelligent_features': 'Advanced AI capabilities'
            },
            'features': {
                'chat': True,
                'business_analysis': True,
                'text_analysis': True,
                'multilingual': True,
                'centralized_config': True,
                'caching': True,
                'rate_limiting': True,
                'error_handling': True,
                'context_awareness': True,
                'intelligent_responses': True
            },
            'performance': {
                'caching_enabled': True,
                'rate_limiting_enabled': True,
                'error_recovery': True
            },
            'timestamp': datetime.now().isoformat()
        }

    def generate_intelligent_content(self, content_type: str, context: Dict[str, Any],
                                   language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Generate intelligent content based on type and context
        New advanced AI feature for business intelligence
        """
        try:
            # Check rate limit
            if not self.rate_limiter.check_rate_limit(user_id, 'intelligent_content'):
                return AIResponse(
                    success=False,
                    error="Rate limit exceeded. Please try again later.",
                    service='rate_limiter',
                    user_id=user_id
                ).to_dict()

            # Check cache first
            cache_data = {'content_type': content_type, 'context': context, 'language': language}
            cached_response = self.cache.get('intelligent_content', cache_data)
            if cached_response:
                cached_response['cache_hit'] = True
                return cached_response

            # Generate content based on type
            if content_type == 'business_plan_section':
                prompt = self._build_business_plan_prompt(context, language)
            elif content_type == 'market_analysis':
                prompt = self._build_market_analysis_prompt(context, language)
            elif content_type == 'financial_projection':
                prompt = self._build_financial_projection_prompt(context, language)
            elif content_type == 'risk_assessment':
                prompt = self._build_risk_assessment_prompt(context, language)
            # ========================================
            # PHASE 1 DIFFERENTIATION CONTENT TYPES
            # ========================================
            elif content_type == 'mentor_matching':
                prompt = self._build_mentor_matching_prompt(context, language)
            elif content_type == 'mentorship_success_prediction':
                prompt = self._build_mentorship_success_prompt(context, language)
            elif content_type == 'schedule_optimization':
                prompt = self._build_schedule_optimization_prompt(context, language)
            elif content_type == 'mentorship_plan_generation':
                prompt = self._build_mentorship_plan_prompt(context, language)
            elif content_type == 'business_plan_executive_summary':
                prompt = self._build_business_plan_executive_summary_prompt(context, language)
            elif content_type == 'business_plan_market_analysis':
                prompt = self._build_business_plan_market_analysis_prompt(context, language)
            elif content_type == 'business_plan_business_model':
                prompt = self._build_business_plan_business_model_prompt(context, language)
            elif content_type == 'business_plan_financial_projections':
                prompt = self._build_business_plan_financial_projections_prompt(context, language)
            elif content_type == 'business_plan_marketing_strategy':
                prompt = self._build_business_plan_marketing_strategy_prompt(context, language)
            else:
                prompt = f"Generate {content_type} content based on: {context}"

            # Use real AI service only
            if not self.config.is_available:
                return AIResponse(
                    success=False,
                    error="AI service is not available. Please check configuration.",
                    service='error',
                    user_id=user_id
                ).to_dict()

            ai_response = self.config.generate_content(prompt)
            if not ai_response:
                return AIResponse(
                    success=False,
                    error=f"AI service failed to generate {content_type} content.",
                    service='error',
                    user_id=user_id
                ).to_dict()

            response_data = {
                'content': ai_response.strip(),
                'content_type': content_type,
                'language': language,
                'context_used': context
            }

            result = AIResponse(
                success=True,
                data=response_data,
                service='gemini',
                user_id=user_id
            ).to_dict()

            # Cache the response (longer timeout for intelligent content)
            self.cache.set('intelligent_content', cache_data, result, timeout=10800)  # 3 hours

            return result

        except Exception as e:
            logger.error(f"Intelligent content generation error: {e}")
            return AIResponse(
                success=False,
                error=str(e),
                service='error',
                user_id=user_id
            ).to_dict()
    
    def is_available(self) -> bool:
        """Check if AI service is available"""
        return self.config.is_available

    def reload_config(self):
        """Reload AI configuration"""
        self.config.reload_config()

    # Helper methods for intelligent content generation
    def _build_business_plan_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build prompt for business plan section generation"""
        business_idea = context.get('business_idea', '')
        section_type = context.get('section_type', '')
        industry = context.get('industry', '')

        if language == 'ar':
            return f"""
            اكتب قسم {section_type} لخطة عمل لفكرة العمل التالية:
            فكرة العمل: {business_idea}
            الصناعة: {industry}

            يجب أن يكون المحتوى مفصلاً ومهنياً ومناسباً للصناعة المحددة.
            """
        else:
            return f"""
            Write a {section_type} section for a business plan for the following business idea:
            Business Idea: {business_idea}
            Industry: {industry}

            The content should be detailed, professional, and industry-appropriate.
            """

    def _build_market_analysis_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build prompt for market analysis generation"""
        business_idea = context.get('business_idea', '')
        target_market = context.get('target_market', '')

        if language == 'ar':
            return f"""
            قم بإجراء تحليل سوق شامل لفكرة العمل التالية:
            فكرة العمل: {business_idea}
            السوق المستهدف: {target_market}

            يجب أن يشمل التحليل: حجم السوق، المنافسين، الاتجاهات، والفرص.
            """
        else:
            return f"""
            Conduct a comprehensive market analysis for the following business idea:
            Business Idea: {business_idea}
            Target Market: {target_market}

            The analysis should include: market size, competitors, trends, and opportunities.
            """

    def _build_financial_projection_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build prompt for financial projection generation"""
        business_idea = context.get('business_idea', '')
        revenue_model = context.get('revenue_model', '')

        if language == 'ar':
            return f"""
            أنشئ توقعات مالية لمدة 3 سنوات لفكرة العمل التالية:
            فكرة العمل: {business_idea}
            نموذج الإيرادات: {revenue_model}

            يجب أن تشمل: الإيرادات، المصروفات، الربح، والتدفق النقدي.
            """
        else:
            return f"""
            Create 3-year financial projections for the following business idea:
            Business Idea: {business_idea}
            Revenue Model: {revenue_model}

            Include: revenue, expenses, profit, and cash flow projections.
            """

    def _build_risk_assessment_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build prompt for risk assessment generation"""
        business_idea = context.get('business_idea', '')
        industry = context.get('industry', '')

        if language == 'ar':
            return f"""
            قم بتقييم المخاطر لفكرة العمل التالية:
            فكرة العمل: {business_idea}
            الصناعة: {industry}

            حدد المخاطر المحتملة واستراتيجيات التخفيف من حدتها.
            """
        else:
            return f"""
            Conduct a risk assessment for the following business idea:
            Business Idea: {business_idea}
            Industry: {industry}

            Identify potential risks and mitigation strategies.
            """

    # Fallback method removed - no more masking of real AI service issues

    # ========================================
    # PHASE 1 DIFFERENTIATION PROMPT BUILDERS
    # ========================================

    def _build_mentor_matching_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build AI mentor matching prompt"""
        startup_profile = context.get('startup_profile', {})

        prompt = f"""
        You are an expert AI mentor matching system for startup incubators in the MENA region.

        STARTUP PROFILE:
        - Industry: {startup_profile.get('industry', 'Not specified')}
        - Stage: {startup_profile.get('stage', 'Not specified')}
        - Team Size: {startup_profile.get('teamSize', 'Not specified')}
        - Technologies: {', '.join(startup_profile.get('technologies', []))}
        - Challenges: {', '.join(startup_profile.get('challenges', []))}
        - Goals: {', '.join(startup_profile.get('goals', []))}
        - Location: {startup_profile.get('location', 'Not specified')}
        - Language: {startup_profile.get('language', 'en')}
        - Cultural Context: {startup_profile.get('culturalContext', 'Not specified')}

        TASK: Find the top 5 optimal mentors for this startup.

        ANALYSIS CRITERIA:
        1. Industry expertise alignment (25%)
        2. Stage-appropriate experience (20%)
        3. Technology stack familiarity (15%)
        4. Cultural and language compatibility (15%)
        5. Geographic proximity/timezone alignment (10%)
        6. Track record of successful mentorships (15%)

        MENA CONTEXT CONSIDERATIONS:
        - Islamic business principles (if applicable)
        - Regional market knowledge
        - Cultural sensitivity
        - Arabic language capabilities
        - Local regulatory understanding

        OUTPUT FORMAT:
        Provide a JSON response with the following structure:
        {{
            "matches": [
                {{
                    "mentor_id": "unique_identifier",
                    "compatibility_score": 85,
                    "reasoning": "Detailed explanation of why this mentor is a good match",
                    "strengths": ["specific strengths relevant to this startup"],
                    "potential_challenges": ["areas that might need attention"],
                    "recommended_focus_areas": ["what this mentor should focus on"],
                    "engagement_frequency": "weekly/biweekly/monthly",
                    "estimated_duration": "6 months"
                }}
            ],
            "overall_analysis": "Summary of the matching process and recommendations"
        }}

        Language: {language}
        """

        return prompt

    def _build_mentorship_success_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build mentorship success prediction prompt"""
        mentor_profile = context.get('mentor_profile', {})
        startup_profile = context.get('startup_profile', {})
        historical_data = context.get('historical_data', {})

        prompt = f"""
        You are an AI system that predicts mentorship success probability based on mentor-startup compatibility.

        MENTOR PROFILE:
        - Experience: {mentor_profile.get('experience', 'Not specified')} years
        - Industries: {', '.join(mentor_profile.get('industries', []))}
        - Successful Mentorships: {mentor_profile.get('successfulMentorships', 'Not specified')}
        - Mentorship Style: {mentor_profile.get('mentorshipStyle', 'Not specified')}
        - Languages: {', '.join(mentor_profile.get('languages', []))}
        - Cultural Background: {', '.join(mentor_profile.get('culturalBackground', []))}

        STARTUP PROFILE:
        - Industry: {startup_profile.get('industry', 'Not specified')}
        - Stage: {startup_profile.get('stage', 'Not specified')}
        - Challenges: {', '.join(startup_profile.get('challenges', []))}
        - Goals: {', '.join(startup_profile.get('goals', []))}
        - Cultural Context: {startup_profile.get('culturalContext', 'Not specified')}

        HISTORICAL SUCCESS DATA:
        - Average Success Rate: {historical_data.get('average_success_rate', 0.73) * 100}%
        - Industry Success Rates: {historical_data.get('industry_success_rates', {})}
        - Key Success Factors: {', '.join(historical_data.get('key_success_factors', []))}

        TASK: Predict the probability of successful mentorship and provide detailed analysis.

        OUTPUT FORMAT:
        Provide a JSON response:
        {{
            "probability": 78,
            "factors": {{
                "industry_alignment": 85,
                "experience_relevance": 75,
                "cultural_compatibility": 80,
                "goal_alignment": 70,
                "time_commitment": 85,
                "style_compatibility": 75
            }},
            "recommendations": [
                "Specific recommendations to improve success probability"
            ],
            "risk_factors": [
                "Potential challenges that could impact success"
            ],
            "success_indicators": [
                "Key metrics to track for success"
            ],
            "reasoning": "Detailed explanation of the prediction"
        }}

        Language: {language}
        """

        return prompt

    def _build_schedule_optimization_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build schedule optimization prompt"""
        mentor_availability = context.get('mentor_availability', {})
        startup_preferences = context.get('startup_preferences', {})
        cultural_factors = context.get('cultural_factors', [])
        cultural_calendar = context.get('cultural_calendar', {})

        prompt = f"""
        You are an AI scheduling optimization system for mentorship programs in the MENA region.

        MENTOR AVAILABILITY:
        - Hours per week: {mentor_availability.get('hoursPerWeek', 'Not specified')}
        - Timezone: {mentor_availability.get('timezone', 'Not specified')}
        - Preferred times: {', '.join(mentor_availability.get('preferredTimes', []))}

        STARTUP PREFERENCES:
        - Timezone: {startup_preferences.get('timezone', 'Not specified')}
        - Preferred frequency: {startup_preferences.get('frequency', 'Not specified')}
        - Available days: {', '.join(startup_preferences.get('availableDays', []))}
        - Preferred duration: {startup_preferences.get('duration', 'Not specified')} minutes

        CULTURAL FACTORS: {', '.join(cultural_factors)}

        CULTURAL CALENDAR:
        - Ramadan period: {cultural_calendar.get('ramadan_period', 'Not specified')}
        - Eid holidays: {', '.join(cultural_calendar.get('eid_holidays', []))}
        - Weekend days: {cultural_calendar.get('weekend_days', {})}

        TASK: Create an optimal mentorship schedule that maximizes engagement while respecting cultural and practical constraints.

        OUTPUT FORMAT:
        Provide a JSON response:
        {{
            "schedule": {{
                "frequency": "weekly",
                "duration": 60,
                "preferred_day": "Tuesday",
                "preferred_time": "14:00 UTC",
                "timezone_note": "Explanation of timezone considerations"
            }},
            "cultural_adaptations": [
                "Specific adaptations for cultural factors"
            ],
            "alternative_options": [
                "Backup scheduling options"
            ],
            "special_considerations": [
                "Important notes about the schedule"
            ],
            "reasoning": "Detailed explanation of the optimization"
        }}

        Language: {language}
        """

        return prompt

    def _build_mentorship_plan_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build mentorship plan generation prompt"""
        mentor_profile = context.get('mentor_profile', {})
        startup_profile = context.get('startup_profile', {})
        duration_months = context.get('duration_months', 6)
        industry_practices = context.get('industry_best_practices', {})

        prompt = f"""
        You are an AI mentorship plan generator that creates comprehensive mentorship roadmaps.

        MENTOR EXPERTISE:
        - Industries: {', '.join(mentor_profile.get('industries', []))}
        - Experience: {mentor_profile.get('experience', 'Not specified')} years
        - Specializations: {', '.join(mentor_profile.get('specializations', []))}
        - Mentorship Style: {mentor_profile.get('mentorshipStyle', 'Not specified')}

        STARTUP DETAILS:
        - Industry: {startup_profile.get('industry', 'Not specified')}
        - Stage: {startup_profile.get('stage', 'Not specified')}
        - Goals: {', '.join(startup_profile.get('goals', []))}
        - Challenges: {', '.join(startup_profile.get('challenges', []))}

        PROGRAM DURATION: {duration_months} months

        INDUSTRY BEST PRACTICES:
        - Focus Areas: {', '.join(industry_practices.get('focus_areas', []))}
        - Typical Duration: {industry_practices.get('typical_duration', 6)} months
        - Key Milestones: {', '.join(industry_practices.get('key_milestones', []))}

        TASK: Create a comprehensive mentorship plan with clear milestones, goals, and success metrics.

        OUTPUT FORMAT:
        Provide a JSON response:
        {{
            "milestones": [
                {{
                    "month": 1,
                    "title": "Foundation Setting",
                    "objectives": ["Specific objectives for this month"],
                    "deliverables": ["Expected deliverables"],
                    "success_metrics": ["How to measure success"]
                }}
            ],
            "goals": {{
                "short_term": ["Goals for first 2 months"],
                "medium_term": ["Goals for months 3-4"],
                "long_term": ["Goals for final months"]
            }},
            "timeline": {{
                "phase_1": "Foundation (Months 1-2)",
                "phase_2": "Development (Months 3-4)",
                "phase_3": "Growth (Months 5-6)"
            }},
            "success_metrics": [
                "Overall success indicators for the program"
            ],
            "recommendations": [
                "Additional recommendations for success"
            ]
        }}

        Language: {language}
        """

        return prompt

    def _build_business_plan_executive_summary_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build business plan executive summary prompt"""
        business_input = context.get('business_input', {})
        mena_context = context.get('mena_context', {})

        prompt = f"""
        You are an expert business plan writer specializing in MENA market startups.

        BUSINESS IDEA: {business_input.get('business_idea', 'Not specified')}
        INDUSTRY: {business_input.get('industry', 'Not specified')}
        TARGET MARKET: {business_input.get('target_market', 'Not specified')}
        REGION: {business_input.get('region', 'Not specified')}
        FUNDING GOAL: ${business_input.get('funding_goal', 'Not specified')}

        REGIONAL CONTEXT:
        - Market Size: ${mena_context.get('market_size', {}).get(business_input.get('industry', 'other'), 'Not available')}
        - Population: {mena_context.get('population', 'Not specified')}
        - GDP per Capita: ${mena_context.get('gdp_per_capita', 'Not specified')}
        - Business Culture: {mena_context.get('business_culture', 'Not specified')}

        TASK: Write a compelling executive summary that captures the essence of this business opportunity.

        EXECUTIVE SUMMARY COMPONENTS:
        1. Business concept and value proposition
        2. Market opportunity and size
        3. Competitive advantage
        4. Business model overview
        5. Financial highlights and funding needs
        6. Team and execution capability
        7. Growth potential and exit strategy

        MENA-SPECIFIC CONSIDERATIONS:
        - Cultural sensitivity and local market adaptation
        - Regulatory environment and compliance
        - Regional business practices
        - Islamic finance compatibility (if applicable)
        - Arabic language market penetration

        OUTPUT: Write a comprehensive executive summary (500-800 words) that would appeal to MENA investors and stakeholders.

        Language: {language}
        """

        return prompt

    def _build_business_plan_market_analysis_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build business plan market analysis prompt"""
        business_input = context.get('business_input', {})
        mena_context = context.get('mena_context', {})
        industry_data = context.get('industry_insights', {})

        prompt = f"""
        You are a market research expert specializing in MENA markets and startup ecosystems.

        BUSINESS DETAILS:
        - Business Idea: {business_input.get('business_idea', 'Not specified')}
        - Industry: {business_input.get('industry', 'Not specified')}
        - Target Market: {business_input.get('target_market', 'Not specified')}
        - Region: {business_input.get('region', 'Not specified')}

        REGIONAL MARKET DATA:
        - Total Market Size: ${mena_context.get('market_size', {}).get(business_input.get('industry', 'other'), 'Not available')}
        - Population: {mena_context.get('population', 'Not specified')}
        - Internet Penetration: {mena_context.get('internet_penetration', 'Not specified') * 100}%
        - Mobile Penetration: {mena_context.get('mobile_penetration', 'Not specified') * 100}%
        - Key Cities: {', '.join(mena_context.get('key_cities', []))}

        INDUSTRY INSIGHTS:
        - Growth Rate: {industry_data.get('growth_rate', 'Not specified') * 100}%
        - Key Trends: {', '.join(industry_data.get('key_trends', []))}
        - Investment Activity: {industry_data.get('investment_activity', 'Not specified')}

        TASK: Conduct comprehensive market analysis with MENA focus.

        ANALYSIS COMPONENTS:
        1. Total Addressable Market (TAM)
        2. Serviceable Addressable Market (SAM)
        3. Serviceable Obtainable Market (SOM)
        4. Market trends and growth drivers
        5. Competitive landscape analysis
        6. Customer segmentation and personas
        7. Market entry barriers and opportunities
        8. Regulatory environment

        MENA-SPECIFIC ANALYSIS:
        - Cultural factors affecting market adoption
        - Regional variations and preferences
        - Local competition and international players
        - Government initiatives and support
        - Economic factors and purchasing power

        OUTPUT: Provide detailed market analysis with specific data, insights, and recommendations for the MENA market.

        Language: {language}
        """

        return prompt

    def _build_business_plan_business_model_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build business plan business model prompt"""
        business_input = context.get('business_input', {})
        cultural_considerations = context.get('cultural_considerations', [])

        prompt = f"""
        You are a business model expert specializing in MENA market startups and cultural adaptations.

        BUSINESS DETAILS:
        - Business Idea: {business_input.get('business_idea', 'Not specified')}
        - Industry: {business_input.get('industry', 'Not specified')}
        - Target Market: {business_input.get('target_market', 'Not specified')}
        - Region: {business_input.get('region', 'Not specified')}
        - Funding Goal: ${business_input.get('funding_goal', 'Not specified')}

        CULTURAL CONSIDERATIONS: {', '.join(cultural_considerations)}

        TASK: Design an optimal business model adapted for the MENA market.

        BUSINESS MODEL COMPONENTS:
        1. Value Proposition Canvas
        2. Revenue Streams and Pricing Strategy
        3. Cost Structure and Unit Economics
        4. Key Partnerships and Alliances
        5. Key Activities and Resources
        6. Customer Relationships and Channels
        7. Scalability and Growth Strategy

        MENA ADAPTATIONS:
        - Islamic finance compliance (if applicable)
        - Cultural sensitivity in value proposition
        - Local payment methods and preferences
        - Regional partnership opportunities
        - Government and regulatory considerations

        OUTPUT: Provide a comprehensive business model with clear revenue streams, cost structure, and cultural adaptations.

        Language: {language}
        """

        return prompt

    def _build_business_plan_financial_projections_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build business plan financial projections prompt"""
        business_input = context.get('business_input', {})
        timeframe = context.get('timeframe', 36)
        funding_goal = context.get('funding_goal', 100000)

        prompt = f"""
        You are a financial modeling expert specializing in MENA startup financial projections.

        BUSINESS DETAILS:
        - Business Idea: {business_input.get('business_idea', 'Not specified')}
        - Industry: {business_input.get('industry', 'Not specified')}
        - Region: {business_input.get('region', 'Not specified')}
        - Funding Goal: ${funding_goal}
        - Projection Timeframe: {timeframe} months
        - Team Size: {business_input.get('team_size', 'Not specified')}

        TASK: Create detailed financial projections with MENA market considerations.

        FINANCIAL PROJECTIONS COMPONENTS:
        1. Revenue Projections (monthly for 36 months)
        2. Expense Breakdown (operational, marketing, personnel)
        3. Cash Flow Analysis
        4. Break-even Analysis
        5. Funding Requirements Timeline
        6. ROI Projections for Investors
        7. Sensitivity Analysis

        MENA-SPECIFIC FINANCIAL FACTORS:
        - Local salary ranges and employment costs
        - Regional marketing and customer acquisition costs
        - Currency considerations and exchange rates
        - Tax implications and regulatory costs
        - Cultural event impacts (Ramadan, Eid) on business cycles

        OUTPUT: Provide detailed month-by-month financial projections with clear assumptions and MENA market adjustments.

        Language: {language}
        """

        return prompt

    def _build_business_plan_marketing_strategy_prompt(self, context: Dict[str, Any], language: str) -> str:
        """Build business plan marketing strategy prompt"""
        business_input = context.get('business_input', {})
        target_demographics = context.get('target_demographics', {})
        digital_channels = context.get('digital_channels', [])

        prompt = f"""
        You are a digital marketing expert specializing in Arabic markets and MENA region customer acquisition.

        BUSINESS DETAILS:
        - Business Idea: {business_input.get('business_idea', 'Not specified')}
        - Industry: {business_input.get('industry', 'Not specified')}
        - Target Market: {business_input.get('target_market', 'Not specified')}
        - Region: {business_input.get('region', 'Not specified')}
        - Language: {business_input.get('language', 'en')}

        TARGET DEMOGRAPHICS:
        - Age Range: {target_demographics.get('age_range', 'Not specified')}
        - Income Level: {target_demographics.get('income_level', 'Not specified')}
        - Digital Behavior: {target_demographics.get('digital_behavior', 'Not specified')}

        AVAILABLE DIGITAL CHANNELS: {', '.join(digital_channels)}

        TASK: Create a comprehensive marketing strategy adapted for MENA digital-native audiences.

        MARKETING STRATEGY COMPONENTS:
        1. Target Audience Segmentation
        2. Brand Positioning and Messaging
        3. Channel Strategy and Mix
        4. Content Marketing Plan
        5. Social Media Strategy
        6. Influencer Marketing Approach
        7. Budget Allocation and ROI Targets
        8. Performance Metrics and KPIs

        MENA-SPECIFIC MARKETING CONSIDERATIONS:
        - Arabic language content strategy
        - Cultural sensitivity and local customs
        - Ramadan and Eid marketing opportunities
        - Gender-specific considerations and targeting
        - Local social media platform preferences
        - Influencer landscape and partnerships
        - Mobile-first approach and app marketing

        OUTPUT: Provide a detailed marketing strategy with specific tactics, budget allocation, and cultural adaptations for the MENA market.

        Language: {language}
        """

        return prompt

    @staticmethod
    def prepare_syrian_chat_prompt(message: str, language: str = 'ar',
                                 context: Optional[Dict[str, Any]] = None,
                                 chat_type: str = 'general') -> str:
        """
        ✅ PREPARE SYRIAN AI CHAT PROMPT WITH DIFFERENT TYPES
        Creates context-aware prompts for Syrian AI assistant
        """

        # ✅ SYRIAN CONTEXT PROMPTS BASED ON CHAT TYPE
        syrian_contexts = {
            'syrian_business': """أنت مساعد ذكي سوري متخصص في ريادة الأعمال والاستثمار في سوريا.
لديك معرفة عميقة بالسوق السوري، المدن الرئيسية مثل دمشق وحلب وحمص واللاذقية وطرطوس ودرعا والسويداء والحسكة ودير الزور والرقة وإدلب،
والفرص الاستثمارية المحلية، والتحديات الاقتصادية، والقوانين التجارية السورية.
تجيب باللغة العربية وتقدم نصائح عملية مناسبة للبيئة السورية والثقافة المحلية.
تركز على الحلول العملية والواقعية التي تناسب الظروف الحالية في سوريا.""",

            'damascus_local': """أنت مساعد ذكي من دمشق، عاصمة سوريا العريقة.
لديك معرفة واسعة بأحياء دمشق (البرامكة، المزة، القصاع، الشعلان، باب توما، القيمرية، الصالحية، المالكي، أبو رمانة، كفر سوسة، جرمانا، دوما، حرستا)،
الأسواق التجارية (سوق الحميدية، سوق مدحت باشا، سوق البزورية)، الجامعات (جامعة دمشق، الجامعة السورية الخاصة)،
والفرص الاستثمارية في العاصمة. تتحدث بلهجة دمشقية مهذبة وتقدم معلومات محلية دقيقة عن دمشق وريفها.""",

            'aleppo_local': """أنت مساعد ذكي من حلب، العاصمة الاقتصادية لسوريا.
لديك معرفة عميقة بأحياء حلب (العزيزية، الفرقان، الحمدانية، الشهباء، السليمانية، الجميلية، المحافظة، الجديدة، صلاح الدين)،
الصناعات التقليدية (النسيج، الصابون، الحلويات)، التجارة، والتراث الحلبي العريق.
تتحدث بلهجة حلبية أصيلة وتقدم نصائح تجارية واستثمارية مناسبة لحلب وريفها،
مع التركيز على الصناعات التقليدية والحرف اليدوية التي تشتهر بها حلب.""",

            'general': """أنت مساعد ذكي سوري عام متخصص في خدمة المجتمع السوري.
لديك معرفة شاملة بجميع المحافظات السورية (دمشق، حلب، حمص، حماة، اللاذقية، طرطوس، درعا، السويداء، القنيطرة، الحسكة، دير الزور، الرقة، إدلب)،
الثقافة السورية، التاريخ، التراث، والتحديات والفرص في البلد.
تجيب باللغة العربية وتقدم معلومات مفيدة ومناسبة للسياق السوري،
مع احترام التنوع الثقافي والديني في سوريا."""
        }

        # ✅ GET APPROPRIATE SYRIAN CONTEXT
        syrian_context = syrian_contexts.get(chat_type, syrian_contexts['general'])

        # ✅ BUILD COMPLETE PROMPT
        prompt = f"""{syrian_context}

رسالة المستخدم: {message}

يرجى الإجابة بطريقة مفيدة ومناسبة للسياق السوري، مع مراعاة الثقافة المحلية والظروف الحالية."""

        # ✅ ADD ADDITIONAL CONTEXT IF PROVIDED
        if context:
            if context.get('user_location'):
                prompt += f"\n\nموقع المستخدم: {context['user_location']}"
            if context.get('business_sector'):
                prompt += f"\nالقطاع التجاري: {context['business_sector']}"
            if context.get('additional_info'):
                prompt += f"\nمعلومات إضافية: {context['additional_info']}"

        return prompt


# Global AI service instance - used throughout the entire application
_ai_service = None

def get_ai_service() -> AIService:
    """
    Get the global AI service instance
    This is the ONLY AI service used across the entire application
    """
    global _ai_service
    if _ai_service is None:
        _ai_service = AIService()
    return _ai_service


# Universal AI functions - used everywhere in the application
def ai_chat(message: str, language: str = 'en', user_id: Optional[int] = None,
            context: Optional[Dict[str, Any]] = None, chat_type: str = 'general',
            ai_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Universal chat function with Syrian context and AI settings - used across all apps"""
    return get_ai_service().chat(message, language, user_id, context, chat_type, ai_settings)

def ai_analyze_business(business_idea: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Universal business analysis function - used across all apps"""
    return get_ai_service().analyze_business(business_idea, language, user_id)

def ai_analyze_text(text: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Universal text analysis function - used across all apps"""
    return get_ai_service().analyze_text(text, language, user_id)

def ai_generate_intelligent_content(content_type: str, context: Dict[str, Any],
                                  language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Universal intelligent content generation - used across all apps"""
    return get_ai_service().generate_intelligent_content(content_type, context, language, user_id)

def ai_get_status() -> Dict[str, Any]:
    """Universal status function - used across all apps"""
    return get_ai_service().get_status()

def ai_is_available() -> bool:
    """Universal availability check - used across all apps"""
    return get_ai_service().is_available()

def ai_reload_config():
    """Universal config reload - used across all apps"""
    return get_ai_service().reload_config()


# Language detection utility
def detect_language(text: str) -> str:
    """
    Simple language detection
    Used across the application for automatic language detection
    """
    if not text or len(text.strip()) == 0:
        return 'en'
    
    # Count Arabic characters
    arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
    total_chars = len([char for char in text if char.isalnum()])
    
    if total_chars == 0:
        return 'en'
    
    # If more than 20% Arabic characters, consider it Arabic
    arabic_percentage = arabic_chars / total_chars
    return 'ar' if arabic_percentage > 0.2 else 'en'


# Auto-detect language and process
def ai_chat_auto(message: str, user_id: Optional[int] = None, chat_type: str = 'general',
                 user_context: Optional[Dict[str, Any]] = None, ai_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Chat with automatic language detection, Syrian context, user personalization, and AI settings"""
    language = detect_language(message)
    return ai_chat(message, language, user_id, user_context, chat_type, ai_settings)

def ai_analyze_business_auto(business_idea: str, user_id: Optional[int] = None) -> Dict[str, Any]:
    """Business analysis with automatic language detection"""
    language = detect_language(business_idea)
    return ai_analyze_business(business_idea, language, user_id)

def ai_analyze_text_auto(text: str, user_id: Optional[int] = None) -> Dict[str, Any]:
    """Text analysis with automatic language detection"""
    language = detect_language(text)
    return ai_analyze_text(text, language, user_id)


# Business Plan specific functions
def generate_business_plan_template(business_idea: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Generate a business plan template for a given business idea"""
    prompt = f"""Generate a comprehensive business plan template for the following business idea: {business_idea}

    Please provide a structured business plan with the following sections:
    1. Executive Summary
    2. Company Description
    3. Market Analysis
    4. Organization & Management
    5. Service or Product Line
    6. Marketing & Sales
    7. Funding Request
    8. Financial Projections
    9. Appendix

    For each section, provide detailed guidance and examples specific to this business idea."""

    return ai_chat(prompt, language, user_id)


def generate_section_content(section_name: str, business_idea: str, context: Dict[str, Any] = None,
                           language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Generate content for a specific business plan section"""
    try:
        # Simplified context handling to avoid JSON serialization issues
        context_info = ""
        if context and isinstance(context, dict):
            # Extract key information safely
            if 'section_info' in context:
                section_info = context['section_info']
                if isinstance(section_info, dict) and 'guiding_questions' in section_info:
                    questions = section_info['guiding_questions']
                    if isinstance(questions, list) and questions:
                        context_info = f"\nGuiding questions to address:\n" + "\n".join([f"- {q}" for q in questions[:5]])

        # Create a simple, reliable prompt
        if language == 'ar':
            prompt = f"""اكتب محتوى مفصل لقسم "{section_name}" في خطة عمل لـ: {business_idea}

{context_info}

يرجى تقديم محتوى شامل ومهني مناسب للمستثمرين وأصحاب المصلحة."""
        else:
            prompt = f"""Generate detailed content for the "{section_name}" section of a business plan for: {business_idea}

{context_info}

Please provide comprehensive, professional content that would be suitable for investors and stakeholders."""

        logger.info(f"Generating section content for: {section_name}")
        logger.info(f"Prompt length: {len(prompt)}")

        return ai_chat(prompt, language, user_id)

    except Exception as e:
        logger.error(f"Error in generate_section_content: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'success': False,
            'error': f"Section content generation failed: {str(e)}"
        }


def analyze_business_plan(business_plan_content: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Analyze a business plan and provide feedback and suggestions"""
    prompt = f"""Analyze the following business plan and provide detailed feedback:

    {business_plan_content}

    Please provide:
    1. Strengths of the plan
    2. Areas for improvement
    3. Missing elements
    4. Market viability assessment
    5. Financial projections review
    6. Overall recommendations

    Be constructive and specific in your feedback."""

    return ai_chat(prompt, language, user_id)


def generate_complete_business_plan(business_idea: str, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Generate a complete business plan for a given business idea"""
    prompt = f"""Generate a complete, comprehensive business plan for: {business_idea}

    Please provide a full business plan with all sections filled out with detailed, realistic content:

    1. Executive Summary (2-3 paragraphs)
    2. Company Description (detailed background and mission)
    3. Market Analysis (target market, competition, market size)
    4. Organization & Management (team structure, key personnel)
    5. Service or Product Line (detailed description, features, benefits)
    6. Marketing & Sales Strategy (pricing, promotion, distribution)
    7. Funding Request (if applicable, amount and use of funds)
    8. Financial Projections (revenue, expenses, profit projections)
    9. Risk Analysis (potential risks and mitigation strategies)
    10. Implementation Timeline (key milestones and deadlines)

    Make it professional, detailed, and investor-ready."""

    return ai_chat(prompt, language, user_id)


def generate_market_analysis(business_idea: str, target_market: str = None, language: str = 'en', user_id: Optional[int] = None) -> Dict[str, Any]:
    """Generate detailed market analysis for a business idea"""
    market_context = f" focusing on {target_market}" if target_market else ""

    prompt = f"""Generate a comprehensive market analysis for: {business_idea}{market_context}

    Please provide detailed analysis including:

    1. Market Size and Growth Potential
       - Total Addressable Market (TAM)
       - Serviceable Addressable Market (SAM)
       - Market growth trends and projections

    2. Target Market Segmentation
       - Primary target demographics
       - Customer personas and behavior
       - Market needs and pain points

    3. Competitive Analysis
       - Direct and indirect competitors
       - Competitive advantages and disadvantages
       - Market positioning opportunities

    4. Market Trends and Opportunities
       - Industry trends affecting the market
       - Emerging opportunities
       - Potential threats and challenges

    5. Market Entry Strategy
       - Recommended approach to enter the market
       - Barriers to entry
       - Success factors

    Provide specific data, statistics, and actionable insights where possible."""

    return ai_chat(prompt, language, user_id)
