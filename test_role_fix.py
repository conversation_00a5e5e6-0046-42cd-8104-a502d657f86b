#!/usr/bin/env python3
"""
Test the role mapping fix
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_role_fix():
    """Test that roles are properly stored"""
    print("[TEST] Testing role mapping fix...")
    
    # Test entrepreneur registration
    test_data = {
        "username": "test_entrepreneur_fix",
        "email": "<EMAIL>",
        "password": "testpass123",
        "password_confirm": "testpass123",
        "first_name": "<PERSON>",
        "last_name": "EntrepreneurFix",
        "selected_role": "entrepreneur",
        "business_name": "FixTest Startup",
        "business_stage": "idea",
        "funding_needed": "under-50k",
        "business_description": "Testing the role fix",
        "bio": "Testing entrepreneur role fix",
        "location": "Test City"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/auth/register-enhanced/",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print(f"[SUCCESS] Registration successful")
            print(f"   User ID: {result.get('user', {}).get('id')}")
            print(f"   Status: {result.get('status')}")
            return result.get('user', {}).get('id')
        else:
            print(f"[ERROR] Registration failed")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"[ERROR] Error: {e}")
        return None

if __name__ == "__main__":
    user_id = test_role_fix()
    if user_id:
        print(f"\n[SUCCESS] Test user created with ID: {user_id}")
        print("Now run check_database.py to verify the role is properly stored")
    else:
        print("\n[ERROR] Test failed")
