import React from 'react';
import { Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON>w, Wifi, WifiOff } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  text?: string;
}

interface LoadingSkeletonProps {
  lines?: number;
  className?: string;
  avatar?: boolean;
}

interface EmptyStateProps {
  icon?: React.ReactNode;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

interface ErrorStateProps {
  error: Error | string;
  onRetry?: () => void;
  className?: string;
  showDetails?: boolean;
}

/**
 * Loading Spinner Component
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className = '',
  text
}) => {
  const { t } = useTranslation();

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className="flex flex-col items-center space-y-2">
        <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-600`} />
        {text && (
          <p className="text-sm text-gray-600">{text}</p>
        )}
      </div>
    </div>
  );
};

/**
 * Loading Skeleton Component
 */
export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  lines = 3,
  className = '',
  avatar = false
}) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {avatar && (
        <div className="flex items-center space-x-4 mb-4">
          <div className="rounded-full bg-gray-300 h-10 w-10"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-3 bg-gray-300 rounded w-1/2"></div>
          </div>
        </div>
      )}
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, index) => (
          <div key={index} className="space-y-2">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

/**
 * Empty State Component
 */
export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action,
  className = ''
}) => {
  const { t } = useTranslation();

  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="flex justify-center mb-4">
        {icon || (
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
            <AlertCircle className="w-8 h-8 text-gray-400" />
          </div>
        )}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      {description && (
        <p className="text-gray-500 mb-6 max-w-sm mx-auto">{description}</p>
      )}
      {action && (
        <button
          onClick={action.onClick}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {action.label}
        </button>
      )}
    </div>
  );
};

/**
 * Error State Component
 */
export const ErrorState: React.FC<ErrorStateProps> = ({
  error,
  onRetry,
  className = '',
  showDetails = false
}) => {
  const { t } = useTranslation();
  const [showFullError, setShowFullError] = React.useState(false);

  const errorMessage = typeof error === 'string' ? error : error.message;
  const isNetworkError = errorMessage.toLowerCase().includes('network') || 
                        errorMessage.toLowerCase().includes('fetch');

  return (
    <div className={`text-center py-8 ${className}`}>
      <div className="flex justify-center mb-4">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
          {isNetworkError ? (
            <WifiOff className="w-8 h-8 text-red-600" />
          ) : (
            <AlertCircle className="w-8 h-8 text-red-600" />
          )}
        </div>
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {isNetworkError 
          ? t('errors.network.title', 'Connection Error')
          : t('errors.general.title', 'Something went wrong')
        }
      </h3>
      
      <p className="text-gray-500 mb-6 max-w-sm mx-auto">
        {isNetworkError
          ? t('errors.network.description', 'Please check your internet connection and try again.')
          : t('errors.general.description', 'An unexpected error occurred. Please try again.')
        }
      </p>

      <div className="flex flex-col items-center space-y-3">
        {onRetry && (
          <button
            onClick={onRetry}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            {t('common.retry', 'Try Again')}
          </button>
        )}

        {showDetails && (
          <div className="w-full max-w-md">
            <button
              onClick={() => setShowFullError(!showFullError)}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {showFullError ? 'Hide Details' : 'Show Details'}
            </button>
            
            {showFullError && (
              <div className="mt-3 p-3 bg-gray-100 rounded text-xs text-left font-mono text-gray-800 overflow-auto max-h-32">
                {typeof error === 'string' ? error : error.stack || error.message}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Network Status Component
 */
export const NetworkStatus: React.FC = () => {
  const { t } = useTranslation();
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (isOnline) return null;

  return (
    <div className="fixed top-0 left-0 right-0 bg-red-600 text-white px-4 py-2 text-center text-sm z-50">
      <div className="flex items-center justify-center space-x-2">
        <WifiOff className="w-4 h-4" />
        <span>{t('network.offline', 'You are currently offline')}</span>
      </div>
    </div>
  );
};

/**
 * Page Loading Component
 */
export const PageLoading: React.FC<{ message?: string }> = ({ message }) => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <LoadingSpinner size="xl" />
        <p className="mt-4 text-lg text-gray-600">
          {message || t('common.loading', 'Loading...')}
        </p>
      </div>
    </div>
  );
};

/**
 * Card Loading Component
 */
export const CardLoading: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
      <LoadingSkeleton lines={4} avatar />
    </div>
  );
};

/**
 * Table Loading Component
 */
export const TableLoading: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => {
  return (
    <div className="animate-pulse">
      <div className="bg-gray-200 h-10 rounded mb-4"></div>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4 mb-3">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="flex-1 h-8 bg-gray-200 rounded"></div>
          ))}
        </div>
      ))}
    </div>
  );
};

/**
 * Button Loading State
 */
export const ButtonLoading: React.FC<{ 
  children: React.ReactNode;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
}> = ({ children, loading = false, disabled = false, className = '', onClick }) => {
  return (
    <button
      onClick={onClick}
      disabled={loading || disabled}
      className={`inline-flex items-center justify-center ${className} ${
        loading || disabled ? 'opacity-50 cursor-not-allowed' : ''
      }`}
    >
      {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
      {children}
    </button>
  );
};
