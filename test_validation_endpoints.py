#!/usr/bin/env python3
"""
Test script for real-time validation endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_username_validation():
    """Test username validation endpoint"""
    print("[TEST] Testing Username Validation...")
    
    test_cases = [
        {"username": "test_entrepreneur", "expected": False},  # Should exist
        {"username": "newuser123", "expected": True},         # Should be available
        {"username": "ab", "expected": False},                # Too short
        {"username": "", "expected": False},                  # Empty
    ]
    
    for case in test_cases:
        try:
            response = requests.post(
                f"{BASE_URL}/api/auth/check-username/",
                json={"username": case["username"]},
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"Username: '{case['username']}'")
            print(f"  Status: {response.status_code}")
            print(f"  Response: {response.json()}")
            print(f"  Expected available: {case['expected']}")
            print()
            
        except Exception as e:
            print(f"Error testing username '{case['username']}': {e}")

def test_email_validation():
    """Test email validation endpoint"""
    print("[TEST] Testing Email Validation...")
    
    test_cases = [
        {"email": "<EMAIL>", "expected": False},  # Should exist
        {"email": "<EMAIL>", "expected": True},     # Should be available
        {"email": "invalid-email", "expected": False},         # Invalid format
        {"email": "", "expected": False},                      # Empty
    ]
    
    for case in test_cases:
        try:
            response = requests.post(
                f"{BASE_URL}/api/auth/check-email/",
                json={"email": case["email"]},
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"Email: '{case['email']}'")
            print(f"  Status: {response.status_code}")
            print(f"  Response: {response.json()}")
            print(f"  Expected available: {case['expected']}")
            print()
            
        except Exception as e:
            print(f"Error testing email '{case['email']}': {e}")

def main():
    """Run validation tests"""
    print("[START] Testing Real-time Validation Endpoints")
    print("=" * 50)
    
    test_username_validation()
    test_email_validation()
    
    print("=" * 50)
    print("[COMPLETE] Validation endpoint testing complete!")

if __name__ == "__main__":
    main()
