# Generated by Django 5.2.1 on 2025-07-22 03:00

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0008_alter_userrole_name_alter_userrole_permission_level"),
    ]

    operations = [
        migrations.AddField(
            model_name="roleapplication",
            name="company_name",
            field=models.CharField(
                blank=True, help_text="Company or project name", max_length=200
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="funding_needed",
            field=models.CharField(
                blank=True,
                choices=[
                    ("0-10k", "Less than $10,000"),
                    ("10k-50k", "$10,000 - $50,000"),
                    ("50k-100k", "$50,000 - $100,000"),
                    ("100k-500k", "$100,000 - $500,000"),
                    ("500k-1m", "$500,000 - $1,000,000"),
                    ("1m+", "More than $1,000,000"),
                    ("no-funding", "No funding needed currently"),
                ],
                help_text="Amount of funding needed",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="industry",
            field=models.CharField(
                blank=True, help_text="Industry or sector", max_length=100
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="investment_criteria",
            field=models.TextField(
                blank=True, help_text="Investment criteria and preferences"
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="linkedin_profile",
            field=models.URLField(blank=True, help_text="LinkedIn profile URL"),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="maximum_investment",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Maximum investment amount",
                max_digits=12,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="minimum_investment",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Minimum investment amount",
                max_digits=12,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="moderation_experience",
            field=models.TextField(
                blank=True, help_text="Previous moderation experience"
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="previous_experience",
            field=models.TextField(
                blank=True, help_text="Previous entrepreneurship experience"
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="project_description",
            field=models.TextField(
                blank=True, help_text="Detailed description of the project/idea"
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="project_stage",
            field=models.CharField(
                blank=True,
                choices=[
                    ("idea", "Just an Idea"),
                    ("planning", "Planning Stage"),
                    ("prototype", "Prototype"),
                    ("mvp", "MVP (Minimum Viable Product)"),
                    ("launched", "Launched"),
                    ("scaling", "Scaling"),
                ],
                help_text="Current stage of the project",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="support_needed",
            field=models.JSONField(
                blank=True,
                default=list,
                help_text="Types of support needed (JSON array)",
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="team_size",
            field=models.CharField(
                blank=True,
                choices=[
                    ("solo", "Solo Founder"),
                    ("2-3", "2-3 People"),
                    ("4-10", "4-10 People"),
                    ("11-25", "11-25 People"),
                    ("25+", "More than 25 People"),
                ],
                help_text="Current team size",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="years_of_experience",
            field=models.PositiveIntegerField(
                blank=True, help_text="Years of professional experience", null=True
            ),
        ),
    ]
