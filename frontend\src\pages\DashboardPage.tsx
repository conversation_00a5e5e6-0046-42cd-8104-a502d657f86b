/**
 * Main Dashboard Page
 * Routes users to their role-specific dashboard with comprehensive overview
 */

import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useLanguage } from '../hooks/useLanguage';
import useReduxRoles from '../hooks/useReduxRoles';
import { ArabicCard, ArabicButton, ArabicTypography } from '../components/ui/ArabicOptimizedComponents';
import { 
  BarChart3, 
  Users, 
  TrendingUp, 
  Calendar, 
  MessageSquare, 
  Target,
  Briefcase,
  DollarSign,
  BookOpen,
  Award,
  Bell,
  Settings
} from 'lucide-react';

interface DashboardStats {
  totalStartups: number;
  activeMentorships: number;
  fundingRaised: number;
  successRate: number;
  upcomingEvents: number;
  unreadMessages: number;
}

interface QuickAction {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  icon: React.ReactNode;
  href: string;
  color: string;
}

const DashboardPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { language, isRTL, t } = useLanguage();
  const { getDashboardRoute, primaryRole } = useReduxRoles();
  const navigate = useNavigate();
  const location = useLocation();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    // Use unified role detection and dashboard routing
    if (user) {
      try {
        const dashboardRoute = getDashboardRoute();
        // Only redirect if we're not already on the correct dashboard
        if (dashboardRoute !== '/dashboard' && location.pathname === '/dashboard') {
          navigate(dashboardRoute);
          return;
        }
      } catch (error) {
        console.error('Error getting dashboard route:', error);
        // Continue to load regular dashboard on error
      }
    }

    // Load dashboard stats
    loadDashboardStats();
  }, [isAuthenticated, user, navigate, location.pathname]);

  const loadDashboardStats = async () => {
    try {
      // Mock data - replace with actual API call
      const mockStats: DashboardStats = {
        totalStartups: 1247,
        activeMentorships: 89,
        fundingRaised: ********,
        successRate: 73,
        upcomingEvents: 5,
        unreadMessages: 12
      };
      
      setStats(mockStats);
    } catch (error) {
      console.error('Error loading dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions: QuickAction[] = [
    {
      id: 'business-plan',
      title: 'Create Business Plan',
      titleAr: 'إنشاء خطة عمل',
      description: 'Start building your business plan with AI assistance',
      descriptionAr: 'ابدأ في بناء خطة عملك بمساعدة الذكاء الاصطناعي',
      icon: <BookOpen className="w-6 h-6" />,
      href: '/dashboard/business-plans/create',
      color: 'bg-blue-500'
    },
    {
      id: 'find-mentor',
      title: 'Find a Mentor',
      titleAr: 'العثور على موجه',
      description: 'Connect with experienced mentors in your industry',
      descriptionAr: 'تواصل مع موجهين ذوي خبرة في مجال عملك',
      icon: <Users className="w-6 h-6" />,
      href: '/dashboard/mentorship/find',
      color: 'bg-green-500'
    },
    {
      id: 'funding',
      title: 'Explore Funding',
      titleAr: 'استكشاف التمويل',
      description: 'Discover funding opportunities for your startup',
      descriptionAr: 'اكتشف فرص التمويل لشركتك الناشئة',
      icon: <DollarSign className="w-6 h-6" />,
      href: '/dashboard/funding',
      color: 'bg-purple-500'
    },
    {
      id: 'ai-analysis',
      title: 'AI Business Analysis',
      titleAr: 'تحليل الأعمال بالذكاء الاصطناعي',
      description: 'Get AI-powered insights for your business',
      descriptionAr: 'احصل على رؤى مدعومة بالذكاء الاصطناعي لعملك',
      icon: <BarChart3 className="w-6 h-6" />,
      href: '/ai-chat', // Use AI chat route
      color: 'bg-orange-500'
    },
    {
      id: 'events',
      title: 'Upcoming Events',
      titleAr: 'الأحداث القادمة',
      description: 'Join workshops, webinars, and networking events',
      descriptionAr: 'انضم إلى ورش العمل والندوات وأحداث التواصل',
      icon: <Calendar className="w-6 h-6" />,
      href: '/dashboard/events',
      color: 'bg-red-500'
    },
    {
      id: 'community',
      title: 'Community Forum',
      titleAr: 'منتدى المجتمع',
      description: 'Connect with other entrepreneurs and share ideas',
      descriptionAr: 'تواصل مع رواد أعمال آخرين وشارك الأفكار',
      icon: <MessageSquare className="w-6 h-6" />,
      href: '/forum',
      color: 'bg-teal-500'
    }
  ];

  const formatCurrency = (amount: number): string => {
    if (language === 'ar') {
      return `${(amount / 1000000).toFixed(1)} مليون ريال`;
    }
    return `$${(amount / 1000000).toFixed(1)}M`;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'} data-testid="dashboard">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div>
              <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                {language === 'ar' ? `مرحباً، ${user?.first_name || 'رائد الأعمال'}` : `Welcome, ${user?.first_name || 'Entrepreneur'}`}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary" className="mt-1">
                {language === 'ar' 
                  ? 'لوحة التحكم الخاصة بك - تتبع تقدمك وإدارة رحلة ريادة الأعمال'
                  : 'Your dashboard - track progress and manage your entrepreneurial journey'
                }
              </ArabicTypography>
            </div>
            <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <button className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors">
                <Bell className="w-6 h-6" />
                {stats && stats.unreadMessages > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {stats.unreadMessages}
                  </span>
                )}
              </button>
              <button 
                onClick={() => navigate('/settings')}
                className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <Settings className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
                <Briefcase className="w-6 h-6 text-blue-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.totalStartups.toLocaleString()}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'الشركات الناشئة' : 'Total Startups'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.activeMentorships}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'الإرشاد النشط' : 'Active Mentorships'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-purple-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {formatCurrency(stats.fundingRaised)}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'التمويل المحصل' : 'Funding Raised'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mx-auto mb-4">
                <TrendingUp className="w-6 h-6 text-orange-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.successRate}%
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'معدل النجاح' : 'Success Rate'}
              </ArabicTypography>
            </ArabicCard>
          </div>
        )}

        {/* Quick Actions */}
        <div className="mb-8">
          <ArabicTypography variant="h2" className="text-gray-900 font-bold mb-6">
            {language === 'ar' ? 'الإجراءات السريعة' : 'Quick Actions'}
          </ArabicTypography>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickActions.map((action) => (
              <ArabicCard key={action.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex-shrink-0 w-12 h-12 ${action.color} rounded-lg flex items-center justify-center text-white ${isRTL ? 'ml-4' : 'mr-4'}`}>
                    {action.icon}
                  </div>
                  <div className="flex-1">
                    <ArabicTypography variant="h5" className="text-gray-900 font-semibold mb-2">
                      {language === 'ar' ? action.titleAr : action.title}
                    </ArabicTypography>
                    <ArabicTypography variant="body2" color="secondary" className="mb-4">
                      {language === 'ar' ? action.descriptionAr : action.description}
                    </ArabicTypography>
                    <ArabicButton
                      size="sm"
                      onClick={() => navigate(action.href)}
                      className="w-full"
                    >
                      {language === 'ar' ? 'ابدأ الآن' : 'Get Started'}
                    </ArabicButton>
                  </div>
                </div>
              </ArabicCard>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <ArabicCard title={language === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}>
            <div className="space-y-4">
              {[1, 2, 3].map((item) => (
                <div key={item} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`w-2 h-2 bg-blue-500 rounded-full ${isRTL ? 'ml-3' : 'mr-3'}`}></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900 font-arabic">
                      {language === 'ar' 
                        ? `تم تحديث خطة العمل - ${item} ساعات مضت`
                        : `Business plan updated - ${item} hours ago`
                      }
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </ArabicCard>

          <ArabicCard title={language === 'ar' ? 'الأهداف القادمة' : 'Upcoming Goals'}>
            <div className="space-y-4">
              {[1, 2, 3].map((item) => (
                <div key={item} className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Target className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    <span className="text-sm text-gray-900 font-arabic">
                      {language === 'ar' ? `الهدف ${item}` : `Goal ${item}`}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500 font-arabic">
                    {language === 'ar' ? `${item} أيام` : `${item} days`}
                  </span>
                </div>
              ))}
            </div>
          </ArabicCard>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
