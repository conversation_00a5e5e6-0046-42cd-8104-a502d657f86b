# Generated by Django 5.2.1 on 2025-07-22 11:34

import api.storage
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0011_add_expertise_field"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailVerificationSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "require_verification",
                    models.BooleanField(
                        default=True,
                        help_text="Require email verification for new registrations",
                    ),
                ),
                (
                    "token_expiry_hours",
                    models.PositiveIntegerField(
                        default=24, help_text="Hours until verification token expires"
                    ),
                ),
                (
                    "max_resend_attempts",
                    models.PositiveIntegerField(
                        default=3,
                        help_text="Maximum number of verification emails that can be sent per day",
                    ),
                ),
                (
                    "resend_cooldown_minutes",
                    models.PositiveIntegerField(
                        default=5,
                        help_text="Minutes to wait between resending verification emails",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Verification Settings",
                "verbose_name_plural": "Email Verification Settings",
                "db_table": "email_verification_settings",
            },
        ),
        migrations.CreateModel(
            name="PasswordResetSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token_expiry_hours",
                    models.PositiveIntegerField(
                        default=1, help_text="Hours until password reset token expires"
                    ),
                ),
                (
                    "max_attempts_per_email_per_hour",
                    models.PositiveIntegerField(
                        default=3,
                        help_text="Maximum password reset attempts per email per hour",
                    ),
                ),
                (
                    "max_attempts_per_ip_per_hour",
                    models.PositiveIntegerField(
                        default=5,
                        help_text="Maximum password reset attempts per IP per hour",
                    ),
                ),
                (
                    "require_email_verification",
                    models.BooleanField(
                        default=True,
                        help_text="Require email to be verified before allowing password reset",
                    ),
                ),
            ],
            options={
                "verbose_name": "Password Reset Settings",
                "verbose_name_plural": "Password Reset Settings",
                "db_table": "password_reset_settings",
            },
        ),
        migrations.AlterModelOptions(
            name="userprofile",
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
            },
        ),
        migrations.AlterUniqueTogether(
            name="roleapplication",
            unique_together={("user", "requested_role")},
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="github",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="linkedin",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="primary_role",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="roles",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="twitter",
        ),
        migrations.RemoveField(
            model_name="userprofile",
            name="years_of_experience",
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="availability",
            field=models.CharField(
                blank=True,
                choices=[
                    ("1-2h/week", "1-2 hours per week"),
                    ("3-5h/week", "3-5 hours per week"),
                    ("5-10h/week", "5-10 hours per week"),
                    ("10+h/week", "More than 10 hours per week"),
                    ("flexible", "Flexible schedule"),
                ],
                help_text="Time availability for mentoring",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="due_diligence_requirements",
            field=models.TextField(blank=True, help_text="Due diligence requirements"),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="expertise_areas",
            field=models.JSONField(
                blank=True, default=list, help_text="Areas of expertise (JSON array)"
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="investment_focus",
            field=models.JSONField(
                blank=True,
                default=list,
                help_text="Investment focus areas (JSON array)",
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="investment_range",
            field=models.CharField(
                blank=True,
                choices=[
                    ("1k-10k", "$1,000 - $10,000"),
                    ("10k-50k", "$10,000 - $50,000"),
                    ("50k-100k", "$50,000 - $100,000"),
                    ("100k-500k", "$100,000 - $500,000"),
                    ("500k-1m", "$500,000 - $1,000,000"),
                    ("1m-5m", "$1,000,000 - $5,000,000"),
                    ("5m+", "More than $5,000,000"),
                ],
                help_text="Typical investment range",
                max_length=50,
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="investment_stage",
            field=models.JSONField(
                blank=True, default=list, help_text="Preferred investment stages"
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="mentoring_experience",
            field=models.TextField(
                blank=True, help_text="Previous mentoring experience"
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="portfolio_companies",
            field=models.TextField(
                blank=True, help_text="Previous investments or portfolio companies"
            ),
        ),
        migrations.AddField(
            model_name="roleapplication",
            name="preferred_communication",
            field=models.JSONField(
                blank=True, default=list, help_text="Preferred communication methods"
            ),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="company",
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="language",
            field=models.CharField(
                choices=[("en", "English"), ("ar", "Arabic")],
                default="en",
                max_length=10,
            ),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="last_activity",
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="profile_image",
            field=models.ImageField(
                blank=True,
                help_text="Profile picture (will be optimized automatically)",
                null=True,
                storage=api.storage.OptimizedImageStorage(),
                upload_to="profile_images/",
            ),
        ),
        migrations.RemoveField(
            model_name="roleapplication",
            name="investment_criteria",
        ),
        migrations.RemoveField(
            model_name="roleapplication",
            name="linkedin_profile",
        ),
        migrations.RemoveField(
            model_name="roleapplication",
            name="maximum_investment",
        ),
        migrations.RemoveField(
            model_name="roleapplication",
            name="minimum_investment",
        ),
        migrations.RemoveField(
            model_name="roleapplication",
            name="moderation_experience",
        ),
        migrations.RemoveField(
            model_name="roleapplication",
            name="years_of_experience",
        ),
        migrations.CreateModel(
            name="EmailVerificationAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("attempted_at", models.DateTimeField(auto_now_add=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("success", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verification_attempts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "email_verification_attempts",
                "ordering": ["-attempted_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "attempted_at"],
                        name="email_verif_user_id_b42034_idx",
                    ),
                    models.Index(
                        fields=["email", "attempted_at"],
                        name="email_verif_email_d231cd_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "attempted_at"],
                        name="email_verif_ip_addr_2e565d_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="EmailVerificationToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                ("email", models.EmailField(max_length=254)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                ("used_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_verification_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "email_verification_tokens",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(fields=["token"], name="email_verif_token_df7c5e_idx"),
                    models.Index(
                        fields=["user", "is_used"],
                        name="email_verif_user_id_35194a_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="email_verif_expires_770728_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PasswordResetAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("attempted_at", models.DateTimeField(auto_now_add=True)),
                ("success", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_reset_attempts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "password_reset_attempts",
                "ordering": ["-attempted_at"],
                "indexes": [
                    models.Index(
                        fields=["email", "attempted_at"],
                        name="password_re_email_eb0c38_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "attempted_at"],
                        name="password_re_ip_addr_a19a3f_idx",
                    ),
                    models.Index(
                        fields=["user", "attempted_at"],
                        name="password_re_user_id_51fcf5_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                ("secure_hash", models.CharField(max_length=128)),
                ("email", models.EmailField(max_length=254)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                ("used_at", models.DateTimeField(blank=True, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_reset_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "password_reset_tokens",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(fields=["token"], name="password_re_token_060a1f_idx"),
                    models.Index(
                        fields=["user", "is_used"],
                        name="password_re_user_id_cd37a3_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="password_re_expires_8e96b7_idx"
                    ),
                    models.Index(fields=["email"], name="password_re_email_2bb9da_idx"),
                ],
            },
        ),
    ]
