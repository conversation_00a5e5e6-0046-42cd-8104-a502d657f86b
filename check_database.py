#!/usr/bin/env python
"""
Check database for registered users and their role-specific data
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from users.models import UserApproval, UserProfile
from django.contrib.auth.models import User

def check_registrations():
    """Check the registered users and their data"""
    print("[DATABASE] Checking registered users and role-specific data...")
    print("=" * 60)
    
    # Get all user approvals
    approvals = UserApproval.objects.all().order_by('-created_at')
    print(f"Total user approvals: {approvals.count()}")
    
    for approval in approvals:
        user = approval.user
        print(f"\n[USER] {user.username} ({user.first_name} {user.last_name})")
        print(f"   Email: {user.email}")
        print(f"   Status: {approval.status}")
        print(f"   Created: {approval.created_at}")
        
        if hasattr(user, 'profile'):
            profile = user.profile
            print(f"   Requested Role: {profile.requested_role_name}")
            
            # Check role-specific data
            if profile.requested_role_name == 'entrepreneur':
                print(f"   [ENTREPRENEUR DATA]")
                print(f"     Business Name: {profile.business_name}")
                print(f"     Business Stage: {profile.business_stage}")
                print(f"     Funding Needed: {profile.funding_needed}")
                print(f"     Business Description: {profile.business_description}")
                
            elif profile.requested_role_name == 'mentor':
                print(f"   [MENTOR DATA]")
                print(f"     Expertise: {profile.expertise}")
                print(f"     Experience: {profile.mentor_experience}")
                print(f"     Mentorship Areas: {profile.mentorship_areas}")
                print(f"     Availability: {profile.availability}")
                
            elif profile.requested_role_name == 'investor':
                print(f"   [INVESTOR DATA]")
                print(f"     Investment Range: {profile.investment_range}")
                print(f"     Investment Stage: {profile.investment_stage}")
                print(f"     Preferred Industries: {profile.preferred_industries}")
                print(f"     Investment Criteria: {profile.investment_criteria}")
                
            elif profile.requested_role_name == 'user':
                print(f"   [USER DATA]")
                print(f"     Interests: {profile.interests}")
                print(f"     Goals: {profile.goals}")
            
            # Check profile summary
            print(f"   [PROFILE SUMMARY]")
            print(f"     Location: {profile.location}")
            print(f"     Company: {profile.company}")
            print(f"     Job Title: {profile.job_title}")
            print(f"     Phone: {profile.phone_number}")
            print(f"     Bio: {profile.bio}")
            
            # Check role additional info
            if profile.role_additional_info:
                print(f"   [ADDITIONAL INFO] {profile.role_additional_info}")

def check_role_applications():
    """Check role applications"""
    print(f"\n[ROLE APPLICATIONS] Checking role applications...")
    
    try:
        from users.models import RoleApplication
        applications = RoleApplication.objects.all()
        print(f"Total role applications: {applications.count()}")
        
        for app in applications:
            print(f"   - {app.user.username} -> {app.requested_role.name} ({app.status})")
            print(f"     Motivation: {app.motivation}")
            
    except Exception as e:
        print(f"   Error checking role applications: {e}")

if __name__ == '__main__':
    check_registrations()
    check_role_applications()
    print("\n[COMPLETE] Database check complete!")
