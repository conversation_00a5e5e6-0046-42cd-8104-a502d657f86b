<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Server Startup Guide</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2d3748;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .step h3 {
            color: #2d3748;
            margin-top: 0;
        }
        .command {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.2s;
        }
        .button:hover {
            background: #3182ce;
        }
        .success {
            background: #c6f6d5;
            border-color: #38a169;
            color: #22543d;
        }
        .warning {
            background: #fef5e7;
            border-color: #d69e2e;
            color: #744210;
        }
        .error {
            background: #fed7d7;
            border-color: #e53e3e;
            color: #742a2a;
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-offline { background: #e53e3e; }
        .status-online { background: #38a169; }
        .status-unknown { background: #d69e2e; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Server Startup Guide</h1>
        
        <div class="step">
            <h3>📋 Current Status</h3>
            <p>
                <span class="status-indicator" id="backend-indicator"></span>
                <strong>Backend Server:</strong> <span id="backend-status">Checking...</span>
            </p>
            <p>
                <span class="status-indicator" id="frontend-indicator"></span>
                <strong>Frontend Server:</strong> <span id="frontend-status">Checking...</span>
            </p>
            <button class="button" onclick="checkStatus()">🔄 Refresh Status</button>
        </div>

        <div class="step">
            <h3>🔧 Step 1: Start Backend Server</h3>
            <p>Open a terminal/command prompt and run these commands:</p>
            <div class="command">
# Navigate to your project directory
cd path/to/your/project

# Go to backend folder
cd backend

# Start Django server
python manage.py runserver 8000
            </div>
            <p><strong>Expected output:</strong> "Starting development server at http://127.0.0.1:8000/"</p>
            <button class="button" onclick="testBackend()">🧪 Test Backend</button>
        </div>

        <div class="step">
            <h3>⚛️ Step 2: Start Frontend Server</h3>
            <p>Open a <strong>NEW</strong> terminal/command prompt and run:</p>
            <div class="command">
# Navigate to your project directory
cd path/to/your/project

# Go to frontend folder
cd frontend

# Start React development server
npm run dev
            </div>
            <p><strong>Expected output:</strong> "Local: http://localhost:3000/"</p>
            <button class="button" onclick="testFrontend()">🧪 Test Frontend</button>
        </div>

        <div class="step success" id="ready-step" style="display: none;">
            <h3>🎉 Servers are Ready!</h3>
            <p>Both servers are running successfully. You can now access the application:</p>
            <div class="quick-links">
                <a href="http://localhost:3000" class="button" target="_blank">🏠 Main App</a>
                <a href="http://localhost:8000/admin/" class="button" target="_blank">👑 Admin Panel</a>
                <a href="./comprehensive-app-test.html" class="button" target="_blank">🧪 Run Tests</a>
                <a href="./test-validation.html" class="button" target="_blank">✅ Validation</a>
            </div>
        </div>

        <div class="step warning">
            <h3>⚠️ Troubleshooting</h3>
            <details>
                <summary><strong>Backend Issues</strong></summary>
                <ul>
                    <li><strong>Python not found:</strong> Install Python from python.org</li>
                    <li><strong>Module errors:</strong> Run <code>pip install -r requirements.txt</code></li>
                    <li><strong>Database errors:</strong> Run <code>python manage.py migrate</code></li>
                    <li><strong>Port in use:</strong> Use <code>python manage.py runserver 8001</code></li>
                </ul>
            </details>
            <details>
                <summary><strong>Frontend Issues</strong></summary>
                <ul>
                    <li><strong>npm not found:</strong> Install Node.js from nodejs.org</li>
                    <li><strong>Module errors:</strong> Run <code>npm install</code></li>
                    <li><strong>Port in use:</strong> Run <code>npx kill-port 3000</code></li>
                    <li><strong>Build errors:</strong> Delete node_modules and run <code>npm install</code></li>
                </ul>
            </details>
        </div>

        <div class="step">
            <h3>🧪 Test All Fixes</h3>
            <p>Once both servers are running, test all the implemented fixes:</p>
            <button class="button" onclick="runAllTests()">🚀 Run All Tests</button>
            <button class="button" onclick="openTestSuite()">📊 Open Test Suite</button>
            <button class="button" onclick="openValidation()">✅ Open Validation</button>
        </div>

        <div class="step" id="test-results" style="display: none;">
            <h3>📊 Test Results</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <script>
        let serverStatus = {
            backend: false,
            frontend: false
        };

        async function checkStatus() {
            // Check backend
            try {
                const backendResponse = await fetch('http://localhost:8000/api/health/', {
                    method: 'GET',
                    mode: 'cors'
                });
                serverStatus.backend = backendResponse.ok;
            } catch (error) {
                serverStatus.backend = false;
            }

            // Check frontend
            try {
                const frontendResponse = await fetch('http://localhost:3000/', {
                    method: 'GET',
                    mode: 'no-cors'
                });
                serverStatus.frontend = true; // If no error, assume it's running
            } catch (error) {
                serverStatus.frontend = false;
            }

            updateStatusDisplay();
        }

        function updateStatusDisplay() {
            // Backend status
            const backendIndicator = document.getElementById('backend-indicator');
            const backendStatus = document.getElementById('backend-status');
            
            if (serverStatus.backend) {
                backendIndicator.className = 'status-indicator status-online';
                backendStatus.textContent = 'Online ✅';
                backendStatus.style.color = '#22543d';
            } else {
                backendIndicator.className = 'status-indicator status-offline';
                backendStatus.textContent = 'Offline ❌';
                backendStatus.style.color = '#742a2a';
            }

            // Frontend status
            const frontendIndicator = document.getElementById('frontend-indicator');
            const frontendStatus = document.getElementById('frontend-status');
            
            if (serverStatus.frontend) {
                frontendIndicator.className = 'status-indicator status-online';
                frontendStatus.textContent = 'Online ✅';
                frontendStatus.style.color = '#22543d';
            } else {
                frontendIndicator.className = 'status-indicator status-offline';
                frontendStatus.textContent = 'Offline ❌';
                frontendStatus.style.color = '#742a2a';
            }

            // Show ready step if both are online
            const readyStep = document.getElementById('ready-step');
            if (serverStatus.backend && serverStatus.frontend) {
                readyStep.style.display = 'block';
            } else {
                readyStep.style.display = 'none';
            }
        }

        async function testBackend() {
            try {
                const response = await fetch('http://localhost:8000/api/health/');
                if (response.ok) {
                    alert('✅ Backend is running correctly!');
                    serverStatus.backend = true;
                } else {
                    alert(`⚠️ Backend responded with status: ${response.status}`);
                }
            } catch (error) {
                alert('❌ Backend is not running. Please start the Django server first.');
            }
            updateStatusDisplay();
        }

        async function testFrontend() {
            try {
                const response = await fetch('http://localhost:3000/', { mode: 'no-cors' });
                alert('✅ Frontend is running correctly!');
                serverStatus.frontend = true;
            } catch (error) {
                alert('❌ Frontend is not running. Please start the React development server first.');
            }
            updateStatusDisplay();
        }

        function runAllTests() {
            if (!serverStatus.backend || !serverStatus.frontend) {
                alert('⚠️ Please start both servers before running tests.');
                return;
            }
            
            window.open('./comprehensive-app-test.html', '_blank');
        }

        function openTestSuite() {
            window.open('./comprehensive-app-test.html', '_blank');
        }

        function openValidation() {
            window.open('./test-validation.html', '_blank');
        }

        // Auto-check status on load
        window.addEventListener('load', () => {
            checkStatus();
            
            // Check status every 30 seconds
            setInterval(checkStatus, 30000);
        });
    </script>
</body>
</html>
