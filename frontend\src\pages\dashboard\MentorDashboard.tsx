/**
 * <PERSON>tor Dashboard
 * Comprehensive dashboard for mentors with mentee management and session tracking
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicTable } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Users, 
  Calendar, 
  Clock, 
  TrendingUp, 
  MessageSquare, 
  Star,
  Video,
  FileText,
  Award,
  Target,
  CheckCircle,
  AlertCircle,
  Plus
} from 'lucide-react';

interface MentorStats {
  activeMentees: number;
  totalSessions: number;
  averageRating: number;
  completedPrograms: number;
  upcomingSessions: number;
  pendingRequests: number;
}

interface Mentee {
  id: string;
  name: string;
  company: string;
  industry: string;
  stage: string;
  progress: number;
  lastSession: string;
  nextSession?: string;
  status: 'active' | 'completed' | 'paused';
  avatar?: string;
}

interface Session {
  id: string;
  mentee: string;
  date: string;
  duration: number;
  type: 'video' | 'phone' | 'in_person';
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
}

const MentorDashboard: React.FC = () => {
  const { user } = useAuth();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [stats, setStats] = useState<MentorStats | null>(null);
  const [mentees, setMentees] = useState<Mentee[]>([]);
  const [upcomingSessions, setUpcomingSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockStats: MentorStats = {
        activeMentees: 8,
        totalSessions: 156,
        averageRating: 4.8,
        completedPrograms: 12,
        upcomingSessions: 3,
        pendingRequests: 2
      };

      const mockMentees: Mentee[] = [
        {
          id: '1',
          name: 'Ahmed Al-Rashid',
          company: 'FinTech Solutions',
          industry: 'FinTech',
          stage: 'MVP',
          progress: 75,
          lastSession: '2024-01-20',
          nextSession: '2024-01-25',
          status: 'active'
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          company: 'HealthTech Innovations',
          industry: 'HealthTech',
          stage: 'Growth',
          progress: 60,
          lastSession: '2024-01-18',
          nextSession: '2024-01-24',
          status: 'active'
        },
        {
          id: '3',
          name: 'Omar Hassan',
          company: 'EduPlatform',
          industry: 'EdTech',
          stage: 'Seed',
          progress: 45,
          lastSession: '2024-01-19',
          status: 'active'
        }
      ];

      const mockSessions: Session[] = [
        {
          id: '1',
          mentee: 'Ahmed Al-Rashid',
          date: '2024-01-25T14:00:00Z',
          duration: 60,
          type: 'video',
          status: 'scheduled'
        },
        {
          id: '2',
          mentee: 'Sarah Johnson',
          date: '2024-01-24T10:00:00Z',
          duration: 45,
          type: 'video',
          status: 'scheduled'
        },
        {
          id: '3',
          mentee: 'Omar Hassan',
          date: '2024-01-26T16:00:00Z',
          duration: 60,
          type: 'phone',
          status: 'scheduled'
        }
      ];

      setStats(mockStats);
      setMentees(mockMentees);
      setUpcomingSessions(mockSessions);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const menteeColumns = [
    {
      key: 'name',
      title: language === 'ar' ? 'الاسم' : 'Name',
      dataIndex: 'name',
      render: (value: string, record: Mentee) => (
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'}`}>
            <span className="text-sm font-semibold text-blue-600">
              {value.charAt(0)}
            </span>
          </div>
          <div>
            <div className="font-semibold font-arabic">{value}</div>
            <div className="text-sm text-gray-500 font-arabic">{record.company}</div>
          </div>
        </div>
      )
    },
    {
      key: 'industry',
      title: language === 'ar' ? 'الصناعة' : 'Industry',
      dataIndex: 'industry',
      render: (value: string, record: Mentee) => (
        <div>
          <div className="font-arabic">{value}</div>
          <div className="text-sm text-gray-500 font-arabic">{record.stage}</div>
        </div>
      )
    },
    {
      key: 'progress',
      title: language === 'ar' ? 'التقدم' : 'Progress',
      dataIndex: 'progress',
      render: (value: number) => (
        <div className="w-full">
          <div className="flex justify-between text-sm mb-1">
            <span className="font-arabic">{value}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${value}%` }}
            />
          </div>
        </div>
      )
    },
    {
      key: 'nextSession',
      title: language === 'ar' ? 'الجلسة القادمة' : 'Next Session',
      dataIndex: 'nextSession',
      render: (value: string) => (
        <span className="font-arabic">
          {value ? new Date(value).toLocaleDateString() : (language === 'ar' ? 'غير محددة' : 'Not scheduled')}
        </span>
      )
    },
    {
      key: 'actions',
      title: language === 'ar' ? 'الإجراءات' : 'Actions',
      dataIndex: 'id',
      render: (id: string) => (
        <div className={`flex space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
          <ArabicButton
            size="sm"
            variant="outline"
            onClick={() => navigate(`/dashboard/mentees/${id}`)}
          >
            {language === 'ar' ? 'عرض' : 'View'}
          </ArabicButton>
        </div>
      )
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div>
              <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                {language === 'ar' ? `مرحباً، ${user?.name || 'الموجه'}` : `Welcome, ${user?.name || 'Mentor'}`}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' 
                  ? 'لوحة تحكم الموجه - إدارة المتدربين وتتبع التقدم'
                  : 'Mentor Dashboard - Manage mentees and track progress'
                }
              </ArabicTypography>
            </div>
            <ArabicButton
              onClick={() => navigate('/dashboard/mentees/requests')}
              icon={<Plus className="w-4 h-4" />}
            >
              {language === 'ar' ? 'طلبات جديدة' : 'New Requests'}
            </ArabicButton>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mx-auto mb-4">
                <Users className="w-6 h-6 text-blue-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.activeMentees}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'المتدربون النشطون' : 'Active Mentees'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-lg mx-auto mb-4">
                <Calendar className="w-6 h-6 text-green-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.totalSessions}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'إجمالي الجلسات' : 'Total Sessions'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-lg mx-auto mb-4">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.averageRating}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'متوسط التقييم' : 'Average Rating'}
              </ArabicTypography>
            </ArabicCard>

            <ArabicCard className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-100 rounded-lg mx-auto mb-4">
                <Award className="w-6 h-6 text-purple-600" />
              </div>
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {stats.completedPrograms}
              </ArabicTypography>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'البرامج المكتملة' : 'Completed Programs'}
              </ArabicTypography>
            </ArabicCard>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Active Mentees */}
            <ArabicCard 
              title={language === 'ar' ? 'المتدربون النشطون' : 'Active Mentees'}
              headerAction={
                <ArabicButton
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/dashboard/mentees')}
                >
                  {language === 'ar' ? 'عرض الكل' : 'View All'}
                </ArabicButton>
              }
            >
              <ArabicTable
                columns={menteeColumns}
                data={mentees}
                rowKey="id"
              />
            </ArabicCard>

            {/* Recent Activity */}
            <ArabicCard title={language === 'ar' ? 'النشاط الأخير' : 'Recent Activity'}>
              <div className="space-y-4">
                {[
                  {
                    type: 'session',
                    message: language === 'ar' ? 'جلسة مكتملة مع أحمد الراشد' : 'Completed session with Ahmed Al-Rashid',
                    time: '2 hours ago',
                    icon: <CheckCircle className="w-5 h-5 text-green-500" />
                  },
                  {
                    type: 'feedback',
                    message: language === 'ar' ? 'تقييم جديد من سارة جونسون (5 نجوم)' : 'New feedback from Sarah Johnson (5 stars)',
                    time: '1 day ago',
                    icon: <Star className="w-5 h-5 text-yellow-500" />
                  },
                  {
                    type: 'request',
                    message: language === 'ar' ? 'طلب إرشاد جديد من عمر حسن' : 'New mentorship request from Omar Hassan',
                    time: '2 days ago',
                    icon: <AlertCircle className="w-5 h-5 text-blue-500" />
                  }
                ].map((activity, index) => (
                  <div key={index} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex-shrink-0 ${isRTL ? 'ml-3' : 'mr-3'}`}>
                      {activity.icon}
                    </div>
                    <div className="flex-1">
                      <ArabicTypography variant="body2" className="text-gray-900">
                        {activity.message}
                      </ArabicTypography>
                      <ArabicTypography variant="caption" color="secondary">
                        {activity.time}
                      </ArabicTypography>
                    </div>
                  </div>
                ))}
              </div>
            </ArabicCard>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upcoming Sessions */}
            <ArabicCard 
              title={language === 'ar' ? 'الجلسات القادمة' : 'Upcoming Sessions'}
              headerAction={
                <ArabicButton
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/dashboard/sessions')}
                >
                  {language === 'ar' ? 'عرض الكل' : 'View All'}
                </ArabicButton>
              }
            >
              <div className="space-y-4">
                {upcomingSessions.map((session) => (
                  <div key={session.id} className="border rounded-lg p-4">
                    <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div>
                        <ArabicTypography variant="body2" className="font-semibold">
                          {session.mentee}
                        </ArabicTypography>
                        <ArabicTypography variant="caption" color="secondary">
                          {new Date(session.date).toLocaleDateString()} • {session.duration} min
                        </ArabicTypography>
                      </div>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        {session.type === 'video' ? (
                          <Video className="w-4 h-4 text-blue-500" />
                        ) : (
                          <MessageSquare className="w-4 h-4 text-green-500" />
                        )}
                      </div>
                    </div>
                    <div className={`flex space-x-2 mt-3 ${isRTL ? 'space-x-reverse' : ''}`}>
                      <ArabicButton size="sm" className="flex-1">
                        {language === 'ar' ? 'انضم' : 'Join'}
                      </ArabicButton>
                      <ArabicButton size="sm" variant="outline">
                        {language === 'ar' ? 'إعادة جدولة' : 'Reschedule'}
                      </ArabicButton>
                    </div>
                  </div>
                ))}
              </div>
            </ArabicCard>

            {/* Quick Actions */}
            <ArabicCard title={language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}>
              <div className="space-y-3">
                <ArabicButton
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate('/dashboard/sessions/schedule')}
                  icon={<Calendar className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'جدولة جلسة' : 'Schedule Session'}
                </ArabicButton>
                
                <ArabicButton
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate('/dashboard/mentees/requests')}
                  icon={<Users className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'طلبات الإرشاد' : 'Mentorship Requests'}
                </ArabicButton>
                
                <ArabicButton
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate('/dashboard/resources')}
                  icon={<FileText className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'موارد الإرشاد' : 'Mentoring Resources'}
                </ArabicButton>
                
                <ArabicButton
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate('/dashboard/analytics')}
                  icon={<TrendingUp className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'تحليلات الأداء' : 'Performance Analytics'}
                </ArabicButton>
              </div>
            </ArabicCard>

            {/* Notifications */}
            <ArabicCard title={language === 'ar' ? 'الإشعارات' : 'Notifications'}>
              <div className="space-y-3">
                {stats && stats.pendingRequests > 0 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <ArabicTypography variant="body2" className="text-blue-800">
                      {language === 'ar' 
                        ? `لديك ${stats.pendingRequests} طلبات إرشاد جديدة`
                        : `You have ${stats.pendingRequests} new mentorship requests`
                      }
                    </ArabicTypography>
                  </div>
                )}
                
                {stats && stats.upcomingSessions > 0 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <ArabicTypography variant="body2" className="text-green-800">
                      {language === 'ar' 
                        ? `لديك ${stats.upcomingSessions} جلسات قادمة اليوم`
                        : `You have ${stats.upcomingSessions} sessions scheduled today`
                      }
                    </ArabicTypography>
                  </div>
                )}
              </div>
            </ArabicCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MentorDashboard;
