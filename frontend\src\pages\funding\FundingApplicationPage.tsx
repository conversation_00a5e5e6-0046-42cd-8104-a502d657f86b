/**
 * Funding Application Page
 * Apply for funding opportunities with comprehensive application forms
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicInput, ArabicProgress } from '../../components/ui/ArabicOptimizedComponents';
import { 
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Upload,
  FileText,
  DollarSign,
  Users,
  Target,
  TrendingUp,
  Calendar,
  Building
} from 'lucide-react';

interface FundingOpportunity {
  id: string;
  name: string;
  type: string;
  fundingRange: { min: number; max: number };
  requirements: string[];
  applicationDeadline?: string;
}

interface ApplicationData {
  // Company Information
  companyName: string;
  industry: string;
  stage: string;
  foundingDate: string;
  location: string;
  website: string;
  
  // Funding Details
  fundingAmount: number;
  useOfFunds: string;
  previousFunding: string;
  currentValuation: string;
  
  // Business Information
  businessModel: string;
  revenueModel: string;
  targetMarket: string;
  competitiveAdvantage: string;
  
  // Team Information
  teamSize: number;
  keyTeamMembers: string;
  advisors: string;
  
  // Traction & Metrics
  currentRevenue: string;
  userMetrics: string;
  growthMetrics: string;
  keyMilestones: string;
  
  // Documents
  businessPlan: File | null;
  pitchDeck: File | null;
  financialProjections: File | null;
  legalDocuments: File | null;
  
  // Additional Information
  whyThisFund: string;
  additionalInfo: string;
}

const FundingApplicationPage: React.FC = () => {
  const { opportunityId } = useParams();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [opportunity, setOpportunity] = useState<FundingOpportunity | null>(null);
  const [applicationData, setApplicationData] = useState<ApplicationData>({
    companyName: '',
    industry: '',
    stage: '',
    foundingDate: '',
    location: '',
    website: '',
    fundingAmount: 0,
    useOfFunds: '',
    previousFunding: '',
    currentValuation: '',
    businessModel: '',
    revenueModel: '',
    targetMarket: '',
    competitiveAdvantage: '',
    teamSize: 1,
    keyTeamMembers: '',
    advisors: '',
    currentRevenue: '',
    userMetrics: '',
    growthMetrics: '',
    keyMilestones: '',
    businessPlan: null,
    pitchDeck: null,
    financialProjections: null,
    legalDocuments: null,
    whyThisFund: '',
    additionalInfo: ''
  });
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  const totalSteps = 6;
  const progress = (currentStep / totalSteps) * 100;

  useEffect(() => {
    loadOpportunity();
  }, [opportunityId]);

  const loadOpportunity = async () => {
    try {
      // Mock data - replace with actual API call
      const mockOpportunity: FundingOpportunity = {
        id: opportunityId || '1',
        name: 'MENA Ventures',
        type: 'vc',
        fundingRange: { min: 100000, max: 2000000 },
        requirements: ['MVP ready', 'Traction metrics', 'Strong team'],
        applicationDeadline: '2024-03-31'
      };
      
      setOpportunity(mockOpportunity);
    } catch (error) {
      console.error('Error loading opportunity:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ApplicationData, value: string | number | File | null) => {
    setApplicationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileUpload = (field: keyof ApplicationData, file: File | null) => {
    setApplicationData(prev => ({
      ...prev,
      [field]: file
    }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    try {
      // Submit application - replace with actual API call
      console.log('Submitting application:', applicationData);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate to success page
      navigate(`/dashboard/funding/application/success/${opportunityId}`);
    } catch (error) {
      console.error('Error submitting application:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Building className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'معلومات الشركة' : 'Company Information'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'أخبرنا عن شركتك' : 'Tell us about your company'}
              </ArabicTypography>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ArabicInput
                label={language === 'ar' ? 'اسم الشركة' : 'Company Name'}
                value={applicationData.companyName}
                onChange={(value) => handleInputChange('companyName', value)}
                required
              />
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'الصناعة' : 'Industry'}
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  value={applicationData.industry}
                  onChange={(e) => handleInputChange('industry', e.target.value)}
                  dir={isRTL ? 'rtl' : 'ltr'}
                >
                  <option value="">{language === 'ar' ? 'اختر الصناعة' : 'Select Industry'}</option>
                  <option value="fintech">{language === 'ar' ? 'التكنولوجيا المالية' : 'FinTech'}</option>
                  <option value="healthtech">{language === 'ar' ? 'التكنولوجيا الصحية' : 'HealthTech'}</option>
                  <option value="edtech">{language === 'ar' ? 'التكنولوجيا التعليمية' : 'EdTech'}</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'مرحلة الشركة' : 'Company Stage'}
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  value={applicationData.stage}
                  onChange={(e) => handleInputChange('stage', e.target.value)}
                  dir={isRTL ? 'rtl' : 'ltr'}
                >
                  <option value="">{language === 'ar' ? 'اختر المرحلة' : 'Select Stage'}</option>
                  <option value="pre-seed">{language === 'ar' ? 'ما قبل البذرة' : 'Pre-Seed'}</option>
                  <option value="seed">{language === 'ar' ? 'البذرة' : 'Seed'}</option>
                  <option value="series-a">{language === 'ar' ? 'الجولة أ' : 'Series A'}</option>
                </select>
              </div>

              <ArabicInput
                label={language === 'ar' ? 'تاريخ التأسيس' : 'Founding Date'}
                type="date"
                value={applicationData.foundingDate}
                onChange={(value) => handleInputChange('foundingDate', value)}
                required
              />

              <ArabicInput
                label={language === 'ar' ? 'الموقع' : 'Location'}
                value={applicationData.location}
                onChange={(value) => handleInputChange('location', value)}
                required
              />

              <ArabicInput
                label={language === 'ar' ? 'الموقع الإلكتروني' : 'Website'}
                type="url"
                value={applicationData.website}
                onChange={(value) => handleInputChange('website', value)}
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <DollarSign className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'تفاصيل التمويل' : 'Funding Details'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'حدد احتياجات التمويل' : 'Specify your funding needs'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <ArabicInput
                label={language === 'ar' ? 'مبلغ التمويل المطلوب' : 'Funding Amount Requested'}
                type="number"
                value={applicationData.fundingAmount.toString()}
                onChange={(value) => handleInputChange('fundingAmount', parseInt(value) || 0)}
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'استخدام الأموال' : 'Use of Funds'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.useOfFunds}
                  onChange={(e) => handleInputChange('useOfFunds', e.target.value)}
                  placeholder={language === 'ar' ? 'اشرح كيف ستستخدم الأموال...' : 'Explain how you will use the funds...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'التمويل السابق' : 'Previous Funding'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.previousFunding}
                  onChange={(e) => handleInputChange('previousFunding', e.target.value)}
                  placeholder={language === 'ar' ? 'اذكر أي تمويل سابق...' : 'Mention any previous funding...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <ArabicInput
                label={language === 'ar' ? 'التقييم الحالي' : 'Current Valuation'}
                value={applicationData.currentValuation}
                onChange={(value) => handleInputChange('currentValuation', value)}
                placeholder={language === 'ar' ? 'مثال: 5 مليون دولار' : 'e.g., $5 million'}
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Target className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'معلومات العمل' : 'Business Information'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'اشرح نموذج عملك' : 'Explain your business model'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'نموذج العمل' : 'Business Model'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.businessModel}
                  onChange={(e) => handleInputChange('businessModel', e.target.value)}
                  placeholder={language === 'ar' ? 'اشرح كيف تعمل شركتك...' : 'Explain how your company works...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'نموذج الإيرادات' : 'Revenue Model'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.revenueModel}
                  onChange={(e) => handleInputChange('revenueModel', e.target.value)}
                  placeholder={language === 'ar' ? 'كيف تحقق الإيرادات؟' : 'How do you generate revenue?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'السوق المستهدف' : 'Target Market'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.targetMarket}
                  onChange={(e) => handleInputChange('targetMarket', e.target.value)}
                  placeholder={language === 'ar' ? 'من هم عملاؤك المستهدفون؟' : 'Who are your target customers?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'الميزة التنافسية' : 'Competitive Advantage'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.competitiveAdvantage}
                  onChange={(e) => handleInputChange('competitiveAdvantage', e.target.value)}
                  placeholder={language === 'ar' ? 'ما الذي يميزك عن المنافسين؟' : 'What sets you apart from competitors?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Users className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'معلومات الفريق' : 'Team Information'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'أخبرنا عن فريقك' : 'Tell us about your team'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <ArabicInput
                label={language === 'ar' ? 'حجم الفريق' : 'Team Size'}
                type="number"
                value={applicationData.teamSize.toString()}
                onChange={(value) => handleInputChange('teamSize', parseInt(value) || 1)}
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'أعضاء الفريق الرئيسيون' : 'Key Team Members'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.keyTeamMembers}
                  onChange={(e) => handleInputChange('keyTeamMembers', e.target.value)}
                  placeholder={language === 'ar' ? 'اذكر أعضاء الفريق الرئيسيين وخبراتهم...' : 'List key team members and their backgrounds...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'المستشارون' : 'Advisors'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.advisors}
                  onChange={(e) => handleInputChange('advisors', e.target.value)}
                  placeholder={language === 'ar' ? 'اذكر المستشارين والموجهين...' : 'List advisors and mentors...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <TrendingUp className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'الجذب والمقاييس' : 'Traction & Metrics'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'أظهر تقدم شركتك' : 'Show your company progress'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <ArabicInput
                label={language === 'ar' ? 'الإيرادات الحالية' : 'Current Revenue'}
                value={applicationData.currentRevenue}
                onChange={(value) => handleInputChange('currentRevenue', value)}
                placeholder={language === 'ar' ? 'مثال: 100,000 ريال شهرياً' : 'e.g., $10,000 monthly'}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'مقاييس المستخدمين' : 'User Metrics'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.userMetrics}
                  onChange={(e) => handleInputChange('userMetrics', e.target.value)}
                  placeholder={language === 'ar' ? 'عدد المستخدمين، معدل النمو، الاحتفاظ...' : 'Number of users, growth rate, retention...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'مقاييس النمو' : 'Growth Metrics'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.growthMetrics}
                  onChange={(e) => handleInputChange('growthMetrics', e.target.value)}
                  placeholder={language === 'ar' ? 'معدلات النمو الشهرية والسنوية...' : 'Monthly and annual growth rates...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'المعالم الرئيسية' : 'Key Milestones'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.keyMilestones}
                  onChange={(e) => handleInputChange('keyMilestones', e.target.value)}
                  placeholder={language === 'ar' ? 'الإنجازات المهمة والمعالم التي حققتها...' : 'Important achievements and milestones reached...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      case 6:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <FileText className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'المستندات والمعلومات الإضافية' : 'Documents & Additional Information'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'ارفع المستندات المطلوبة' : 'Upload required documents'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              {/* Document Upload Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {[
                  { key: 'businessPlan', label: language === 'ar' ? 'خطة العمل' : 'Business Plan', required: true },
                  { key: 'pitchDeck', label: language === 'ar' ? 'العرض التقديمي' : 'Pitch Deck', required: true },
                  { key: 'financialProjections', label: language === 'ar' ? 'التوقعات المالية' : 'Financial Projections', required: false },
                  { key: 'legalDocuments', label: language === 'ar' ? 'الوثائق القانونية' : 'Legal Documents', required: false }
                ].map((doc) => (
                  <div key={doc.key} className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <ArabicTypography variant="body2" className="font-semibold mb-1">
                      {doc.label} {doc.required && <span className="text-red-500">*</span>}
                    </ArabicTypography>
                    <input
                      type="file"
                      accept=".pdf,.doc,.docx,.ppt,.pptx"
                      onChange={(e) => handleFileUpload(doc.key as keyof ApplicationData, e.target.files?.[0] || null)}
                      className="hidden"
                      id={doc.key}
                    />
                    <label htmlFor={doc.key} className="cursor-pointer">
                      <ArabicButton size="sm" variant="outline" className="mt-2">
                        {language === 'ar' ? 'اختر ملف' : 'Choose File'}
                      </ArabicButton>
                    </label>
                    {applicationData[doc.key as keyof ApplicationData] && (
                      <p className="text-sm text-green-600 mt-2 font-arabic">
                        {(applicationData[doc.key as keyof ApplicationData] as File)?.name}
                      </p>
                    )}
                  </div>
                ))}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'لماذا هذا الصندوق؟' : 'Why this fund?'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.whyThisFund}
                  onChange={(e) => handleInputChange('whyThisFund', e.target.value)}
                  placeholder={language === 'ar' ? 'لماذا تريد العمل مع هذا الصندوق تحديداً؟' : 'Why do you want to work with this specific fund?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'معلومات إضافية' : 'Additional Information'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.additionalInfo}
                  onChange={(e) => handleInputChange('additionalInfo', e.target.value)}
                  placeholder={language === 'ar' ? 'أي معلومات إضافية تود مشاركتها...' : 'Any additional information you would like to share...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <ArabicButton
              variant="ghost"
              onClick={() => navigate(-1)}
              icon={<ArrowLeft className="w-4 h-4" />}
              className={isRTL ? 'ml-4' : 'mr-4'}
            >
              {language === 'ar' ? 'رجوع' : 'Back'}
            </ArabicButton>
            <div>
              <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                {language === 'ar' ? 'طلب التمويل' : 'Funding Application'}
              </ArabicTypography>
              {opportunity && (
                <ArabicTypography variant="body1" color="secondary">
                  {opportunity.name}
                </ArabicTypography>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <ArabicProgress
            value={progress}
            label={`${language === 'ar' ? 'الخطوة' : 'Step'} ${currentStep} ${language === 'ar' ? 'من' : 'of'} ${totalSteps}`}
            showPercentage={false}
            size="lg"
            color="blue"
          />
        </div>

        {/* Application Form */}
        <ArabicCard className="mb-8">
          {renderStep()}
        </ArabicCard>

        {/* Navigation Buttons */}
        <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicButton
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            icon={isRTL ? <ArrowRight className="w-4 h-4" /> : <ArrowLeft className="w-4 h-4" />}
            iconPosition="start"
          >
            {language === 'ar' ? 'السابق' : 'Previous'}
          </ArabicButton>

          {currentStep === totalSteps ? (
            <ArabicButton
              onClick={handleSubmit}
              disabled={submitting}
              icon={submitting ? undefined : <CheckCircle className="w-4 h-4" />}
              iconPosition="end"
            >
              {submitting 
                ? (language === 'ar' ? 'جاري الإرسال...' : 'Submitting...')
                : (language === 'ar' ? 'إرسال الطلب' : 'Submit Application')
              }
            </ArabicButton>
          ) : (
            <ArabicButton
              onClick={handleNext}
              icon={isRTL ? <ArrowLeft className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
              iconPosition="end"
            >
              {language === 'ar' ? 'التالي' : 'Next'}
            </ArabicButton>
          )}
        </div>
      </div>
    </div>
  );
};

export default FundingApplicationPage;
