/**
 * GEMINI MODEL SELECTOR COMPONENT
 * Shows all available 2025 Gemini models with their capabilities
 */

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Zap, Brain, Mic, Eye, Video, Code, Search, Sparkles } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { integratedAiApi } from '../../services/integratedAiApi';

interface ModelInfo {
  id: string;
  name: string;
  description: string;
  release_date: string;
  cost_tier: string;
  capabilities: {
    thinking: boolean;
    vision: boolean;
    audio: boolean;
    video: boolean;
    function_calling: boolean;
    code_execution: boolean;
  };
}

interface ModelSelectorProps {
  onModelSelect?: (modelId: string) => void;
  selectedModel?: string;
  showCapabilities?: boolean;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  onModelSelect,
  selectedModel,
  showCapabilities = true
}) => {
  const { t } = useTranslation();
  const [models, setModels] = useState<ModelInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadModels();
  }, []);

  const loadModels = async () => {
    try {
      setLoading(true);
      const response = await integratedAiApi.getAvailableModels({ latestOnly: true });
      
      if (response.success) {
        setModels(response.latest_models);
      } else {
        setError('Failed to load models');
      }
    } catch (err) {
      setError('Error loading models');
      console.error('Model loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getCostTierColor = (tier: string) => {
    switch (tier) {
      case 'premium': return 'text-purple-400 bg-purple-500/20';
      case 'medium': return 'text-blue-400 bg-blue-500/20';
      case 'low': return 'text-green-400 bg-green-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getCostTierIcon = (tier: string) => {
    switch (tier) {
      case 'premium': return <Sparkles size={12} />;
      case 'medium': return <Zap size={12} />;
      case 'low': return <Bot size={12} />;
      default: return <Bot size={12} />;
    }
  };

  const renderCapabilityIcon = (capability: string, enabled: boolean) => {
    const iconProps = { size: 14, className: enabled ? 'text-green-400' : 'text-gray-500' };
    
    switch (capability) {
      case 'thinking': return <Brain {...iconProps} />;
      case 'vision': return <Eye {...iconProps} />;
      case 'audio': return <Mic {...iconProps} />;
      case 'video': return <Video {...iconProps} />;
      case 'function_calling': return <Code {...iconProps} />;
      case 'code_execution': return <Search {...iconProps} />;
      default: return null;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        <span className="ml-3 text-gray-300">Loading models...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
        <p className="text-red-400">❌ {error}</p>
        <button 
          onClick={loadModels}
          className="mt-2 text-sm text-red-300 hover:text-red-200 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">
          🤖 Available Gemini Models (2025)
        </h3>
        <span className="text-sm text-gray-400">
          {models.length} models available
        </span>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {models.map((model) => (
          <div
            key={model.id}
            className={`
              relative bg-white/5 backdrop-blur-sm border rounded-lg p-4 cursor-pointer transition-all
              ${selectedModel === model.id 
                ? 'border-purple-500 bg-purple-500/10' 
                : 'border-white/10 hover:border-white/20 hover:bg-white/10'
              }
            `}
            onClick={() => onModelSelect?.(model.id)}
          >
            {/* Model Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h4 className="font-medium text-white text-sm mb-1">
                  {model.name}
                </h4>
                <p className="text-xs text-gray-400 leading-relaxed">
                  {model.description}
                </p>
              </div>
              
              {/* Cost Tier Badge */}
              <div className={`
                flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium
                ${getCostTierColor(model.cost_tier)}
              `}>
                {getCostTierIcon(model.cost_tier)}
                <span className="capitalize">{model.cost_tier}</span>
              </div>
            </div>

            {/* Release Date */}
            <div className="text-xs text-gray-500 mb-3">
              Released: {model.release_date}
            </div>

            {/* Capabilities */}
            {showCapabilities && (
              <div className="space-y-2">
                <div className="text-xs text-gray-400 font-medium">Capabilities:</div>
                <div className="grid grid-cols-3 gap-2">
                  {Object.entries(model.capabilities).map(([capability, enabled]) => (
                    <div
                      key={capability}
                      className="flex items-center space-x-1"
                      title={capability.replace('_', ' ').toUpperCase()}
                    >
                      {renderCapabilityIcon(capability, enabled)}
                      <span className={`text-xs ${enabled ? 'text-green-400' : 'text-gray-500'}`}>
                        {capability.replace('_', ' ').split(' ').map(word => 
                          word.charAt(0).toUpperCase() + word.slice(1)
                        ).join(' ')}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Selection Indicator */}
            {selectedModel === model.id && (
              <div className="absolute top-2 right-2">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Model Recommendations */}
      <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-300 mb-2">
          💡 Recommendations
        </h4>
        <div className="text-xs text-blue-200 space-y-1">
          <div>• <strong>gemini-2.5-flash</strong>: Best overall choice for most applications</div>
          <div>• <strong>gemini-2.5-pro</strong>: For complex reasoning and analysis</div>
          <div>• <strong>gemini-2.5-flash-lite</strong>: For high-volume, cost-sensitive applications</div>
        </div>
      </div>

      {/* Capabilities Legend */}
      {showCapabilities && (
        <div className="bg-gray-500/20 border border-gray-500/30 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-300 mb-2">
            🔧 Capabilities Legend
          </h4>
          <div className="grid grid-cols-2 gap-2 text-xs text-gray-400">
            <div className="flex items-center space-x-2">
              <Brain size={12} className="text-green-400" />
              <span>Thinking: Advanced reasoning</span>
            </div>
            <div className="flex items-center space-x-2">
              <Eye size={12} className="text-green-400" />
              <span>Vision: Image understanding</span>
            </div>
            <div className="flex items-center space-x-2">
              <Mic size={12} className="text-green-400" />
              <span>Audio: Speech processing</span>
            </div>
            <div className="flex items-center space-x-2">
              <Video size={12} className="text-green-400" />
              <span>Video: Video analysis</span>
            </div>
            <div className="flex items-center space-x-2">
              <Code size={12} className="text-green-400" />
              <span>Functions: Tool calling</span>
            </div>
            <div className="flex items-center space-x-2">
              <Search size={12} className="text-green-400" />
              <span>Code: Code execution</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelSelector;
