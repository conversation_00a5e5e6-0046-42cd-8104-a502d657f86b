import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  fetchUserRoles,
  fetchAvailableRoles,
  selectUserRoles,
  selectPrimaryRole,
  selectPermissionLevel,
  selectAvailableRoles,
  selectIsLoadingRoles,
  selectRoleErrors,
  selectHasRole,
  selectIsAdmin,
  selectIsModerator,
  selectCanModerate,
  selectCanAdmin,
  resetRoleState,
} from '../store/roleSlice';

/**
 * Unified Redux-based role management hook
 * Replaces all the duplicate role hooks and services
 */
export const useReduxRoles = () => {
  const dispatch = useAppDispatch();
  
  // Selectors
  const userRoles = useAppSelector(selectUserRoles);
  const primaryRole = useAppSelector(selectPrimaryRole);
  const permissionLevel = useAppSelector(selectPermissionLevel);
  const availableRoles = useAppSelector(selectAvailableRoles);
  const isLoading = useAppSelector(selectIsLoadingRoles);
  const errors = useAppSelector(selectRoleErrors);
  
  // Auth state
  const { isAuthenticated, user } = useAppSelector(state => state.auth);
  
  // Auto-fetch roles when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      dispatch(fetchUserRoles());
      dispatch(fetchAvailableRoles());
    } else {
      dispatch(resetRoleState());
    }
  }, [isAuthenticated, user, dispatch]);

  // Helper functions
  const hasRole = (roleName: string) => {
    return userRoles.some(role => role.name === roleName);
  };

  const isAdmin = () => {
    return ['admin', 'super_admin'].includes(primaryRole) ||
           userRoles.some(role => ['admin', 'super_admin'].includes(role.name));
  };

  const isModerator = () => {
    return ['moderator', 'admin', 'super_admin'].includes(primaryRole) ||
           userRoles.some(role => ['moderator', 'admin', 'super_admin'].includes(role.name));
  };

  const canModerate = () => {
    return ['moderate', 'admin', 'super_admin'].includes(permissionLevel);
  };

  const canAdmin = () => {
    return ['admin', 'super_admin'].includes(permissionLevel);
  };

  const refreshRoles = () => {
    if (isAuthenticated) {
      dispatch(fetchUserRoles());
    }
  };

  const refreshAvailableRoles = () => {
    dispatch(fetchAvailableRoles());
  };

  // Get display name for role
  const getRoleDisplayName = (roleName: string) => {
    const role = availableRoles.find(r => r.name === roleName);
    return role?.display_name || roleName;
  };

  // Get dashboard route for role
  const getDashboardRoute = (role?: string) => {
    const targetRole = role || primaryRole;
    
    switch (targetRole) {
      case 'super_admin':
        return '/super-admin/dashboard';
      case 'admin':
        return '/admin/dashboard';
      case 'moderator':
        return '/moderator/dashboard';
      case 'mentor':
        return '/mentor/dashboard';
      case 'investor':
        return '/investor/dashboard';
      case 'entrepreneur':
        return '/entrepreneur/dashboard';
      default:
        return '/user/dashboard';
    }
  };

  // Get sidebar navigation for role (compatible with existing NavItem interface)
  const getSidebarNavigation = () => {
    const baseNavigation = [
      {
        id: 'dashboard',
        name: 'Dashboard',
        path: getDashboardRoute(),
        icon: 'Home',
        allowedRoles: ['user', 'admin', 'super_admin', 'mentor', 'investor', 'entrepreneur', 'moderator'],
        category: 'main' as const
      },
    ];

    // Add role-specific navigation
    switch (primaryRole) {
      case 'super_admin':
        return [
          ...baseNavigation,
          {
            id: 'system',
            name: 'System Management',
            path: '/super-admin/system',
            icon: 'Settings',
            allowedRoles: ['super_admin'],
            category: 'system' as const
          },
          {
            id: 'users',
            name: 'User Management',
            path: '/super-admin/users',
            icon: 'Users',
            allowedRoles: ['super_admin'],
            category: 'main' as const
          },
          {
            id: 'analytics',
            name: 'Analytics',
            path: '/super-admin/analytics',
            icon: 'BarChart3',
            allowedRoles: ['super_admin'],
            category: 'main' as const
          },
          {
            id: 'ai-chat',
            name: 'AI Chat',
            path: '/super-admin/ai-chat',
            icon: 'Bot',
            allowedRoles: ['super_admin'],
            category: 'ai' as const
          },
          {
            id: 'ai',
            name: 'AI Management',
            path: '/super-admin/ai',
            icon: 'Bot',
            allowedRoles: ['super_admin'],
            category: 'ai' as const
          },
        ];

      case 'admin':
        return [
          ...baseNavigation,
          {
            id: 'ai-chat',
            name: 'AI Chat',
            path: '/admin/ai-chat',
            icon: 'Bot',
            allowedRoles: ['admin'],
            category: 'ai' as const
          },
          {
            id: 'users',
            name: 'User Management',
            path: '/admin/users',
            icon: 'Users',
            allowedRoles: ['admin'],
            category: 'main' as const
          },
          {
            id: 'content',
            name: 'Content Management',
            path: '/admin/content',
            icon: 'FileText',
            allowedRoles: ['admin'],
            category: 'content' as const
          },
          {
            id: 'analytics',
            name: 'Analytics',
            path: '/admin/analytics',
            icon: 'BarChart3',
            allowedRoles: ['admin'],
            category: 'main' as const
          },
        ];

      case 'moderator':
        return [
          ...baseNavigation,
          {
            id: 'ai-chat',
            name: 'AI Chat',
            path: '/moderator/ai-chat',
            icon: 'Bot',
            allowedRoles: ['moderator'],
            category: 'ai' as const
          },
          {
            id: 'content-review',
            name: 'Content Review',
            path: '/moderator/content',
            icon: 'Shield',
            allowedRoles: ['moderator'],
            category: 'content' as const
          },
          {
            id: 'reports',
            name: 'Reports',
            path: '/moderator/reports',
            icon: 'Flag',
            allowedRoles: ['moderator'],
            category: 'main' as const
          },
          {
            id: 'users',
            name: 'User Management',
            path: '/moderator/users',
            icon: 'Users',
            allowedRoles: ['moderator'],
            category: 'main' as const
          },
        ];

      case 'mentor':
        return [
          ...baseNavigation,
          {
            id: 'ai-chat',
            name: 'AI Chat',
            path: '/mentor/ai-chat',
            icon: 'Bot',
            allowedRoles: ['mentor'],
            category: 'ai' as const
          },
          {
            id: 'mentees',
            name: 'My Mentees',
            path: '/mentor/mentees',
            icon: 'Users',
            allowedRoles: ['mentor'],
            category: 'main' as const
          },
          {
            id: 'sessions',
            name: 'Sessions',
            path: '/mentor/sessions',
            icon: 'Calendar',
            allowedRoles: ['mentor'],
            category: 'main' as const
          },
          {
            id: 'resources',
            name: 'Resources',
            path: '/mentor/resources',
            icon: 'BookOpen',
            allowedRoles: ['mentor'],
            category: 'content' as const
          },
        ];

      case 'investor':
        return [
          ...baseNavigation,
          {
            id: 'ai-chat',
            name: 'AI Chat',
            path: '/investor/ai-chat',
            icon: 'Bot',
            allowedRoles: ['investor'],
            category: 'ai' as const
          },
          {
            id: 'opportunities',
            name: 'Opportunities',
            path: '/investor/opportunities',
            icon: 'TrendingUp',
            allowedRoles: ['investor'],
            category: 'main' as const
          },
          {
            id: 'portfolio',
            name: 'Portfolio',
            path: '/investor/portfolio',
            icon: 'Briefcase',
            allowedRoles: ['investor'],
            category: 'main' as const
          },
          {
            id: 'market',
            name: 'Market Analysis',
            path: '/investor/market',
            icon: 'BarChart3',
            allowedRoles: ['investor'],
            category: 'main' as const
          },
        ];

      case 'entrepreneur':
        return [
          ...baseNavigation,
          {
            id: 'ai-chat',
            name: 'AI Chat',
            path: '/entrepreneur/ai-chat',
            icon: 'Bot',
            allowedRoles: ['entrepreneur'],
            category: 'ai' as const
          },
          {
            id: 'projects',
            name: 'My Projects',
            path: '/entrepreneur/projects',
            icon: 'Folder',
            allowedRoles: ['entrepreneur'],
            category: 'main' as const
          },
          {
            id: 'business-plans',
            name: 'Business Plans',
            path: '/entrepreneur/business-plans',
            icon: 'FileText',
            allowedRoles: ['entrepreneur'],
            category: 'content' as const
          },
          {
            id: 'funding',
            name: 'Funding',
            path: '/entrepreneur/funding',
            icon: 'DollarSign',
            allowedRoles: ['entrepreneur'],
            category: 'main' as const
          },
          {
            id: 'mentorship',
            name: 'Mentorship',
            path: '/entrepreneur/mentorship',
            icon: 'Users',
            allowedRoles: ['entrepreneur'],
            category: 'main' as const
          },
        ];

      default: // user
        return [
          ...baseNavigation,
          {
            id: 'profile',
            name: 'Profile',
            path: '/user/profile',
            icon: 'User',
            allowedRoles: ['user'],
            category: 'main' as const
          },
          {
            id: 'settings',
            name: 'Settings',
            path: '/user/settings',
            icon: 'Settings',
            allowedRoles: ['user'],
            category: 'main' as const
          },
          {
            id: 'ai-chat',
            name: 'AI Chat',
            path: '/user/ai-chat',
            icon: 'Bot',
            allowedRoles: ['user'],
            category: 'ai' as const
          },
        ];
    }
  };

  return {
    // State
    userRoles,
    primaryRole,
    permissionLevel,
    availableRoles,
    isLoading,
    errors,
    
    // Helper functions
    hasRole,
    isAdmin,
    isModerator,
    canModerate,
    canAdmin,
    
    // Actions
    refreshRoles,
    refreshAvailableRoles,
    
    // Utilities
    getRoleDisplayName,
    getDashboardRoute,
    getSidebarNavigation,
  };
};

export default useReduxRoles;
