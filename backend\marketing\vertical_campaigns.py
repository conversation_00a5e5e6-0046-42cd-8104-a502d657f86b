"""
Vertical-Specific Marketing Campaign Strategies
Comprehensive marketing campaigns for FinTech, HealthTech, and EdTech verticals
"""

from typing import Dict, Any, List
from datetime import datetime, timedelta
import json


class VerticalMarketingCampaigns:
    """
    Marketing campaign strategies for different industry verticals
    """
    
    def __init__(self):
        self.campaigns = {
            'fintech': self._get_fintech_campaigns(),
            'healthtech': self._get_healthtech_campaigns(),
            'edtech': self._get_edtech_campaigns()
        }
    
    def get_campaign_strategy(self, vertical: str, language: str = 'en') -> Dict[str, Any]:
        """Get comprehensive campaign strategy for a vertical"""
        if vertical not in self.campaigns:
            return {'error': f'Vertical {vertical} not supported'}
        
        campaign = self.campaigns[vertical]
        
        # Adapt content for language
        if language == 'ar':
            campaign = self._adapt_for_arabic(campaign)
        
        return campaign
    
    def _get_fintech_campaigns(self) -> Dict[str, Any]:
        """FinTech marketing campaign strategy"""
        return {
            'vertical': 'fintech',
            'target_audience': {
                'primary': {
                    'demographics': {
                        'age': '25-40',
                        'income': 'Middle to high income',
                        'education': 'University graduates',
                        'occupation': 'Tech professionals, entrepreneurs, finance professionals'
                    },
                    'psychographics': {
                        'values': ['Innovation', 'Efficiency', 'Financial growth'],
                        'interests': ['Technology', 'Investment', 'Digital banking'],
                        'pain_points': ['Traditional banking limitations', 'Complex financial processes', 'Limited investment options']
                    },
                    'digital_behavior': {
                        'platforms': ['LinkedIn', 'Twitter', 'Instagram', 'YouTube'],
                        'content_preferences': ['Educational videos', 'Industry insights', 'Success stories'],
                        'device_usage': 'Mobile-first (80% mobile, 20% desktop)'
                    }
                },
                'secondary': {
                    'demographics': {
                        'age': '22-35',
                        'profile': 'Digital natives, early adopters',
                        'location': 'Urban areas in MENA region'
                    }
                }
            },
            'value_propositions': {
                'english': [
                    "AI-powered FinTech incubation with Islamic finance expertise",
                    "From idea to funding in 6 months with MENA market focus",
                    "Connect with Sharia-compliant investors and mentors"
                ],
                'arabic': [
                    "حاضنة التكنولوجيا المالية بالذكاء الاصطناعي مع خبرة التمويل الإسلامي",
                    "من الفكرة إلى التمويل في 6 أشهر مع التركيز على السوق العربي",
                    "تواصل مع المستثمرين والموجهين المتوافقين مع الشريعة"
                ]
            },
            'content_strategy': {
                'content_pillars': [
                    {
                        'pillar': 'Islamic FinTech Innovation',
                        'percentage': 30,
                        'topics': [
                            'Sharia-compliant financial products',
                            'Islamic banking technology trends',
                            'Halal investment opportunities',
                            'Regulatory compliance in MENA'
                        ]
                    },
                    {
                        'pillar': 'Success Stories',
                        'percentage': 25,
                        'topics': [
                            'FinTech startup success stories',
                            'Mentor-entrepreneur case studies',
                            'Funding success stories',
                            'Market expansion stories'
                        ]
                    },
                    {
                        'pillar': 'Educational Content',
                        'percentage': 25,
                        'topics': [
                            'FinTech business model tutorials',
                            'Regulatory landscape guides',
                            'Investment readiness training',
                            'Technology implementation guides'
                        ]
                    },
                    {
                        'pillar': 'Industry Insights',
                        'percentage': 20,
                        'topics': [
                            'MENA FinTech market trends',
                            'Investment landscape analysis',
                            'Regulatory updates',
                            'Technology innovations'
                        ]
                    }
                ],
                'content_calendar': {
                    'weekly_schedule': {
                        'monday': 'Industry Insights (LinkedIn Article)',
                        'tuesday': 'Educational Content (YouTube Video)',
                        'wednesday': 'Success Story (Instagram Post + Story)',
                        'thursday': 'Islamic FinTech Innovation (Twitter Thread)',
                        'friday': 'Community Engagement (Live Q&A)',
                        'saturday': 'Weekend Inspiration (Motivational Content)',
                        'sunday': 'Week Recap (Newsletter)'
                    }
                }
            },
            'channel_strategy': {
                'primary_channels': [
                    {
                        'platform': 'LinkedIn',
                        'budget_allocation': 30,
                        'content_types': ['Articles', 'Professional updates', 'Industry insights'],
                        'posting_frequency': '5 times per week',
                        'target_metrics': {
                            'followers': 10000,
                            'engagement_rate': 0.05,
                            'lead_generation': 50
                        }
                    },
                    {
                        'platform': 'Instagram',
                        'budget_allocation': 25,
                        'content_types': ['Stories', 'Reels', 'IGTV', 'Posts'],
                        'posting_frequency': '7 times per week',
                        'target_metrics': {
                            'followers': 15000,
                            'engagement_rate': 0.08,
                            'story_views': 5000
                        }
                    },
                    {
                        'platform': 'YouTube',
                        'budget_allocation': 20,
                        'content_types': ['Educational videos', 'Webinars', 'Interviews'],
                        'posting_frequency': '2 times per week',
                        'target_metrics': {
                            'subscribers': 5000,
                            'watch_time': 10000,
                            'video_views': 50000
                        }
                    },
                    {
                        'platform': 'Twitter',
                        'budget_allocation': 15,
                        'content_types': ['Threads', 'Quick updates', 'Industry news'],
                        'posting_frequency': '10 times per week',
                        'target_metrics': {
                            'followers': 8000,
                            'engagement_rate': 0.06,
                            'retweets': 500
                        }
                    }
                ],
                'secondary_channels': [
                    {
                        'platform': 'TikTok',
                        'budget_allocation': 10,
                        'content_types': ['Short educational videos', 'Behind-the-scenes'],
                        'posting_frequency': '3 times per week'
                    }
                ]
            },
            'campaign_tactics': {
                'launch_campaign': {
                    'name': 'FinTech Revolution MENA',
                    'duration': '3 months',
                    'budget': 50000,
                    'objectives': [
                        'Brand awareness in FinTech community',
                        'Generate 500 qualified leads',
                        'Establish thought leadership'
                    ],
                    'tactics': [
                        'Influencer partnerships with FinTech leaders',
                        'Sponsored content on industry publications',
                        'Virtual FinTech summit hosting',
                        'Podcast sponsorships'
                    ]
                },
                'ongoing_campaigns': [
                    {
                        'name': 'Islamic FinTech Spotlight',
                        'type': 'Content series',
                        'frequency': 'Monthly',
                        'description': 'Monthly deep-dive into Islamic FinTech innovations'
                    },
                    {
                        'name': 'Startup Success Stories',
                        'type': 'Case study series',
                        'frequency': 'Bi-weekly',
                        'description': 'Showcase successful FinTech startups from our program'
                    }
                ]
            },
            'performance_metrics': {
                'awareness_metrics': [
                    'Brand mention volume',
                    'Share of voice in FinTech discussions',
                    'Website traffic from social media'
                ],
                'engagement_metrics': [
                    'Social media engagement rate',
                    'Content shares and saves',
                    'Comment sentiment analysis'
                ],
                'conversion_metrics': [
                    'Lead generation from campaigns',
                    'Application conversion rate',
                    'Cost per acquisition (CPA)'
                ]
            }
        }
    
    def _get_healthtech_campaigns(self) -> Dict[str, Any]:
        """HealthTech marketing campaign strategy"""
        return {
            'vertical': 'healthtech',
            'target_audience': {
                'primary': {
                    'demographics': {
                        'age': '28-45',
                        'income': 'Middle to high income',
                        'education': 'Medical/healthcare background or tech professionals',
                        'occupation': 'Healthcare professionals, medical entrepreneurs, health-tech developers'
                    },
                    'psychographics': {
                        'values': ['Patient care improvement', 'Healthcare accessibility', 'Medical innovation'],
                        'interests': ['Digital health', 'Medical technology', 'Healthcare policy'],
                        'pain_points': ['Healthcare system inefficiencies', 'Limited access to quality care', 'Regulatory complexity']
                    }
                }
            },
            'value_propositions': {
                'english': [
                    "AI-powered HealthTech incubation with MENA healthcare expertise",
                    "Navigate complex healthcare regulations with expert guidance",
                    "Connect with healthcare investors and medical professionals"
                ],
                'arabic': [
                    "حاضنة التكنولوجيا الصحية بالذكاء الاصطناعي مع خبرة الرعاية الصحية العربية",
                    "تنقل عبر اللوائح الصحية المعقدة مع إرشاد الخبراء",
                    "تواصل مع مستثمري الرعاية الصحية والمهنيين الطبيين"
                ]
            },
            'content_strategy': {
                'content_pillars': [
                    {
                        'pillar': 'Healthcare Innovation',
                        'percentage': 35,
                        'topics': [
                            'Digital health solutions',
                            'Telemedicine advancements',
                            'AI in healthcare',
                            'Medical device innovation'
                        ]
                    },
                    {
                        'pillar': 'Regulatory Guidance',
                        'percentage': 25,
                        'topics': [
                            'Healthcare compliance in MENA',
                            'Medical device approval processes',
                            'Data privacy in healthcare',
                            'Clinical trial regulations'
                        ]
                    },
                    {
                        'pillar': 'Success Stories',
                        'percentage': 25,
                        'topics': [
                            'HealthTech startup journeys',
                            'Patient impact stories',
                            'Healthcare transformation cases',
                            'Investment success stories'
                        ]
                    },
                    {
                        'pillar': 'Industry Insights',
                        'percentage': 15,
                        'topics': [
                            'MENA healthcare market trends',
                            'Healthcare investment landscape',
                            'Technology adoption in healthcare',
                            'Future of healthcare delivery'
                        ]
                    }
                ]
            },
            'channel_strategy': {
                'primary_channels': [
                    {
                        'platform': 'LinkedIn',
                        'budget_allocation': 35,
                        'content_types': ['Professional articles', 'Industry insights', 'Thought leadership'],
                        'posting_frequency': '5 times per week'
                    },
                    {
                        'platform': 'YouTube',
                        'budget_allocation': 25,
                        'content_types': ['Educational webinars', 'Expert interviews', 'Product demos'],
                        'posting_frequency': '3 times per week'
                    },
                    {
                        'platform': 'Instagram',
                        'budget_allocation': 20,
                        'content_types': ['Behind-the-scenes', 'Infographics', 'Stories'],
                        'posting_frequency': '5 times per week'
                    },
                    {
                        'platform': 'Twitter',
                        'budget_allocation': 20,
                        'content_types': ['Industry news', 'Quick insights', 'Live updates'],
                        'posting_frequency': '8 times per week'
                    }
                ]
            }
        }
    
    def _get_edtech_campaigns(self) -> Dict[str, Any]:
        """EdTech marketing campaign strategy"""
        return {
            'vertical': 'edtech',
            'target_audience': {
                'primary': {
                    'demographics': {
                        'age': '25-40',
                        'income': 'Middle income',
                        'education': 'Education professionals or tech background',
                        'occupation': 'Educators, educational entrepreneurs, ed-tech developers'
                    },
                    'psychographics': {
                        'values': ['Educational excellence', 'Student empowerment', 'Learning innovation'],
                        'interests': ['Educational technology', 'Online learning', 'Student engagement'],
                        'pain_points': ['Traditional education limitations', 'Student engagement challenges', 'Educational resource accessibility']
                    }
                }
            },
            'value_propositions': {
                'english': [
                    "AI-powered EdTech incubation with Arabic education expertise",
                    "Transform education in the MENA region with innovative solutions",
                    "Connect with education investors and academic leaders"
                ],
                'arabic': [
                    "حاضنة التكنولوجيا التعليمية بالذكاء الاصطناعي مع خبرة التعليم العربي",
                    "حول التعليم في منطقة الشرق الأوسط وشمال أفريقيا بحلول مبتكرة",
                    "تواصل مع مستثمري التعليم والقادة الأكاديميين"
                ]
            },
            'content_strategy': {
                'content_pillars': [
                    {
                        'pillar': 'Educational Innovation',
                        'percentage': 40,
                        'topics': [
                            'AI in education',
                            'Personalized learning solutions',
                            'Arabic language learning technology',
                            'Mobile-first education platforms'
                        ]
                    },
                    {
                        'pillar': 'Success Stories',
                        'percentage': 30,
                        'topics': [
                            'EdTech startup success stories',
                            'Student achievement stories',
                            'Teacher empowerment cases',
                            'Educational transformation stories'
                        ]
                    },
                    {
                        'pillar': 'Educational Insights',
                        'percentage': 30,
                        'topics': [
                            'MENA education market trends',
                            'Learning methodology innovations',
                            'Educational policy updates',
                            'Technology adoption in schools'
                        ]
                    }
                ]
            },
            'channel_strategy': {
                'primary_channels': [
                    {
                        'platform': 'YouTube',
                        'budget_allocation': 30,
                        'content_types': ['Educational content', 'Teacher training', 'Student testimonials'],
                        'posting_frequency': '4 times per week'
                    },
                    {
                        'platform': 'Instagram',
                        'budget_allocation': 25,
                        'content_types': ['Educational infographics', 'Stories', 'Reels'],
                        'posting_frequency': '6 times per week'
                    },
                    {
                        'platform': 'LinkedIn',
                        'budget_allocation': 25,
                        'content_types': ['Professional insights', 'Industry articles', 'Thought leadership'],
                        'posting_frequency': '4 times per week'
                    },
                    {
                        'platform': 'TikTok',
                        'budget_allocation': 20,
                        'content_types': ['Quick educational tips', 'Behind-the-scenes', 'Student content'],
                        'posting_frequency': '5 times per week'
                    }
                ]
            }
        }
    
    def _adapt_for_arabic(self, campaign: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt campaign content for Arabic language and culture"""
        # Add Arabic-specific adaptations
        if 'cultural_adaptations' not in campaign:
            campaign['cultural_adaptations'] = {}
        
        campaign['cultural_adaptations'].update({
            'ramadan_considerations': {
                'content_timing': 'Adjust posting schedule for Ramadan hours',
                'messaging': 'Include Ramadan-appropriate messaging',
                'campaigns': 'Special Ramadan-themed campaigns'
            },
            'arabic_content': {
                'percentage': 70,  # 70% Arabic, 30% English
                'rtl_design': 'All visual content adapted for RTL reading',
                'cultural_references': 'Use local cultural references and examples'
            },
            'local_influencers': {
                'strategy': 'Partner with Arabic-speaking influencers',
                'focus': 'MENA region micro-influencers with high engagement'
            }
        })
        
        return campaign


# Global instance
_vertical_campaigns = None


def get_vertical_campaigns() -> VerticalMarketingCampaigns:
    """Get the global vertical campaigns instance"""
    global _vertical_campaigns
    if _vertical_campaigns is None:
        _vertical_campaigns = VerticalMarketingCampaigns()
    return _vertical_campaigns


class DigitalMarketingAutomation:
    """
    Automated digital marketing campaign management
    """

    def __init__(self):
        self.campaigns = get_vertical_campaigns()

    def generate_content_calendar(self, vertical: str, language: str = 'en', weeks: int = 4) -> Dict[str, Any]:
        """Generate automated content calendar for a vertical"""
        campaign = self.campaigns.get_campaign_strategy(vertical, language)

        if 'error' in campaign:
            return campaign

        content_strategy = campaign.get('content_strategy', {})
        content_pillars = content_strategy.get('content_pillars', [])
        weekly_schedule = content_strategy.get('content_calendar', {}).get('weekly_schedule', {})

        calendar = {
            'vertical': vertical,
            'language': language,
            'weeks': weeks,
            'content_calendar': []
        }

        # Generate content for each week
        for week in range(1, weeks + 1):
            week_content = {
                'week': week,
                'content': []
            }

            for day, content_type in weekly_schedule.items():
                # Select appropriate content pillar
                pillar = self._select_content_pillar(content_pillars, week, day)

                content_item = {
                    'day': day,
                    'content_type': content_type,
                    'pillar': pillar['pillar'],
                    'topic': self._generate_topic(pillar, vertical, language),
                    'platform': self._determine_platform(content_type),
                    'hashtags': self._generate_hashtags(vertical, pillar['pillar'], language),
                    'call_to_action': self._generate_cta(vertical, language)
                }

                week_content['content'].append(content_item)

            calendar['content_calendar'].append(week_content)

        return calendar

    def _select_content_pillar(self, pillars: List[Dict], week: int, day: str) -> Dict[str, Any]:
        """Select content pillar based on week and day"""
        # Rotate through pillars based on week
        pillar_index = (week - 1) % len(pillars)
        return pillars[pillar_index]

    def _generate_topic(self, pillar: Dict[str, Any], vertical: str, language: str) -> str:
        """Generate specific topic based on pillar and vertical"""
        topics = pillar.get('topics', [])
        if not topics:
            return f"{pillar['pillar']} content"

        # Select topic (could be randomized or based on algorithm)
        topic = topics[0]  # Simplified selection

        if language == 'ar':
            # Translate or adapt for Arabic
            topic_translations = {
                'Sharia-compliant financial products': 'المنتجات المالية المتوافقة مع الشريعة',
                'Digital health solutions': 'حلول الصحة الرقمية',
                'AI in education': 'الذكاء الاصطناعي في التعليم'
            }
            return topic_translations.get(topic, topic)

        return topic

    def _determine_platform(self, content_type: str) -> str:
        """Determine best platform for content type"""
        platform_mapping = {
            'LinkedIn Article': 'LinkedIn',
            'YouTube Video': 'YouTube',
            'Instagram Post + Story': 'Instagram',
            'Twitter Thread': 'Twitter',
            'Live Q&A': 'Instagram Live',
            'Motivational Content': 'Instagram',
            'Newsletter': 'Email'
        }

        return platform_mapping.get(content_type, 'Instagram')

    def _generate_hashtags(self, vertical: str, pillar: str, language: str) -> List[str]:
        """Generate relevant hashtags for content"""
        base_hashtags = {
            'fintech': ['#FinTech', '#StartupIncubator', '#MENA', '#Innovation'],
            'healthtech': ['#HealthTech', '#DigitalHealth', '#MedTech', '#Healthcare'],
            'edtech': ['#EdTech', '#Education', '#Learning', '#Innovation']
        }

        arabic_hashtags = {
            'fintech': ['#التكنولوجيا_المالية', '#ريادة_الأعمال', '#الابتكار'],
            'healthtech': ['#التكنولوجيا_الصحية', '#الصحة_الرقمية', '#الرعاية_الصحية'],
            'edtech': ['#التكنولوجيا_التعليمية', '#التعليم', '#التعلم']
        }

        hashtags = base_hashtags.get(vertical, ['#Innovation', '#Startup'])

        if language == 'ar':
            hashtags.extend(arabic_hashtags.get(vertical, ['#الابتكار']))

        return hashtags[:8]  # Limit to 8 hashtags

    def _generate_cta(self, vertical: str, language: str) -> str:
        """Generate call-to-action for content"""
        ctas = {
            'en': {
                'fintech': 'Join our FinTech incubator program today!',
                'healthtech': 'Transform healthcare with us!',
                'edtech': 'Revolutionize education together!'
            },
            'ar': {
                'fintech': 'انضم إلى برنامج حاضنة التكنولوجيا المالية اليوم!',
                'healthtech': 'حول الرعاية الصحية معنا!',
                'edtech': 'ثور التعليم معاً!'
            }
        }

        return ctas.get(language, ctas['en']).get(vertical, 'Join us today!')


class InfluencerMarketingStrategy:
    """
    Influencer marketing strategy for MENA region
    """

    def __init__(self):
        self.influencer_tiers = {
            'mega': {'followers': '1M+', 'budget_range': '10000-50000'},
            'macro': {'followers': '100K-1M', 'budget_range': '2000-10000'},
            'micro': {'followers': '10K-100K', 'budget_range': '500-2000'},
            'nano': {'followers': '1K-10K', 'budget_range': '100-500'}
        }

    def get_influencer_strategy(self, vertical: str, budget: int, language: str = 'en') -> Dict[str, Any]:
        """Get influencer marketing strategy for vertical"""

        strategy = {
            'vertical': vertical,
            'total_budget': budget,
            'language': language,
            'recommended_mix': self._get_influencer_mix(vertical, budget),
            'content_types': self._get_content_types(vertical),
            'campaign_objectives': self._get_campaign_objectives(vertical),
            'kpis': self._get_influencer_kpis(vertical),
            'timeline': self._get_campaign_timeline(),
            'selection_criteria': self._get_selection_criteria(vertical, language)
        }

        return strategy

    def _get_influencer_mix(self, vertical: str, budget: int) -> Dict[str, Any]:
        """Determine optimal influencer mix based on budget and vertical"""
        if budget < 5000:
            return {
                'micro': {'percentage': 70, 'count': 5, 'budget': budget * 0.7},
                'nano': {'percentage': 30, 'count': 10, 'budget': budget * 0.3}
            }
        elif budget < 20000:
            return {
                'macro': {'percentage': 40, 'count': 2, 'budget': budget * 0.4},
                'micro': {'percentage': 60, 'count': 8, 'budget': budget * 0.6}
            }
        else:
            return {
                'mega': {'percentage': 30, 'count': 1, 'budget': budget * 0.3},
                'macro': {'percentage': 40, 'count': 3, 'budget': budget * 0.4},
                'micro': {'percentage': 30, 'count': 6, 'budget': budget * 0.3}
            }

    def _get_content_types(self, vertical: str) -> List[Dict[str, Any]]:
        """Get recommended content types for vertical"""
        content_types = {
            'fintech': [
                {'type': 'Educational posts', 'percentage': 40},
                {'type': 'Success stories', 'percentage': 30},
                {'type': 'Product demos', 'percentage': 20},
                {'type': 'Industry insights', 'percentage': 10}
            ],
            'healthtech': [
                {'type': 'Health education', 'percentage': 50},
                {'type': 'Product testimonials', 'percentage': 25},
                {'type': 'Expert interviews', 'percentage': 15},
                {'type': 'Behind-the-scenes', 'percentage': 10}
            ],
            'edtech': [
                {'type': 'Educational content', 'percentage': 45},
                {'type': 'Student success stories', 'percentage': 25},
                {'type': 'Teacher testimonials', 'percentage': 20},
                {'type': 'Product tutorials', 'percentage': 10}
            ]
        }

        return content_types.get(vertical, content_types['fintech'])

    def _get_campaign_objectives(self, vertical: str) -> List[str]:
        """Get campaign objectives for vertical"""
        objectives = {
            'fintech': [
                'Increase brand awareness in FinTech community',
                'Generate qualified leads for incubator program',
                'Establish thought leadership in Islamic finance',
                'Drive traffic to application portal'
            ],
            'healthtech': [
                'Build trust in healthcare innovation',
                'Educate about digital health solutions',
                'Generate leads from healthcare professionals',
                'Increase program applications'
            ],
            'edtech': [
                'Raise awareness about educational innovation',
                'Engage with educator community',
                'Showcase student success stories',
                'Drive program enrollment'
            ]
        }

        return objectives.get(vertical, objectives['fintech'])

    def _get_influencer_kpis(self, vertical: str) -> Dict[str, Any]:
        """Get KPIs for influencer campaigns"""
        return {
            'reach_metrics': [
                'Total reach',
                'Unique impressions',
                'Audience overlap'
            ],
            'engagement_metrics': [
                'Engagement rate',
                'Comments quality',
                'Shares and saves',
                'Story completion rate'
            ],
            'conversion_metrics': [
                'Click-through rate',
                'Landing page visits',
                'Application submissions',
                'Cost per acquisition'
            ],
            'brand_metrics': [
                'Brand mention sentiment',
                'Share of voice',
                'Brand recall lift'
            ]
        }

    def _get_campaign_timeline(self) -> Dict[str, str]:
        """Get recommended campaign timeline"""
        return {
            'week_1': 'Influencer outreach and negotiation',
            'week_2': 'Content briefing and creation',
            'week_3-4': 'Content review and approval',
            'week_5-8': 'Campaign execution and monitoring',
            'week_9': 'Performance analysis and reporting'
        }

    def _get_selection_criteria(self, vertical: str, language: str) -> Dict[str, Any]:
        """Get influencer selection criteria"""
        criteria = {
            'audience_alignment': {
                'demographics': 'Target age group and income level',
                'interests': f'{vertical} and entrepreneurship',
                'location': 'MENA region focus'
            },
            'content_quality': {
                'authenticity': 'Genuine engagement with audience',
                'professionalism': 'High-quality content production',
                'consistency': 'Regular posting schedule'
            },
            'performance_metrics': {
                'engagement_rate': '>3% for macro, >5% for micro',
                'audience_growth': 'Steady, organic growth',
                'brand_safety': 'No controversial content'
            }
        }

        if language == 'ar':
            criteria['language_requirements'] = {
                'arabic_fluency': 'Native or fluent Arabic speaker',
                'cultural_understanding': 'Deep understanding of MENA culture',
                'bilingual_capability': 'Ability to create Arabic and English content'
            }

        return criteria


# Global instances
_digital_marketing = None
_influencer_strategy = None


def get_digital_marketing_automation() -> DigitalMarketingAutomation:
    """Get the global digital marketing automation instance"""
    global _digital_marketing
    if _digital_marketing is None:
        _digital_marketing = DigitalMarketingAutomation()
    return _digital_marketing


def get_influencer_marketing_strategy() -> InfluencerMarketingStrategy:
    """Get the global influencer marketing strategy instance"""
    global _influencer_strategy
    if _influencer_strategy is None:
        _influencer_strategy = InfluencerMarketingStrategy()
    return _influencer_strategy
