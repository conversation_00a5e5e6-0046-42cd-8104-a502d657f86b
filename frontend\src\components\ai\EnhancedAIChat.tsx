/**
 * ✅ ENHANCED AI CHAT COMPONENT
 * Modern, feature-rich AI chat with beautiful styling and advanced functionality
 */

import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import useReduxRoles from '../../hooks/useReduxRoles';
import { integratedAiApi } from '../../services/integratedAiApi';
import {
  Send, Bot, User, Loader2, AlertCircle, CheckCircle,
  Sparkles, Copy, ThumbsUp, ThumbsDown, RotateCcw,
  FileText, Settings, Zap, Brain,
  MessageSquare, Clock, Globe, Lightbulb
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';

interface Message {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  metadata?: any;
  reactions?: string[];
  isTyping?: boolean;
}

interface EnhancedAIChatProps {
  height?: string;
  className?: string;
  showFeatures?: boolean;
  onFeatureClick?: (feature: string) => void;
  onStatsUpdate?: (stats: { messageCount: number; sessionDuration: number; isConnected: boolean }) => void;
  featureMessage?: string;
  onFeatureMessageProcessed?: () => void;
  startNewChat?: boolean;
  onNewChatStarted?: () => void;
  chatType?: 'syrian_business' | 'damascus_local' | 'aleppo_local' | 'general'; // ✅ SYRIAN CHAT TYPE
  onChatTypeChange?: (type: 'syrian_business' | 'damascus_local' | 'aleppo_local' | 'general') => void; // ✅ CHAT TYPE CHANGE HANDLER
}

const EnhancedAIChat: React.FC<EnhancedAIChatProps> = ({
  height = 'h-full',
  className = '',
  showFeatures = true,
  onStatsUpdate,
  featureMessage,
  onFeatureMessageProcessed,
  startNewChat,
  onNewChatStarted,
  chatType = 'general', // ✅ SYRIAN CHAT TYPE
  onChatTypeChange // ✅ CHAT TYPE CHANGE HANDLER
}) => {
  const { t } = useTranslation();
  const { isRTL, language } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  const { primaryRole } = useReduxRoles();
  
  // Chat state
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error'>('connecting');

  // ✅ SESSION STATISTICS STATE
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);
  const [sessionDuration, setSessionDuration] = useState(0);
  const [messageCount, setMessageCount] = useState(0);
  
  // UI state
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [showSettings, setShowSettings] = useState(false);

  // AI Settings state
  const [selectedModel, setSelectedModel] = useState('ياسمين الذكي');
  const [temperature, setTemperature] = useState(0.7);
  const [responseLanguage, setResponseLanguage] = useState('auto');
  const [maxTokens, setMaxTokens] = useState(4000);
  const [enableThinking, setEnableThinking] = useState(true);

  // Settings modal state
  const [isLoadingSettings, setIsLoadingSettings] = useState(false);
  const [isSavingSettings, setIsSavingSettings] = useState(false);
  const [settingsError, setSettingsError] = useState<string | null>(null);
  const [availableModels, setAvailableModels] = useState<any[]>([]);
  
  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const settingsRef = useRef<HTMLDivElement>(null);

  // ✅ INITIALIZE CHAT ON MOUNT ONLY
  useEffect(() => {
    initializeChat();
  }, [primaryRole]); // ✅ ONLY RESTART WHEN ROLE CHANGES, NOT CHAT TYPE

  // ✅ LOAD SETTINGS WHEN MODAL OPENS
  useEffect(() => {
    if (showSettings) {
      loadAISettings();
    }
  }, [showSettings]);

  // ✅ CLICK OUTSIDE TO CLOSE SETTINGS
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setShowSettings(false);
      }
    };

    if (showSettings) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSettings]);

  // ✅ HANDLE CHAT TYPE CHANGES WITHOUT RECONNECTING
  useEffect(() => {
    if (connectionStatus === 'connected') {
      // Just update the welcome message when chat type changes
      const welcomeMessage: Message = {
        id: 'welcome-' + Date.now(),
        type: 'ai',
        content: getWelcomeMessage(),
        timestamp: new Date(),
        metadata: { isWelcome: true }
      };

      setMessages([welcomeMessage]);
    }
  }, [chatType]); // ✅ UPDATE WELCOME MESSAGE WHEN CHAT TYPE CHANGES

  // Auto scroll to bottom
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // ✅ OPTIMIZED SESSION TIMER (REDUCED FREQUENCY)
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (sessionStartTime) {
      interval = setInterval(() => {
        const now = new Date();
        const duration = Math.floor((now.getTime() - sessionStartTime.getTime()) / 1000 / 60); // minutes
        setSessionDuration(duration);

        // ✅ UPDATE PARENT COMPONENT WITH STATS (LESS FREQUENT)
        if (onStatsUpdate) {
          onStatsUpdate({
            messageCount,
            sessionDuration: duration,
            isConnected: connectionStatus === 'connected'
          });
        }
      }, 10000); // ✅ REDUCED FROM 1 SECOND TO 10 SECONDS
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [sessionStartTime, messageCount, connectionStatus, onStatsUpdate]);

  // ✅ HANDLE NEW CHAT TRIGGER FROM PARENT
  useEffect(() => {
    if (startNewChat) {
      startNewChatSession();

      // Notify parent that new chat was started
      if (onNewChatStarted) {
        onNewChatStarted();
      }
    }
  }, [startNewChat, onNewChatStarted]);

  // ✅ HANDLE FEATURE MESSAGE FROM PARENT (AFTER NEW CHAT)
  useEffect(() => {
    if (featureMessage && featureMessage.trim()) {
      console.log('🎯 Received feature message:', featureMessage);
      setInputMessage(featureMessage);
      inputRef.current?.focus();

      // Clear the feature message after processing
      if (onFeatureMessageProcessed) {
        onFeatureMessageProcessed();
      }
    }
  }, [featureMessage, onFeatureMessageProcessed]);

  const initializeChat = async () => {
    try {
      setConnectionStatus('connecting');
      
      // Test backend connection
      const status = await integratedAiApi.getStatus();
      
      if (status.is_available) {
        setConnectionStatus('connected');

        // ✅ START SESSION TIMER
        setSessionStartTime(new Date());

        // Add welcome message
        const welcomeMessage: Message = {
          id: Date.now().toString(),
          type: 'ai',
          content: getWelcomeMessage(),
          timestamp: new Date(),
          metadata: { isWelcome: true }
        };

        setMessages([welcomeMessage]);
        setMessageCount(1); // Count welcome message

        // ✅ NO SESSION MANAGEMENT NEEDED - DIRECT CHAT
        // Session will be handled automatically by the backend during chat
      } else {
        setConnectionStatus('error');
        addSystemMessage('❌ AI service is currently unavailable. Please try again later.');
      }
    } catch (error) {
      setConnectionStatus('error');
      addSystemMessage('❌ Failed to connect to AI service. Please check your connection.');
    }
  };

  const getWelcomeMessage = () => {
    // ✅ GET USER INFO FOR PERSONALIZED WELCOME
    const userName = user?.first_name || user?.username || 'صديقي';
    const userRole = primaryRole || 'user';

    // ✅ PERSONALIZED SYRIAN CONTEXT WELCOME MESSAGES
    const syrianWelcomeMessages = {
      'syrian_business': `أهلاً وسهلاً ${userName}! أنا ياسمين، مساعدك الذكي المتخصص في ريادة الأعمال السورية. أعرف أنك ${getRoleInArabic(userRole)} في منصة ياسمين للذكاء الاصطناعي وعلوم البيانات. يمكنني مساعدتك في تطوير مشاريعك وفهم السوق السوري والفرص الاستثمارية المحلية.`,
      'damascus_local': `مرحباً فيك ${userName}! أنا ياسمين، مساعدك الذكي من دمشق الشام. أعرف أنك مسجل في منصتنا كـ${getRoleInArabic(userRole)}. بعرف كل شي عن العاصمة وأحياؤها وأسواقها. يمكنني مساعدتك في أي شي يخص دمشق وريفها.`,
      'aleppo_local': `أهلين ${userName}! أنا ياسمين، مساعدك الذكي من حلب الشهباء. أعرف أنك ${getRoleInArabic(userRole)} في منصة ياسمين. بعرف كل تفاصيل المدينة وصناعاتها وتجارتها. يمكنني مساعدتك في كل ما يخص حلب وتراثها العريق.`,
      'general': `أهلاً وسهلاً ${userName}! أنا ياسمين، مساعدك الذكي السوري. أعرف أنك ${getRoleInArabic(userRole)} في منصة ياسمين للذكاء الاصطناعي وعلوم البيانات. لديّ معرفة واسعة بجميع المحافظات السورية وثقافتنا وتراثنا. يمكنني مساعدتك في أي موضوع يخص سوريا الحبيبة.`
    };

    return syrianWelcomeMessages[chatType] || syrianWelcomeMessages.general;
  };

  // ✅ GET ROLE IN ARABIC
  const getRoleInArabic = (role: string) => {
    const roleTranslations = {
      'user': 'مستخدم',
      'entrepreneur': 'رائد أعمال',
      'mentor': 'مرشد',
      'investor': 'مستثمر',
      'admin': 'مدير',
      'super_admin': 'مدير عام',
      'moderator': 'مشرف'
    };
    return roleTranslations[role as keyof typeof roleTranslations] || 'مستخدم';
  };

  const addSystemMessage = (content: string) => {
    const message: Message = {
      id: Date.now().toString(),
      type: 'system',
      content,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setIsTyping(true);
    setShowSuggestions(false);

    // ✅ UPDATE MESSAGE COUNT
    setMessageCount(prev => prev + 1);

    try {
      // ✅ SEND TO BACKEND WITH SYRIAN CONTEXT AND USER INFO
      const response = await integratedAiApi.chat(
        userMessage.content,
        chatType, // ✅ USE SYRIAN CHAT TYPE FROM PARENT
        'auto' // ✅ LET AI DETECT LANGUAGE FROM USER MESSAGE
      );

      // ✅ HANDLE ACTUAL BACKEND RESPONSE FORMAT
      if (response.success && response.response) {
        const aiMessage: Message = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: response.response,
          timestamp: new Date(),
          metadata: {
            session_id: response.session_id || 'direct-chat',
            interaction_id: response.interaction_id || Date.now().toString(),
            model: (response as any).model || 'ai-service'
          }
        };

        setMessages(prev => [...prev, aiMessage]);

        // ✅ UPDATE MESSAGE COUNT FOR AI RESPONSE
        setMessageCount(prev => prev + 1);
      } else {
        throw new Error((response as any).error || 'Failed to get AI response');
      }
    } catch (error) {
      console.error('Chat error:', error);
      addSystemMessage(`❌ خطأ: ${error instanceof Error ? error.message : 'فشل في إرسال الرسالة'}`);
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    // Could add toast notification here
  };

  // ✅ LOAD AI SETTINGS FROM BACKEND
  const loadAISettings = async () => {
    try {
      setIsLoadingSettings(true);
      setSettingsError(null);

      // Load available models
      const modelsResponse = await integratedAiApi.getAvailableModels({ latestOnly: true });
      if (modelsResponse.success) {
        setAvailableModels(modelsResponse.latest_models || []);
      }

      // Load current AI status/config
      const statusResponse = await integratedAiApi.getStatus();
      if (statusResponse.is_available) {
        // Set current model based on backend response
        if (statusResponse.model) {
          setSelectedModel(getModelDisplayName(statusResponse.model));
        }
      }

    } catch (error) {
      console.error('Failed to load AI settings:', error);
      setSettingsError('فشل في تحميل الإعدادات');
    } finally {
      setIsLoadingSettings(false);
    }
  };

  // ✅ SAVE AI SETTINGS TO BACKEND
  const saveAISettings = async () => {
    try {
      setIsSavingSettings(true);
      setSettingsError(null);

      // Here you would call backend API to save settings
      // For now, we'll simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Close modal on successful save
      setShowSettings(false);

      // You could add a success toast here
      console.log('Settings saved successfully');

    } catch (error) {
      console.error('Failed to save AI settings:', error);
      setSettingsError('فشل في حفظ الإعدادات');
    } finally {
      setIsSavingSettings(false);
    }
  };

  // ✅ GET DISPLAY NAME FOR MODEL
  const getModelDisplayName = (modelId: string) => {
    const modelMap: Record<string, string> = {
      'gemini-2.5-flash': 'ياسمين الذكي',
      'gemini-2.5-pro': 'ياسمين المتقدم',
      'gemini-2.0-flash': 'ياسمين السريع',
      'gemini-1.5-flash': 'ياسمين الإبداعي'
    };
    return modelMap[modelId] || 'ياسمين الذكي';
  };

  // ✅ GET SYRIAN CHAT TYPE LABEL
  const getChatTypeLabel = (type: string) => {
    const labels = {
      'syrian_business': '🏢 أعمال سورية',
      'damascus_local': '🏛️ دمشق المحلية',
      'aleppo_local': '🏭 حلب المحلية',
      'general': '🇸🇾 عام سوري'
    };
    return labels[type as keyof typeof labels] || '🇸🇾 عام سوري';
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // ✅ IMPROVED NEW CHAT SESSION FUNCTION
  const startNewChatSession = () => {
    console.log('🔄 Starting NEW CHAT session...');

    // Clear all messages and reset everything
    setMessages([]);
    setInputMessage('');
    setShowSuggestions(true);
    setIsLoading(false);
    setIsTyping(false);

    // Reset session statistics
    setSessionStartTime(new Date());
    setMessageCount(0);
    setSessionDuration(0);

    // Add fresh welcome message after a short delay
    setTimeout(() => {
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        type: 'ai',
        content: getWelcomeMessage(),
        timestamp: new Date(),
        metadata: { isWelcome: true }
      };

      setMessages([welcomeMessage]);
      setMessageCount(1);
      console.log('✅ NEW CHAT session started with fresh welcome message');
    }, 100);
  };

  // ✅ SYRIAN CONTEXT-SPECIFIC SUGGESTIONS
  const getSyrianSuggestions = () => {
    const syrianSuggestions = {
      'syrian_business': [
        { icon: Lightbulb, text: 'اقترح لي فكرة مشروع مناسبة للسوق السوري', category: 'business' },
        { icon: Brain, text: 'ما هي أفضل الفرص الاستثمارية في سوريا حالياً؟', category: 'business' },
        { icon: FileText, text: 'ساعدني في كتابة خطة عمل تناسب البيئة السورية', category: 'business' },
        { icon: MessageSquare, text: 'ما هي التحديات التي تواجه رواد الأعمال في سوريا؟', category: 'general' }
      ],
      'damascus_local': [
        { icon: Lightbulb, text: 'اقترح لي مشروع يناسب دمشق وأحياؤها', category: 'business' },
        { icon: Brain, text: 'وين أفضل الأماكن للاستثمار في دمشق؟', category: 'business' },
        { icon: FileText, text: 'حدثني عن أسواق دمشق التجارية', category: 'local' },
        { icon: MessageSquare, text: 'شو أهم المناطق التجارية في العاصمة؟', category: 'general' }
      ],
      'aleppo_local': [
        { icon: Lightbulb, text: 'اقترح لي مشروع يناسب حلب وصناعاتها', category: 'business' },
        { icon: Brain, text: 'شو أهم الصناعات التقليدية في حلب؟', category: 'business' },
        { icon: FileText, text: 'حدثني عن تجارة النسيج في حلب', category: 'local' },
        { icon: MessageSquare, text: 'كيف يمكنني الاستفادة من تراث حلب التجاري؟', category: 'general' }
      ],
      'general': [
        { icon: Lightbulb, text: 'اقترح لي فكرة مشروع يخدم المجتمع السوري', category: 'business' },
        { icon: Brain, text: 'ما هي أهم المحافظات السورية للاستثمار؟', category: 'business' },
        { icon: FileText, text: 'حدثني عن الثقافة السورية وتنوعها', category: 'culture' },
        { icon: MessageSquare, text: 'كيف يمكنني المساهمة في تطوير سوريا؟', category: 'general' }
      ]
    };

    return syrianSuggestions[chatType] || syrianSuggestions.general;
  };

  const suggestions = getSyrianSuggestions();

  return (
    <div className={`${height} ${className} flex flex-col bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900 rounded-xl shadow-2xl border border-purple-500/20`}>
      {/* ✅ RESPONSIVE HEADER */}
      <div className="flex items-center justify-between p-3 sm:p-4 border-b border-white/10 bg-white/5 backdrop-blur-sm rounded-t-xl">
        <div className={`flex items-center gap-2 sm:gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className="relative">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <Bot className="w-4 h-4 sm:w-6 sm:h-6 text-white" />
            </div>
            <div className={`absolute -bottom-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 rounded-full border-2 border-white ${
              connectionStatus === 'connected' ? 'bg-green-500' :
              connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
            }`} />
          </div>
          <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
            <h3 className="text-white font-semibold text-sm sm:text-base">ياسمين AI</h3>
            <div className="flex items-center gap-2">
              <span className="text-purple-200 text-xs sm:text-sm">
                {connectionStatus === 'connected' ? 'متصل' :
                 connectionStatus === 'connecting' ? 'جاري الاتصال...' : 'غير متصل'}
              </span>
              {connectionStatus === 'connected' && onChatTypeChange && (
                <>
                  <span className="text-purple-200 text-xs sm:text-sm">•</span>
                  <select
                    value={chatType}
                    onChange={(e) => onChatTypeChange(e.target.value as any)}
                    className="bg-white/10 border border-white/20 rounded-md px-2 py-1 text-purple-200 text-xs sm:text-sm backdrop-blur-sm focus:outline-none focus:ring-1 focus:ring-purple-400"
                  >
                    <option value="syrian_business">🏢 أعمال سورية</option>
                    <option value="damascus_local">🏛️ دمشق المحلية</option>
                    <option value="aleppo_local">🏭 حلب المحلية</option>
                    <option value="general">🇸🇾 عام سوري</option>
                  </select>
                </>
              )}
            </div>
          </div>
        </div>

        {showFeatures && (
          <div className={`flex items-center gap-1 sm:gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <button
              onClick={() => setShowSettings(true)}
              className="p-1 sm:p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <Settings className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
            </button>
          </div>
        )}
      </div>

      {/* ✅ RESPONSIVE MESSAGES */}
      <div className="flex-1 overflow-y-auto p-2 sm:p-4 space-y-3 sm:space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? (isRTL ? 'justify-start' : 'justify-end') : (isRTL ? 'justify-end' : 'justify-start')}`}
          >
            {/* ✅ RESPONSIVE MESSAGE BUBBLE */}
            <div className={`max-w-[85%] sm:max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
              <div className={`flex items-start gap-2 sm:gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                {/* Avatar */}
                <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  message.type === 'user'
                    ? 'bg-blue-500'
                    : message.type === 'system'
                    ? 'bg-red-500'
                    : 'bg-gradient-to-r from-purple-500 to-blue-500'
                }`}>
                  {message.type === 'user' ? (
                    <User className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                  ) : message.type === 'system' ? (
                    <AlertCircle className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                  ) : (
                    <Bot className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                  )}
                </div>

                {/* Message bubble */}
                <div className={`rounded-xl sm:rounded-2xl p-3 sm:p-4 ${
                  message.type === 'user'
                    ? 'bg-blue-500/20 border border-blue-400/30'
                    : message.type === 'system'
                    ? 'bg-red-500/20 border border-red-400/30'
                    : 'bg-white/10 border border-white/20 backdrop-blur-sm'
                }`}>
                  <div className={`text-white leading-relaxed text-sm sm:text-base ${isRTL ? 'text-right' : 'text-left'}`}>
                    {message.type === 'ai' ? (
                      <div className="prose prose-invert prose-sm max-w-none [&>*]:text-white [&>h1]:text-lg [&>h1]:font-bold [&>h1]:text-yellow-200 [&>h2]:text-base [&>h2]:font-bold [&>h2]:text-yellow-200 [&>h3]:text-sm [&>h3]:font-bold [&>h3]:text-yellow-200 [&>p]:mb-2 [&>p]:leading-relaxed [&>strong]:font-bold [&>strong]:text-yellow-200 [&>em]:italic [&>em]:text-blue-200 [&>ul]:list-disc [&>ul]:list-inside [&>ul]:mb-2 [&>ul]:space-y-1 [&>ol]:list-decimal [&>ol]:list-inside [&>ol]:mb-2 [&>ol]:space-y-1 [&>li]:text-white [&>code]:bg-gray-800 [&>code]:text-green-300 [&>code]:px-1 [&>code]:py-0.5 [&>code]:rounded [&>code]:text-xs [&>blockquote]:border-l-4 [&>blockquote]:border-purple-400 [&>blockquote]:pl-4 [&>blockquote]:italic [&>blockquote]:text-purple-200">
                        <ReactMarkdown>
                          {message.content}
                        </ReactMarkdown>
                      </div>
                    ) : (
                      message.content
                    )}
                  </div>

                  {/* Message footer */}
                  <div className={`flex items-center justify-between mt-2 text-xs text-gray-300 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-xs">{formatTimestamp(message.timestamp)}</span>

                    {message.type === 'ai' && (
                      <div className={`flex items-center gap-1 sm:gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <button
                          onClick={() => copyMessage(message.content)}
                          className="hover:text-white transition-colors p-1"
                          title="نسخ"
                        >
                          <Copy className="w-3 h-3" />
                        </button>
                        <button className="hover:text-white transition-colors p-1" title="إعجاب">
                          <ThumbsUp className="w-3 h-3" />
                        </button>
                        <button className="hover:text-white transition-colors p-1" title="عدم إعجاب">
                          <ThumbsDown className="w-3 h-3" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Typing indicator */}
        {isTyping && (
          <div className={`flex ${isRTL ? 'justify-end' : 'justify-start'}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse space-x-reverse space-x-3' : 'space-x-3'}`}>
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                <Bot className="w-4 h-4 text-white" />
              </div>
              <div className="bg-white/10 border border-white/20 rounded-2xl p-4 backdrop-blur-sm">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* ✅ RESPONSIVE SUGGESTIONS */}
      {showSuggestions && messages.length <= 1 && (
        <div className="p-3 sm:p-4 border-t border-white/10">
          <p className={`text-purple-200 text-xs sm:text-sm mb-2 sm:mb-3 ${isRTL ? 'text-right' : 'text-left'}`}>
            اقتراحات للبدء:
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => {
                  setInputMessage(suggestion.text);
                  inputRef.current?.focus();
                }}
                className={`flex items-center p-2 sm:p-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-all duration-200 ${isRTL ? 'flex-row-reverse text-right' : 'text-left'}`}
              >
                <suggestion.icon className={`w-3 h-3 sm:w-4 sm:h-4 text-purple-400 flex-shrink-0 ${isRTL ? 'ml-2 sm:ml-3' : 'mr-2 sm:mr-3'}`} />
                <span className="text-white text-xs sm:text-sm leading-tight">{suggestion.text}</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* ✅ IMPROVED RESPONSIVE INPUT */}
      <div className="p-3 sm:p-4 border-t border-white/10 bg-white/5 backdrop-blur-sm rounded-b-xl">
        <div className={`flex items-end gap-2 sm:gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className="flex-1 min-w-0">
            <textarea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={isRTL ? 'اكتب رسالتك هنا...' : 'Type your message...'}
              className={`w-full bg-white/10 border border-white/20 rounded-xl px-3 sm:px-4 py-2 sm:py-3 text-white placeholder-gray-400 resize-none backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all ${isRTL ? 'text-right' : 'text-left'}`}
              rows={1}
              style={{ minHeight: '40px', maxHeight: '120px' }}
              dir={isRTL ? 'rtl' : 'ltr'}
              disabled={isLoading || connectionStatus !== 'connected'}
            />
          </div>

          {/* ✅ RESPONSIVE SEND BUTTON */}
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading || connectionStatus !== 'connected'}
            className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed p-2 sm:p-3 rounded-xl transition-all duration-200 shadow-lg flex-shrink-0"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 text-white animate-spin" />
            ) : (
              <Send className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
            )}
          </button>
        </div>
      </div>

      {/* ✅ AI SETTINGS MODAL */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900 rounded-xl shadow-2xl border border-purple-500/20 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 className="text-xl font-bold text-white flex items-center gap-3">
                <Settings className="w-6 h-6 text-purple-400" />
                إعدادات الذكاء الاصطناعي
              </h2>
              <button
                onClick={() => setShowSettings(false)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <span className="text-white text-xl">×</span>
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 space-y-6">
              {/* Loading State */}
              {isLoadingSettings && (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-8 h-8 text-purple-400 animate-spin" />
                  <span className="ml-3 text-white">جاري تحميل الإعدادات...</span>
                </div>
              )}

              {/* Error State */}
              {settingsError && (
                <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="w-5 h-5 text-red-400" />
                    <span className="text-red-300">{settingsError}</span>
                  </div>
                </div>
              )}

              {/* Settings Form - Only show when not loading */}
              {!isLoadingSettings && (
                <>
                  {/* Chat Type Selection */}
                  <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  نوع المحادثة السورية
                </label>
                <select
                  value={chatType}
                  onChange={(e) => onChatTypeChange?.(e.target.value as any)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="syrian_business">🏢 أعمال سورية - ريادة الأعمال والاستثمار</option>
                  <option value="damascus_local">🏛️ دمشق المحلية - العاصمة وأحياؤها</option>
                  <option value="aleppo_local">🏭 حلب المحلية - العاصمة الاقتصادية</option>
                  <option value="general">🇸🇾 عام سوري - جميع المحافظات</option>
                </select>
              </div>

              {/* AI Model Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  نموذج الذكاء الاصطناعي
                </label>
                <select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="ياسمين الذكي">🤖 ياسمين الذكي (الافتراضي)</option>
                  <option value="ياسمين المتقدم">🧠 ياسمين المتقدم (تحليل عميق)</option>
                  <option value="ياسمين السريع">⚡ ياسمين السريع (استجابة فورية)</option>
                  <option value="ياسمين الإبداعي">🎨 ياسمين الإبداعي (حلول مبتكرة)</option>
                  {/* Dynamic models from backend */}
                  {availableModels.map((model) => (
                    <option key={model.id} value={getModelDisplayName(model.id)}>
                      {getModelDisplayName(model.id)} ({model.description})
                    </option>
                  ))}
                </select>
                {availableModels.length > 0 && (
                  <p className="text-xs text-gray-400 mt-2">
                    تم تحميل {availableModels.length} نموذج من الخادم
                  </p>
                )}
              </div>

              {/* Response Language */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  لغة الاستجابة
                </label>
                <select
                  value={responseLanguage}
                  onChange={(e) => setResponseLanguage(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="auto">🌐 تلقائي (حسب لغة السؤال)</option>
                  <option value="ar">🇸🇾 العربية</option>
                  <option value="en">🇺🇸 English</option>
                  <option value="mixed">🔄 مختلط (عربي وإنجليزي)</option>
                </select>
              </div>

              {/* Creativity Level */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  مستوى الإبداع والتنوع
                </label>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={temperature}
                    onChange={(e) => setTemperature(parseFloat(e.target.value))}
                    className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>محافظ (0.0)</span>
                    <span className="text-purple-300">متوازن ({temperature})</span>
                    <span>إبداعي (1.0)</span>
                  </div>
                </div>
              </div>

              {/* Response Length */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  طول الاستجابة
                </label>
                <select
                  value={maxTokens}
                  onChange={(e) => setMaxTokens(parseInt(e.target.value))}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value={1000}>📝 قصير (1000 كلمة)</option>
                  <option value={2000}>📄 متوسط (2000 كلمة)</option>
                  <option value={4000}>📋 طويل (4000 كلمة)</option>
                  <option value={8000}>📚 مفصل جداً (8000 كلمة)</option>
                </select>
              </div>

              {/* Advanced Features */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  الميزات المتقدمة
                </label>
                <div className="space-y-3">
                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={enableThinking}
                      onChange={(e) => setEnableThinking(e.target.checked)}
                      className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                    />
                    <span className="text-white">🧠 تفعيل التفكير العميق</span>
                  </label>
                </div>
              </div>
                </>
              )}
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-between p-6 border-t border-white/10">
              <button
                onClick={() => setShowSettings(false)}
                className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={saveAISettings}
                disabled={isSavingSettings}
                className="px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center gap-2"
              >
                {isSavingSettings ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    جاري الحفظ...
                  </>
                ) : (
                  'حفظ الإعدادات'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedAIChat;
