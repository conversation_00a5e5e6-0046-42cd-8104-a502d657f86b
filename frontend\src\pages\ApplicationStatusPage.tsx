/**
 * Application Status Page
 * Track application progress and status updates
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useLanguage } from '../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicProgress } from '../components/ui/ArabicOptimizedComponents';
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  FileText, 
  Users, 
  Calendar, 
  Mail,
  Phone,
  Download,
  Edit,
  RefreshCw
} from 'lucide-react';

interface ApplicationStatus {
  id: string;
  status: 'submitted' | 'under_review' | 'interview_scheduled' | 'interview_completed' | 'final_review' | 'accepted' | 'rejected' | 'waitlisted';
  submittedDate: string;
  lastUpdated: string;
  currentStage: string;
  nextStage?: string;
  estimatedCompletion?: string;
  feedback?: string;
  interviewDate?: string;
  interviewLink?: string;
  documents?: Array<{
    name: string;
    type: string;
    required: boolean;
    submitted: boolean;
    url?: string;
  }>;
}

interface StatusStep {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  status: 'completed' | 'current' | 'pending';
  date?: string;
  icon: React.ReactNode;
}

const ApplicationStatusPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const { applicationId } = useParams();
  const [applicationStatus, setApplicationStatus] = useState<ApplicationStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadApplicationStatus();
  }, [applicationId]);

  const loadApplicationStatus = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API call
      const mockStatus: ApplicationStatus = {
        id: applicationId || 'APP-1234567890',
        status: 'interview_scheduled',
        submittedDate: '2024-01-15',
        lastUpdated: '2024-01-22',
        currentStage: 'Initial Interview',
        nextStage: 'Final Evaluation',
        estimatedCompletion: '2024-02-15',
        interviewDate: '2024-01-25T14:00:00Z',
        interviewLink: 'https://meet.yasmeen-ai.com/interview/123',
        feedback: 'Your application shows great potential. We are impressed with your business model and market analysis.',
        documents: [
          {
            name: 'Business Plan',
            type: 'pdf',
            required: true,
            submitted: true,
            url: '/documents/business-plan.pdf'
          },
          {
            name: 'Financial Projections',
            type: 'xlsx',
            required: true,
            submitted: true,
            url: '/documents/financial-projections.xlsx'
          },
          {
            name: 'Pitch Deck',
            type: 'pdf',
            required: false,
            submitted: false
          }
        ]
      };
      
      setApplicationStatus(mockStatus);
    } catch (error) {
      console.error('Error loading application status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadApplicationStatus();
    setRefreshing(false);
  };

  const getStatusSteps = (status: ApplicationStatus): StatusStep[] => {
    const steps: StatusStep[] = [
      {
        id: 'submitted',
        title: 'Application Submitted',
        titleAr: 'تم إرسال الطلب',
        description: 'Your application has been successfully submitted',
        descriptionAr: 'تم إرسال طلبك بنجاح',
        status: 'completed',
        date: status.submittedDate,
        icon: <FileText className="w-5 h-5" />
      },
      {
        id: 'review',
        title: 'Under Review',
        titleAr: 'قيد المراجعة',
        description: 'Our team is reviewing your application',
        descriptionAr: 'فريقنا يراجع طلبك',
        status: ['submitted', 'under_review'].includes(status.status) ? 
          (status.status === 'under_review' ? 'current' : 'completed') : 'completed',
        icon: <Clock className="w-5 h-5" />
      },
      {
        id: 'interview',
        title: 'Interview',
        titleAr: 'المقابلة',
        description: 'Initial interview with our team',
        descriptionAr: 'مقابلة أولية مع فريقنا',
        status: ['interview_scheduled', 'interview_completed'].includes(status.status) ? 
          (status.status === 'interview_scheduled' ? 'current' : 'completed') : 
          (['submitted', 'under_review'].includes(status.status) ? 'pending' : 'completed'),
        date: status.interviewDate,
        icon: <Users className="w-5 h-5" />
      },
      {
        id: 'final_review',
        title: 'Final Review',
        titleAr: 'المراجعة النهائية',
        description: 'Final evaluation and decision',
        descriptionAr: 'التقييم النهائي والقرار',
        status: status.status === 'final_review' ? 'current' : 
          (['accepted', 'rejected', 'waitlisted'].includes(status.status) ? 'completed' : 'pending'),
        icon: <Calendar className="w-5 h-5" />
      },
      {
        id: 'decision',
        title: 'Decision',
        titleAr: 'القرار',
        description: 'Final decision notification',
        descriptionAr: 'إشعار القرار النهائي',
        status: ['accepted', 'rejected', 'waitlisted'].includes(status.status) ? 'completed' : 'pending',
        icon: <CheckCircle className="w-5 h-5" />
      }
    ];

    return steps;
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'accepted':
        return 'text-green-600 bg-green-100';
      case 'rejected':
        return 'text-red-600 bg-red-100';
      case 'waitlisted':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-blue-600 bg-blue-100';
    }
  };

  const getStatusText = (status: string): string => {
    const statusTexts = {
      en: {
        submitted: 'Submitted',
        under_review: 'Under Review',
        interview_scheduled: 'Interview Scheduled',
        interview_completed: 'Interview Completed',
        final_review: 'Final Review',
        accepted: 'Accepted',
        rejected: 'Rejected',
        waitlisted: 'Waitlisted'
      },
      ar: {
        submitted: 'تم الإرسال',
        under_review: 'قيد المراجعة',
        interview_scheduled: 'تم تحديد موعد المقابلة',
        interview_completed: 'تمت المقابلة',
        final_review: 'المراجعة النهائية',
        accepted: 'مقبول',
        rejected: 'مرفوض',
        waitlisted: 'قائمة الانتظار'
      }
    };

    return statusTexts[language][status as keyof typeof statusTexts.en] || status;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (!applicationStatus) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
            {language === 'ar' ? 'لم يتم العثور على الطلب' : 'Application Not Found'}
          </ArabicTypography>
          <ArabicTypography variant="body1" color="secondary" className="mb-4">
            {language === 'ar' 
              ? 'لم نتمكن من العثور على طلب بهذا الرقم'
              : 'We could not find an application with this ID'
            }
          </ArabicTypography>
          <ArabicButton onClick={() => navigate('/dashboard')}>
            {language === 'ar' ? 'العودة للوحة التحكم' : 'Back to Dashboard'}
          </ArabicButton>
        </div>
      </div>
    );
  }

  const statusSteps = getStatusSteps(applicationStatus);
  const currentStepIndex = statusSteps.findIndex(step => step.status === 'current');
  const progress = currentStepIndex >= 0 ? ((currentStepIndex + 1) / statusSteps.length) * 100 : 0;

  return (
    <div className={`min-h-screen bg-gray-50 py-8 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div>
              <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                {language === 'ar' ? 'حالة الطلب' : 'Application Status'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'رقم الطلب:' : 'Application ID:'} {applicationStatus.id}
              </ArabicTypography>
            </div>
            <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <div className={`px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(applicationStatus.status)}`}>
                {getStatusText(applicationStatus.status)}
              </div>
              <ArabicButton
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
                icon={<RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />}
              >
                {language === 'ar' ? 'تحديث' : 'Refresh'}
              </ArabicButton>
            </div>
          </div>
        </div>

        {/* Progress Overview */}
        <ArabicCard className="mb-8">
          <ArabicProgress
            value={progress}
            label={language === 'ar' ? 'تقدم الطلب' : 'Application Progress'}
            showPercentage={true}
            size="lg"
            color="blue"
            className="mb-6"
          />
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'تاريخ الإرسال' : 'Submitted'}
              </ArabicTypography>
              <ArabicTypography variant="body1" className="font-semibold">
                {new Date(applicationStatus.submittedDate).toLocaleDateString()}
              </ArabicTypography>
            </div>
            <div>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'آخر تحديث' : 'Last Updated'}
              </ArabicTypography>
              <ArabicTypography variant="body1" className="font-semibold">
                {new Date(applicationStatus.lastUpdated).toLocaleDateString()}
              </ArabicTypography>
            </div>
            <div>
              <ArabicTypography variant="body2" color="secondary">
                {language === 'ar' ? 'الإنجاز المتوقع' : 'Expected Completion'}
              </ArabicTypography>
              <ArabicTypography variant="body1" className="font-semibold">
                {applicationStatus.estimatedCompletion ? 
                  new Date(applicationStatus.estimatedCompletion).toLocaleDateString() : 
                  (language === 'ar' ? 'غير محدد' : 'TBD')
                }
              </ArabicTypography>
            </div>
          </div>
        </ArabicCard>

        {/* Status Timeline */}
        <ArabicCard 
          title={language === 'ar' ? 'مراحل الطلب' : 'Application Timeline'}
          className="mb-8"
        >
          <div className="space-y-6">
            {statusSteps.map((step, index) => (
              <div key={step.id} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                  step.status === 'completed' ? 'bg-green-100 text-green-600' :
                  step.status === 'current' ? 'bg-blue-100 text-blue-600' :
                  'bg-gray-100 text-gray-400'
                } ${isRTL ? 'ml-4' : 'mr-4'}`}>
                  {step.status === 'completed' ? <CheckCircle className="w-5 h-5" /> : step.icon}
                </div>
                
                <div className="flex-1">
                  <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <ArabicTypography variant="h5" className={`font-semibold ${
                      step.status === 'current' ? 'text-blue-600' : 'text-gray-900'
                    }`}>
                      {language === 'ar' ? step.titleAr : step.title}
                    </ArabicTypography>
                    {step.date && (
                      <ArabicTypography variant="body2" color="secondary">
                        {new Date(step.date).toLocaleDateString()}
                      </ArabicTypography>
                    )}
                  </div>
                  <ArabicTypography variant="body2" color="secondary" className="mt-1">
                    {language === 'ar' ? step.descriptionAr : step.description}
                  </ArabicTypography>
                  
                  {index < statusSteps.length - 1 && (
                    <div className={`mt-4 w-px h-6 ${
                      step.status === 'completed' ? 'bg-green-200' : 'bg-gray-200'
                    } ${isRTL ? 'mr-5' : 'ml-5'}`}></div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </ArabicCard>

        {/* Interview Information */}
        {applicationStatus.interviewDate && (
          <ArabicCard 
            title={language === 'ar' ? 'معلومات المقابلة' : 'Interview Information'}
            className="mb-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <ArabicTypography variant="body2" color="secondary" className="mb-1">
                  {language === 'ar' ? 'تاريخ ووقت المقابلة' : 'Interview Date & Time'}
                </ArabicTypography>
                <ArabicTypography variant="body1" className="font-semibold">
                  {new Date(applicationStatus.interviewDate).toLocaleString()}
                </ArabicTypography>
              </div>
              
              {applicationStatus.interviewLink && (
                <div>
                  <ArabicTypography variant="body2" color="secondary" className="mb-2">
                    {language === 'ar' ? 'رابط المقابلة' : 'Interview Link'}
                  </ArabicTypography>
                  <ArabicButton
                    size="sm"
                    onClick={() => window.open(applicationStatus.interviewLink, '_blank')}
                  >
                    {language === 'ar' ? 'انضم للمقابلة' : 'Join Interview'}
                  </ArabicButton>
                </div>
              )}
            </div>
          </ArabicCard>
        )}

        {/* Feedback */}
        {applicationStatus.feedback && (
          <ArabicCard 
            title={language === 'ar' ? 'ملاحظات الفريق' : 'Team Feedback'}
            className="mb-8"
          >
            <ArabicTypography variant="body1">
              {applicationStatus.feedback}
            </ArabicTypography>
          </ArabicCard>
        )}

        {/* Documents */}
        {applicationStatus.documents && applicationStatus.documents.length > 0 && (
          <ArabicCard 
            title={language === 'ar' ? 'المستندات' : 'Documents'}
            className="mb-8"
          >
            <div className="space-y-4">
              {applicationStatus.documents.map((doc, index) => (
                <div key={index} className={`flex items-center justify-between p-4 border rounded-lg ${
                  doc.submitted ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
                } ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <FileText className={`w-5 h-5 ${doc.submitted ? 'text-green-600' : 'text-gray-400'} ${isRTL ? 'ml-3' : 'mr-3'}`} />
                    <div>
                      <ArabicTypography variant="body1" className="font-semibold">
                        {doc.name}
                      </ArabicTypography>
                      <ArabicTypography variant="body2" color="secondary">
                        {doc.required ? 
                          (language === 'ar' ? 'مطلوب' : 'Required') : 
                          (language === 'ar' ? 'اختياري' : 'Optional')
                        } • {doc.submitted ? 
                          (language === 'ar' ? 'تم الإرسال' : 'Submitted') : 
                          (language === 'ar' ? 'لم يتم الإرسال' : 'Not Submitted')
                        }
                      </ArabicTypography>
                    </div>
                  </div>
                  
                  <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                    {doc.submitted && doc.url && (
                      <ArabicButton
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(doc.url, '_blank')}
                        icon={<Download className="w-4 h-4" />}
                      >
                        {language === 'ar' ? 'تحميل' : 'Download'}
                      </ArabicButton>
                    )}
                    {!doc.submitted && (
                      <ArabicButton
                        size="sm"
                        onClick={() => navigate(`/application/${applicationStatus.id}/upload/${doc.name}`)}
                        icon={<Edit className="w-4 h-4" />}
                      >
                        {language === 'ar' ? 'رفع' : 'Upload'}
                      </ArabicButton>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </ArabicCard>
        )}

        {/* Contact Information */}
        <ArabicCard 
          title={language === 'ar' ? 'هل تحتاج مساعدة؟' : 'Need Help?'}
        >
          <ArabicTypography variant="body1" color="secondary" className="mb-4">
            {language === 'ar' 
              ? 'إذا كان لديك أي أسئلة حول طلبك، لا تتردد في التواصل معنا'
              : 'If you have any questions about your application, feel free to contact us'
            }
          </ArabicTypography>
          
          <div className={`flex flex-col sm:flex-row gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
            <ArabicButton
              variant="outline"
              onClick={() => window.location.href = 'mailto:<EMAIL>'}
              icon={<Mail className="w-4 h-4" />}
            >
              {language === 'ar' ? 'إرسال بريد إلكتروني' : 'Send Email'}
            </ArabicButton>
            
            <ArabicButton
              variant="outline"
              onClick={() => window.location.href = 'tel:+966111234567'}
              icon={<Phone className="w-4 h-4" />}
            >
              {language === 'ar' ? 'اتصل بنا' : 'Call Us'}
            </ArabicButton>
          </div>
        </ArabicCard>
      </div>
    </div>
  );
};

export default ApplicationStatusPage;
