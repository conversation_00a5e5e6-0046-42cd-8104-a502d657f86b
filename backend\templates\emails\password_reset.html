<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Request - {{ site_name }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        h1 {
            color: #1a202c;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .subtitle {
            color: #718096;
            margin: 10px 0 0;
            font-size: 16px;
        }
        .content {
            margin: 30px 0;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2d3748;
        }
        .message {
            font-size: 16px;
            margin-bottom: 30px;
            color: #4a5568;
            line-height: 1.7;
        }
        .button-container {
            text-align: center;
            margin: 40px 0;
        }
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            transition: transform 0.2s;
        }
        .reset-button:hover {
            transform: translateY(-2px);
            text-decoration: none;
            color: white;
        }
        .alternative-link {
            margin-top: 30px;
            padding: 20px;
            background-color: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .alternative-link h3 {
            margin: 0 0 10px;
            color: #2d3748;
            font-size: 16px;
        }
        .alternative-link p {
            margin: 0;
            color: #718096;
            font-size: 14px;
            word-break: break-all;
        }
        .expiry-info {
            background-color: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 16px;
            margin: 30px 0;
        }
        .expiry-info h3 {
            margin: 0 0 8px;
            color: #c53030;
            font-size: 16px;
        }
        .expiry-info p {
            margin: 0;
            color: #742a2a;
            font-size: 14px;
        }
        .security-notice {
            background-color: #fffaf0;
            border: 1px solid #fbd38d;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .security-notice h3 {
            margin: 0 0 8px;
            color: #c05621;
            font-size: 16px;
        }
        .security-notice p {
            margin: 0;
            color: #744210;
            font-size: 14px;
        }
        .request-details {
            background-color: #f0f4f8;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
            font-size: 14px;
        }
        .request-details h3 {
            margin: 0 0 10px;
            color: #2d3748;
            font-size: 16px;
        }
        .request-details p {
            margin: 5px 0;
            color: #4a5568;
        }
        .footer {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #718096;
            font-size: 14px;
        }
        .footer p {
            margin: 5px 0;
        }
        .warning-notice {
            background-color: #fef5e7;
            border: 1px solid #f6ad55;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .warning-notice h3 {
            margin: 0 0 8px;
            color: #c05621;
            font-size: 16px;
        }
        .warning-notice p {
            margin: 0;
            color: #744210;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            h1 {
                font-size: 24px;
            }
            .reset-button {
                padding: 14px 28px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Y</div>
            <h1>Password Reset Request</h1>
            <p class="subtitle">Secure password reset for {{ site_name }}</p>
        </div>

        <div class="content">
            <div class="greeting">
                Hello {{ user.first_name|default:user.username }},
            </div>

            <div class="message">
                We received a request to reset the password for your {{ site_name }} account. If you made this request, click the button below to create a new password.
            </div>

            <div class="button-container">
                <a href="{{ reset_url }}" class="reset-button">
                    Reset My Password
                </a>
            </div>

            <div class="expiry-info">
                <h3>⏰ Time Sensitive</h3>
                <p>This password reset link will expire on {{ expires_at|date:"F j, Y" }} at {{ expires_at|time:"g:i A" }}. For your security, please reset your password promptly.</p>
            </div>

            <div class="request-details">
                <h3>📋 Request Details</h3>
                <p><strong>Email:</strong> {{ user.email }}</p>
                <p><strong>Request Time:</strong> {{ expires_at|date:"F j, Y g:i A" }}</p>
                {% if ip_address %}
                <p><strong>IP Address:</strong> {{ ip_address }}</p>
                {% endif %}
                {% if user_agent %}
                <p><strong>Device:</strong> {{ user_agent }}</p>
                {% endif %}
            </div>

            <div class="alternative-link">
                <h3>Can't click the button?</h3>
                <p>Copy and paste this link into your browser:</p>
                <p>{{ reset_url }}</p>
            </div>

            <div class="security-notice">
                <h3>🔒 Security Notice</h3>
                <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged. However, if you're concerned about your account security, please contact our support team immediately.</p>
            </div>

            <div class="warning-notice">
                <h3>⚠️ Important Security Tips</h3>
                <p>• Never share your password reset link with anyone<br>
                • Always verify the URL starts with our official domain<br>
                • Create a strong, unique password<br>
                • Consider enabling two-factor authentication</p>
            </div>
        </div>

        <div class="footer">
            <p><strong>{{ site_name }}</strong></p>
            <p>Syrian Community for Data Science and AI</p>
            <p>This is an automated security message, please do not reply to this email.</p>
            <p>If you need help, contact our support team.</p>
        </div>
    </div>
</body>
</html>
