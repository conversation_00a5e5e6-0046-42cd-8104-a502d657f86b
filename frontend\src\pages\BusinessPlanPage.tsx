/**
 * Business Plan Management Page
 * Create, edit, and manage business plans with AI assistance
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useLanguage } from '../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicProgress } from '../components/ui/ArabicOptimizedComponents';
import { 
  FileText, 
  Edit, 
  Save, 
  Download, 
  Share2, 
  Bot, 
  Eye,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  TrendingUp,
  DollarSign,
  Target
} from 'lucide-react';

interface BusinessPlan {
  id: string;
  title: string;
  description: string;
  industry: string;
  stage: string;
  lastModified: string;
  completionPercentage: number;
  sections: BusinessPlanSection[];
  collaborators: Array<{
    id: string;
    name: string;
    role: string;
    avatar?: string;
  }>;
  status: 'draft' | 'in_review' | 'approved' | 'needs_revision';
}

interface BusinessPlanSection {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  content: string;
  completed: boolean;
  aiAssisted: boolean;
  lastModified: string;
  icon: React.ReactNode;
  order: number;
}

const BusinessPlanPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const { planId } = useParams();
  const [businessPlan, setBusinessPlan] = useState<BusinessPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeSection, setActiveSection] = useState<string>('executive-summary');
  const [isEditing, setIsEditing] = useState(false);
  const [aiGenerating, setAiGenerating] = useState(false);

  useEffect(() => {
    loadBusinessPlan();
  }, [planId]);

  const loadBusinessPlan = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API call
      const mockPlan: BusinessPlan = {
        id: planId || 'bp-1234567890',
        title: 'AI-Powered FinTech Solution',
        description: 'Revolutionary financial technology platform for MENA region',
        industry: 'fintech',
        stage: 'mvp',
        lastModified: '2024-01-22T10:30:00Z',
        completionPercentage: 65,
        status: 'draft',
        collaborators: [
          {
            id: '1',
            name: 'Ahmed Al-Rashid',
            role: 'Founder & CEO'
          },
          {
            id: '2',
            name: 'Sarah Johnson',
            role: 'Co-Founder & CTO'
          }
        ],
        sections: [
          {
            id: 'executive-summary',
            title: 'Executive Summary',
            titleAr: 'الملخص التنفيذي',
            description: 'Overview of your business concept and key highlights',
            descriptionAr: 'نظرة عامة على مفهوم عملك والنقاط الرئيسية',
            content: 'Our AI-powered FinTech platform revolutionizes financial services in the MENA region...',
            completed: true,
            aiAssisted: true,
            lastModified: '2024-01-22T10:30:00Z',
            icon: <FileText className="w-5 h-5" />,
            order: 1
          },
          {
            id: 'market-analysis',
            title: 'Market Analysis',
            titleAr: 'تحليل السوق',
            description: 'Market size, trends, and competitive landscape',
            descriptionAr: 'حجم السوق والاتجاهات والمشهد التنافسي',
            content: 'The MENA FinTech market is valued at $2.5B and growing at 25% annually...',
            completed: true,
            aiAssisted: true,
            lastModified: '2024-01-21T15:20:00Z',
            icon: <TrendingUp className="w-5 h-5" />,
            order: 2
          },
          {
            id: 'business-model',
            title: 'Business Model',
            titleAr: 'نموذج العمل',
            description: 'Revenue streams, pricing strategy, and value proposition',
            descriptionAr: 'مصادر الإيرادات واستراتيجية التسعير وعرض القيمة',
            content: 'Our business model is based on subscription fees and transaction commissions...',
            completed: true,
            aiAssisted: false,
            lastModified: '2024-01-20T09:15:00Z',
            icon: <Target className="w-5 h-5" />,
            order: 3
          },
          {
            id: 'financial-projections',
            title: 'Financial Projections',
            titleAr: 'التوقعات المالية',
            description: 'Revenue forecasts, expenses, and funding requirements',
            descriptionAr: 'توقعات الإيرادات والمصروفات ومتطلبات التمويل',
            content: '',
            completed: false,
            aiAssisted: false,
            lastModified: '',
            icon: <DollarSign className="w-5 h-5" />,
            order: 4
          },
          {
            id: 'team',
            title: 'Team & Management',
            titleAr: 'الفريق والإدارة',
            description: 'Key team members, advisors, and organizational structure',
            descriptionAr: 'أعضاء الفريق الرئيسيون والمستشارون والهيكل التنظيمي',
            content: '',
            completed: false,
            aiAssisted: false,
            lastModified: '',
            icon: <Users className="w-5 h-5" />,
            order: 5
          }
        ]
      };
      
      setBusinessPlan(mockPlan);
    } catch (error) {
      console.error('Error loading business plan:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateWithAI = async (sectionId: string) => {
    setAiGenerating(true);
    try {
      // Mock AI generation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Update section with AI-generated content
      if (businessPlan) {
        const updatedSections = businessPlan.sections.map(section => {
          if (section.id === sectionId) {
            return {
              ...section,
              content: 'AI-generated content for ' + section.title + '...',
              completed: true,
              aiAssisted: true,
              lastModified: new Date().toISOString()
            };
          }
          return section;
        });
        
        setBusinessPlan({
          ...businessPlan,
          sections: updatedSections,
          completionPercentage: Math.round((updatedSections.filter(s => s.completed).length / updatedSections.length) * 100)
        });
      }
    } catch (error) {
      console.error('Error generating AI content:', error);
    } finally {
      setAiGenerating(false);
    }
  };

  const handleSave = async () => {
    try {
      // Save business plan - replace with actual API call
      console.log('Saving business plan:', businessPlan);
      
      // Mock save
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving business plan:', error);
    }
  };

  const handleExport = () => {
    // Export business plan as PDF
    console.log('Exporting business plan as PDF');
  };

  const handleShare = () => {
    // Share business plan with collaborators
    console.log('Sharing business plan');
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-100';
      case 'needs_revision':
        return 'text-red-600 bg-red-100';
      case 'in_review':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-blue-600 bg-blue-100';
    }
  };

  const getStatusText = (status: string): string => {
    const statusTexts = {
      en: {
        draft: 'Draft',
        in_review: 'In Review',
        approved: 'Approved',
        needs_revision: 'Needs Revision'
      },
      ar: {
        draft: 'مسودة',
        in_review: 'قيد المراجعة',
        approved: 'معتمد',
        needs_revision: 'يحتاج مراجعة'
      }
    };

    return statusTexts[language][status as keyof typeof statusTexts.en] || status;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (!businessPlan) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
            {language === 'ar' ? 'لم يتم العثور على خطة العمل' : 'Business Plan Not Found'}
          </ArabicTypography>
          <ArabicButton onClick={() => navigate('/dashboard/business-plans')}>
            {language === 'ar' ? 'العودة لخطط العمل' : 'Back to Business Plans'}
          </ArabicButton>
        </div>
      </div>
    );
  }

  const activeSection = businessPlan.sections.find(s => s.id === activeSection);

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div>
              <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                {businessPlan.title}
              </ArabicTypography>
              <div className={`flex items-center mt-2 space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
                <div className={`px-3 py-1 rounded-full text-sm font-semibold ${getStatusColor(businessPlan.status)}`}>
                  {getStatusText(businessPlan.status)}
                </div>
                <ArabicTypography variant="body2" color="secondary">
                  {language === 'ar' ? 'آخر تعديل:' : 'Last modified:'} {new Date(businessPlan.lastModified).toLocaleDateString()}
                </ArabicTypography>
              </div>
            </div>
            
            <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <ArabicButton
                variant="outline"
                onClick={handleShare}
                icon={<Share2 className="w-4 h-4" />}
              >
                {language === 'ar' ? 'مشاركة' : 'Share'}
              </ArabicButton>
              
              <ArabicButton
                variant="outline"
                onClick={handleExport}
                icon={<Download className="w-4 h-4" />}
              >
                {language === 'ar' ? 'تصدير PDF' : 'Export PDF'}
              </ArabicButton>
              
              <ArabicButton
                onClick={isEditing ? handleSave : () => setIsEditing(true)}
                icon={isEditing ? <Save className="w-4 h-4" /> : <Edit className="w-4 h-4" />}
              >
                {isEditing ? 
                  (language === 'ar' ? 'حفظ' : 'Save') : 
                  (language === 'ar' ? 'تعديل' : 'Edit')
                }
              </ArabicButton>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar - Sections Navigation */}
          <div className="lg:col-span-1">
            <ArabicCard>
              <div className="mb-6">
                <ArabicTypography variant="h4" className="text-gray-900 font-bold mb-2">
                  {language === 'ar' ? 'التقدم' : 'Progress'}
                </ArabicTypography>
                <ArabicProgress
                  value={businessPlan.completionPercentage}
                  showPercentage={true}
                  size="md"
                  color="blue"
                />
              </div>

              <div className="space-y-2">
                <ArabicTypography variant="h5" className="text-gray-900 font-semibold mb-4">
                  {language === 'ar' ? 'أقسام خطة العمل' : 'Business Plan Sections'}
                </ArabicTypography>
                
                {businessPlan.sections
                  .sort((a, b) => a.order - b.order)
                  .map((section) => (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full text-left p-3 rounded-lg transition-colors ${
                        activeSection === section.id 
                          ? 'bg-blue-50 border-blue-200 border' 
                          : 'hover:bg-gray-50'
                      } ${isRTL ? 'text-right' : 'text-left'}`}
                    >
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <div className={`text-blue-600 ${isRTL ? 'ml-3' : 'mr-3'}`}>
                          {section.icon}
                        </div>
                        <div className="flex-1">
                          <ArabicTypography variant="body2" className="font-semibold">
                            {language === 'ar' ? section.titleAr : section.title}
                          </ArabicTypography>
                          <div className={`flex items-center mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                            {section.completed ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : (
                              <Clock className="w-4 h-4 text-gray-400" />
                            )}
                            {section.aiAssisted && (
                              <Bot className={`w-4 h-4 text-purple-500 ${isRTL ? 'mr-1' : 'ml-1'}`} />
                            )}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
              </div>

              {/* Collaborators */}
              <div className="mt-8">
                <ArabicTypography variant="h5" className="text-gray-900 font-semibold mb-4">
                  {language === 'ar' ? 'المتعاونون' : 'Collaborators'}
                </ArabicTypography>
                <div className="space-y-2">
                  {businessPlan.collaborators.map((collaborator) => (
                    <div key={collaborator.id} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'}`}>
                        <span className="text-sm font-semibold text-blue-600">
                          {collaborator.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <ArabicTypography variant="body2" className="font-semibold">
                          {collaborator.name}
                        </ArabicTypography>
                        <ArabicTypography variant="caption" color="secondary">
                          {collaborator.role}
                        </ArabicTypography>
                      </div>
                    </div>
                  ))}
                  <ArabicButton
                    variant="ghost"
                    size="sm"
                    onClick={() => {/* Add collaborator */}}
                    icon={<Plus className="w-4 h-4" />}
                    className="w-full mt-2"
                  >
                    {language === 'ar' ? 'إضافة متعاون' : 'Add Collaborator'}
                  </ArabicButton>
                </div>
              </div>
            </ArabicCard>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeSection && (
              <ArabicCard>
                <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                      {language === 'ar' ? activeSection.titleAr : activeSection.title}
                    </ArabicTypography>
                    <ArabicTypography variant="body1" color="secondary">
                      {language === 'ar' ? activeSection.descriptionAr : activeSection.description}
                    </ArabicTypography>
                  </div>
                  
                  {!activeSection.completed && (
                    <ArabicButton
                      onClick={() => handleGenerateWithAI(activeSection.id)}
                      disabled={aiGenerating}
                      icon={<Bot className="w-4 h-4" />}
                      variant="outline"
                    >
                      {aiGenerating 
                        ? (language === 'ar' ? 'جاري الإنشاء...' : 'Generating...')
                        : (language === 'ar' ? 'إنشاء بالذكاء الاصطناعي' : 'Generate with AI')
                      }
                    </ArabicButton>
                  )}
                </div>

                {/* Content Editor */}
                <div className="min-h-96">
                  {isEditing ? (
                    <textarea
                      className="w-full h-96 p-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                      value={activeSection.content}
                      onChange={(e) => {
                        // Update section content
                        const updatedSections = businessPlan.sections.map(section => {
                          if (section.id === activeSection.id) {
                            return { ...section, content: e.target.value };
                          }
                          return section;
                        });
                        setBusinessPlan({ ...businessPlan, sections: updatedSections });
                      }}
                      placeholder={language === 'ar' ? 'اكتب محتوى هذا القسم...' : 'Write the content for this section...'}
                      dir={isRTL ? 'rtl' : 'ltr'}
                    />
                  ) : (
                    <div className="prose max-w-none font-arabic" dir={isRTL ? 'rtl' : 'ltr'}>
                      {activeSection.content ? (
                        <div className="whitespace-pre-wrap">{activeSection.content}</div>
                      ) : (
                        <div className="text-center py-12 text-gray-500">
                          <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                          <ArabicTypography variant="body1">
                            {language === 'ar' 
                              ? 'لم يتم إنشاء محتوى لهذا القسم بعد'
                              : 'No content has been created for this section yet'
                            }
                          </ArabicTypography>
                          <ArabicButton
                            className="mt-4"
                            onClick={() => handleGenerateWithAI(activeSection.id)}
                            icon={<Bot className="w-4 h-4" />}
                          >
                            {language === 'ar' ? 'إنشاء بالذكاء الاصطناعي' : 'Generate with AI'}
                          </ArabicButton>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Section Footer */}
                {activeSection.lastModified && (
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className={`flex items-center justify-between text-sm text-gray-500 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Clock className={`w-4 h-4 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <span className="font-arabic">
                          {language === 'ar' ? 'آخر تعديل:' : 'Last modified:'} {new Date(activeSection.lastModified).toLocaleString()}
                        </span>
                      </div>
                      {activeSection.aiAssisted && (
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <Bot className={`w-4 h-4 text-purple-500 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                          <span className="font-arabic">
                            {language === 'ar' ? 'تم إنشاؤه بالذكاء الاصطناعي' : 'AI Generated'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </ArabicCard>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessPlanPage;
