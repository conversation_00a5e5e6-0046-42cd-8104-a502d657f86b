"""
Partnership URLs
URL configuration for partnership strategy and network development APIs
"""

from django.urls import path
from .partnership_api import (
    get_partnership_strategy_api,
    create_partnership_plan_api,
    get_network_development_plan_api,
    get_partnership_metrics_api,
    optimize_partnership_strategy_api,
    get_partnership_opportunities_api
)

app_name = 'partnerships'

urlpatterns = [
    # ========================================
    # PARTNERSHIP STRATEGY ENDPOINTS
    # ========================================
    
    # Get partnership strategy for specific type and vertical
    path('strategy/', get_partnership_strategy_api, name='get_partnership_strategy'),
    
    # Create comprehensive partnership development plan
    path('plans/create/', create_partnership_plan_api, name='create_partnership_plan'),
    
    # Get network development plan
    path('network/plan/', get_network_development_plan_api, name='get_network_development_plan'),
    
    # ========================================
    # PARTNERSHIP ANALYTICS ENDPOINTS
    # ========================================
    
    # Get partnership performance metrics
    path('metrics/', get_partnership_metrics_api, name='get_partnership_metrics'),
    
    # Get partnership strategy optimization recommendations
    path('optimize/', optimize_partnership_strategy_api, name='optimize_partnership_strategy'),
    
    # Get current partnership opportunities
    path('opportunities/', get_partnership_opportunities_api, name='get_partnership_opportunities'),
]
