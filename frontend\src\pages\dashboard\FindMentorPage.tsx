/**
 * Find Mentor Page
 * Search and connect with mentors
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { 
  Users, 
  Search, 
  Filter, 
  Star,
  MapPin,
  Clock,
  MessageCircle,
  Calendar,
  Award,
  Briefcase,
  Heart,
  CheckCircle
} from 'lucide-react';

interface Mentor {
  id: number;
  name: string;
  title: string;
  company: string;
  location: string;
  rating: number;
  reviewCount: number;
  expertise: string[];
  experience: string;
  hourlyRate: number;
  availability: string;
  profileImage: string;
  bio: string;
  isVerified: boolean;
}

const FindMentorPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [mentors, setMentors] = useState<Mentor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [expertiseFilter, setExpertiseFilter] = useState<string>('all');
  const [ratingFilter, setRatingFilter] = useState<number>(0);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockMentors: Mentor[] = [
      {
        id: 1,
        name: 'Sarah Johnson',
        title: 'Senior Product Manager',
        company: 'Tech Innovations Inc.',
        location: 'San Francisco, CA',
        rating: 4.9,
        reviewCount: 127,
        expertise: ['Product Management', 'Strategy', 'Leadership'],
        experience: '8+ years',
        hourlyRate: 150,
        availability: 'Available this week',
        profileImage: '/api/placeholder/64/64',
        bio: 'Experienced product manager with a track record of launching successful products.',
        isVerified: true
      },
      {
        id: 2,
        name: 'Michael Chen',
        title: 'Startup Founder & CEO',
        company: 'GreenTech Solutions',
        location: 'Austin, TX',
        rating: 4.8,
        reviewCount: 89,
        expertise: ['Entrepreneurship', 'Fundraising', 'Business Development'],
        experience: '12+ years',
        hourlyRate: 200,
        availability: 'Available next week',
        profileImage: '/api/placeholder/64/64',
        bio: 'Serial entrepreneur with 3 successful exits. Passionate about sustainable technology.',
        isVerified: true
      },
      {
        id: 3,
        name: 'Emily Rodriguez',
        title: 'Marketing Director',
        company: 'Digital Growth Agency',
        location: 'New York, NY',
        rating: 4.7,
        reviewCount: 156,
        expertise: ['Digital Marketing', 'Brand Strategy', 'Growth Hacking'],
        experience: '6+ years',
        hourlyRate: 120,
        availability: 'Available today',
        profileImage: '/api/placeholder/64/64',
        bio: 'Digital marketing expert specializing in startup growth and brand building.',
        isVerified: false
      }
    ];

    setTimeout(() => {
      setMentors(mockMentors);
      setIsLoading(false);
    }, 1000);
  }, []);

  const filteredMentors = mentors.filter(mentor => {
    const matchesSearch = mentor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         mentor.expertise.some(exp => exp.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         mentor.company.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesExpertise = expertiseFilter === 'all' || mentor.expertise.includes(expertiseFilter);
    const matchesRating = mentor.rating >= ratingFilter;
    return matchesSearch && matchesExpertise && matchesRating;
  });

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        size={16}
        className={index < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  const handleConnectMentor = (mentorId: number) => {
    // TODO: Implement mentor connection logic
    console.log('Connecting to mentor:', mentorId);
    navigate(`/dashboard/mentorship/connect/${mentorId}`);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading', 'Loading...')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Users className={`text-blue-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={32} />
            {t('mentorship.findMentor.title', 'Find a Mentor')}
          </h1>
          <p className="text-gray-600 mt-2">
            {t('mentorship.findMentor.subtitle', 'Connect with experienced professionals to accelerate your growth')}
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder={t('mentorship.findMentor.search', 'Search mentors by name, expertise, or company...')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex items-center">
                <Filter className="text-gray-400 mr-2" size={20} />
                <select
                  value={expertiseFilter}
                  onChange={(e) => setExpertiseFilter(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">{t('mentorship.findMentor.allExpertise', 'All Expertise')}</option>
                  <option value="Product Management">{t('mentorship.expertise.productManagement', 'Product Management')}</option>
                  <option value="Entrepreneurship">{t('mentorship.expertise.entrepreneurship', 'Entrepreneurship')}</option>
                  <option value="Digital Marketing">{t('mentorship.expertise.digitalMarketing', 'Digital Marketing')}</option>
                  <option value="Strategy">{t('mentorship.expertise.strategy', 'Strategy')}</option>
                  <option value="Leadership">{t('mentorship.expertise.leadership', 'Leadership')}</option>
                </select>
              </div>
              
              <div className="flex items-center">
                <Star className="text-gray-400 mr-2" size={20} />
                <select
                  value={ratingFilter}
                  onChange={(e) => setRatingFilter(Number(e.target.value))}
                  className="border border-gray-300 rounded-lg px-3 py-3 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={0}>{t('mentorship.findMentor.anyRating', 'Any Rating')}</option>
                  <option value={4}>{t('mentorship.findMentor.rating4Plus', '4+ Stars')}</option>
                  <option value={4.5}>{t('mentorship.findMentor.rating45Plus', '4.5+ Stars')}</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Mentors Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredMentors.length === 0 ? (
            <div className="col-span-full bg-white rounded-lg shadow-sm p-12 text-center">
              <Users className="mx-auto text-gray-400 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('mentorship.findMentor.noMentors', 'No mentors found')}
              </h3>
              <p className="text-gray-600">
                {t('mentorship.findMentor.noMentorsDesc', 'Try adjusting your search criteria')}
              </p>
            </div>
          ) : (
            filteredMentors.map((mentor) => (
              <div key={mentor.id} className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start mb-4">
                  <img
                    src={mentor.profileImage}
                    alt={mentor.name}
                    className="w-16 h-16 rounded-full object-cover mr-4"
                  />
                  <div className="flex-1">
                    <div className="flex items-center mb-1">
                      <h3 className="text-lg font-semibold text-gray-900">{mentor.name}</h3>
                      {mentor.isVerified && (
                        <CheckCircle className="text-blue-500 ml-2" size={16} />
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{mentor.title}</p>
                    <p className="text-sm text-gray-500">{mentor.company}</p>
                  </div>
                </div>

                <div className="flex items-center mb-3">
                  <div className="flex items-center mr-4">
                    {renderStars(mentor.rating)}
                    <span className="text-sm text-gray-600 ml-1">
                      {mentor.rating} ({mentor.reviewCount})
                    </span>
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-4 line-clamp-2">{mentor.bio}</p>

                <div className="flex flex-wrap gap-2 mb-4">
                  {mentor.expertise.slice(0, 3).map((skill, index) => (
                    <span
                      key={index}
                      className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {skill}
                    </span>
                  ))}
                </div>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center">
                    <MapPin size={14} className="mr-1" />
                    <span>{mentor.location}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock size={14} className="mr-1" />
                    <span>{mentor.availability}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-lg font-semibold text-gray-900">
                    ${mentor.hourlyRate}/hr
                  </div>
                  <button
                    onClick={() => handleConnectMentor(mentor.id)}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    <MessageCircle size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('mentorship.findMentor.connect', 'Connect')}
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default FindMentorPage;
