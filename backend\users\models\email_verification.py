"""
Email Verification Models
Handles email confirmation tokens and verification status
"""

import uuid
from datetime import timedelta
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils.html import strip_tags


class EmailVerificationToken(models.Model):
    """
    Model to store email verification tokens
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='email_verification_tokens')
    token = models.UUIDField(default=uuid.uuid4, unique=True, editable=False)
    email = models.EmailField()
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    used_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'email_verification_tokens'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['user', 'is_used']),
            models.Index(fields=['expires_at']),
        ]
    
    def save(self, *args, **kwargs):
        if not self.expires_at:
            # Token expires in 24 hours
            self.expires_at = timezone.now() + timedelta(hours=24)
        super().save(*args, **kwargs)
    
    @property
    def is_expired(self):
        """Check if the token has expired"""
        return timezone.now() > self.expires_at
    
    @property
    def is_valid(self):
        """Check if the token is valid (not used and not expired)"""
        return not self.is_used and not self.is_expired
    
    def mark_as_used(self):
        """Mark the token as used"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save()
    
    def send_verification_email(self, request=None):
        """
        Send verification email to the user
        """
        try:
            # Build verification URL
            if request:
                domain = request.get_host()
                protocol = 'https' if request.is_secure() else 'http'
            else:
                domain = getattr(settings, 'FRONTEND_DOMAIN', 'localhost:3003')
                protocol = 'https' if 'localhost' not in domain else 'http'
            
            verification_url = f"{protocol}://{domain}/verify-email/{self.token}"
            
            # Prepare email context
            context = {
                'user': self.user,
                'verification_url': verification_url,
                'token': self.token,
                'expires_at': self.expires_at,
                'site_name': getattr(settings, 'SITE_NAME', 'Yasmeen AI'),
            }
            
            # Render email templates
            html_message = render_to_string('emails/email_verification.html', context)
            plain_message = strip_tags(html_message)
            
            # Send email
            send_mail(
                subject=f'Verify your email address - {context["site_name"]}',
                message=plain_message,
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=[self.email],
                html_message=html_message,
                fail_silently=False,
            )
            
            return True
            
        except Exception as e:
            print(f"Failed to send verification email: {e}")
            return False
    
    @classmethod
    def create_for_user(cls, user, email=None):
        """
        Create a new verification token for a user
        """
        # Use user's email if not provided
        if not email:
            email = user.email
        
        # Invalidate any existing tokens for this user and email
        cls.objects.filter(
            user=user,
            email=email,
            is_used=False
        ).update(is_used=True, used_at=timezone.now())
        
        # Create new token
        token = cls.objects.create(
            user=user,
            email=email
        )
        
        return token
    
    @classmethod
    def verify_token(cls, token_uuid):
        """
        Verify a token and mark user's email as verified
        """
        try:
            token = cls.objects.get(token=token_uuid)
            
            if not token.is_valid:
                return False, "Token is invalid or expired"
            
            # Mark token as used
            token.mark_as_used()
            
            # Update user's email verification status
            user = token.user
            if hasattr(user, 'profile'):
                user.profile.email_verified = True
                user.profile.email_verified_at = timezone.now()
                user.profile.save()
            
            # If the token email is different from user's current email, update it
            if user.email != token.email:
                user.email = token.email
                user.save()
            
            return True, "Email verified successfully"
            
        except cls.DoesNotExist:
            return False, "Invalid token"
        except Exception as e:
            return False, f"Verification failed: {str(e)}"
    
    @classmethod
    def cleanup_expired_tokens(cls):
        """
        Clean up expired tokens (should be run periodically)
        """
        expired_count = cls.objects.filter(
            expires_at__lt=timezone.now(),
            is_used=False
        ).update(is_used=True, used_at=timezone.now())
        
        return expired_count
    
    def __str__(self):
        return f"Email verification for {self.user.username} ({self.email})"


class EmailVerificationSettings(models.Model):
    """
    Global settings for email verification
    """
    require_verification = models.BooleanField(
        default=True,
        help_text="Require email verification for new registrations"
    )
    token_expiry_hours = models.PositiveIntegerField(
        default=24,
        help_text="Hours until verification token expires"
    )
    max_resend_attempts = models.PositiveIntegerField(
        default=3,
        help_text="Maximum number of verification emails that can be sent per day"
    )
    resend_cooldown_minutes = models.PositiveIntegerField(
        default=5,
        help_text="Minutes to wait between resending verification emails"
    )
    
    class Meta:
        db_table = 'email_verification_settings'
        verbose_name = "Email Verification Settings"
        verbose_name_plural = "Email Verification Settings"
    
    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and EmailVerificationSettings.objects.exists():
            raise ValueError("Only one EmailVerificationSettings instance is allowed")
        super().save(*args, **kwargs)
    
    @classmethod
    def get_settings(cls):
        """Get the current settings, creating default if none exist"""
        settings, created = cls.objects.get_or_create(
            pk=1,
            defaults={
                'require_verification': True,
                'token_expiry_hours': 24,
                'max_resend_attempts': 3,
                'resend_cooldown_minutes': 5,
            }
        )
        return settings
    
    def __str__(self):
        return "Email Verification Settings"


class EmailVerificationAttempt(models.Model):
    """
    Track email verification attempts for rate limiting
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='verification_attempts')
    email = models.EmailField()
    attempted_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    success = models.BooleanField(default=False)
    
    class Meta:
        db_table = 'email_verification_attempts'
        ordering = ['-attempted_at']
        indexes = [
            models.Index(fields=['user', 'attempted_at']),
            models.Index(fields=['email', 'attempted_at']),
            models.Index(fields=['ip_address', 'attempted_at']),
        ]
    
    @classmethod
    def can_send_verification(cls, user, email):
        """
        Check if user can send another verification email
        """
        settings = EmailVerificationSettings.get_settings()
        
        # Check attempts in the last 24 hours
        yesterday = timezone.now() - timedelta(hours=24)
        recent_attempts = cls.objects.filter(
            user=user,
            email=email,
            attempted_at__gte=yesterday
        ).count()
        
        if recent_attempts >= settings.max_resend_attempts:
            return False, f"Maximum {settings.max_resend_attempts} attempts per day exceeded"
        
        # Check cooldown period
        cooldown_time = timezone.now() - timedelta(minutes=settings.resend_cooldown_minutes)
        recent_attempt = cls.objects.filter(
            user=user,
            email=email,
            attempted_at__gte=cooldown_time
        ).first()
        
        if recent_attempt:
            return False, f"Please wait {settings.resend_cooldown_minutes} minutes before requesting another email"
        
        return True, "Can send verification email"
    
    @classmethod
    def record_attempt(cls, user, email, success=False, ip_address=None, user_agent=None):
        """
        Record a verification attempt
        """
        return cls.objects.create(
            user=user,
            email=email,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def __str__(self):
        return f"Verification attempt for {self.user.username} ({self.email}) at {self.attempted_at}"
