/**
 * Funding Applications Page
 * View and manage funding applications
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { 
  DollarSign, 
  Plus, 
  Search, 
  Filter, 
  Calendar,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  FileText,
  Eye,
  Edit
} from 'lucide-react';

interface FundingApplication {
  id: number;
  title: string;
  amount: number;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  submittedDate: string;
  fundingType: string;
  description: string;
}

const FundingApplicationsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [applications, setApplications] = useState<FundingApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockApplications: FundingApplication[] = [
      {
        id: 1,
        title: 'Tech Startup Seed Funding',
        amount: 50000,
        status: 'pending',
        submittedDate: '2024-01-15',
        fundingType: 'Seed Round',
        description: 'Funding for AI-powered business analytics platform'
      },
      {
        id: 2,
        title: 'Green Energy Initiative',
        amount: 100000,
        status: 'approved',
        submittedDate: '2024-01-10',
        fundingType: 'Series A',
        description: 'Renewable energy solutions for small businesses'
      },
      {
        id: 3,
        title: 'Healthcare Innovation Grant',
        amount: 25000,
        status: 'under_review',
        submittedDate: '2024-01-20',
        fundingType: 'Grant',
        description: 'Digital health monitoring system'
      }
    ];

    setTimeout(() => {
      setApplications(mockApplications);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="text-green-500" size={20} />;
      case 'rejected':
        return <XCircle className="text-red-500" size={20} />;
      case 'under_review':
        return <AlertCircle className="text-yellow-500" size={20} />;
      default:
        return <Clock className="text-blue-500" size={20} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         app.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading', 'Loading...')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <DollarSign className={`text-green-500 ${isRTL ? 'ml-3' : 'mr-3'}`} size={32} />
                {t('funding.applications.title', 'Funding Applications')}
              </h1>
              <p className="text-gray-600 mt-2">
                {t('funding.applications.subtitle', 'Track and manage your funding applications')}
              </p>
            </div>
            
            <button
              onClick={() => navigate('/dashboard/funding')}
              className="flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Plus size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
              {t('funding.applications.new', 'New Application')}
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder={t('funding.applications.search', 'Search applications...')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <Filter className="text-gray-400 mr-2" size={20} />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">{t('funding.applications.allStatuses', 'All Statuses')}</option>
                  <option value="pending">{t('funding.applications.pending', 'Pending')}</option>
                  <option value="under_review">{t('funding.applications.underReview', 'Under Review')}</option>
                  <option value="approved">{t('funding.applications.approved', 'Approved')}</option>
                  <option value="rejected">{t('funding.applications.rejected', 'Rejected')}</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Applications List */}
        <div className="space-y-6">
          {filteredApplications.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm p-12 text-center">
              <DollarSign className="mx-auto text-gray-400 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {t('funding.applications.noApplications', 'No funding applications found')}
              </h3>
              <p className="text-gray-600 mb-6">
                {t('funding.applications.noApplicationsDesc', 'Start by creating your first funding application')}
              </p>
              <button
                onClick={() => navigate('/dashboard/funding')}
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Plus size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('funding.applications.createFirst', 'Create First Application')}
              </button>
            </div>
          ) : (
            filteredApplications.map((application) => (
              <div key={application.id} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 mr-3">
                        {application.title}
                      </h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                        {getStatusIcon(application.status)}
                        <span className="ml-1">
                          {t(`funding.applications.status.${application.status}`, application.status)}
                        </span>
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{application.description}</p>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <div className="flex items-center">
                        <DollarSign size={16} className="mr-1" />
                        <span className="font-medium text-gray-900">
                          {formatCurrency(application.amount)}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <FileText size={16} className="mr-1" />
                        <span>{application.fundingType}</span>
                      </div>
                      <div className="flex items-center">
                        <Calendar size={16} className="mr-1" />
                        <span>{formatDate(application.submittedDate)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => navigate(`/dashboard/funding/applications/${application.id}`)}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
                      title={t('common.view', 'View')}
                    >
                      <Eye size={20} />
                    </button>
                    <button
                      onClick={() => navigate(`/dashboard/funding/applications/${application.id}/edit`)}
                      className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg"
                      title={t('common.edit', 'Edit')}
                    >
                      <Edit size={20} />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default FundingApplicationsPage;
