/**
 * SIMPLE WORKING CHAT COMPONENT
 * A fallback chat component that works without backend dependencies
 * Includes proper styling and role-based responses
 */

import React, { useState, useEffect, useRef } from 'react';
import { Send, Bot, User, Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import useDatabaseRoles from '../../hooks/useDatabaseRoles';

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface SimpleWorkingChatProps {
  height?: string;
  className?: string;
}

const SimpleWorkingChat: React.FC<SimpleWorkingChatProps> = ({
  height = 'h-96',
  className = ''
}) => {
  const { t } = useTranslation();
  const {
    primaryRole,
    displayName,
    permissionLevel
  } = useDatabaseRoles();
  
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Add welcome message on mount
  useEffect(() => {
    const welcomeMessage = getWelcomeMessage(primaryRole, displayName);
    addMessage('ai', welcomeMessage);
  }, [primaryRole, displayName]);

  const addMessage = (type: 'user' | 'ai', content: string) => {
    const message: Message = {
      id: Date.now().toString(),
      type,
      content,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
  };

  const getWelcomeMessage = (role: string, roleDisplayName: string): string => {
    const welcomeMessages = {
      entrepreneur: `Hello ${roleDisplayName}! 🚀 I'm Yasmeen, your AI business assistant. I can help you with business planning, market analysis, and growth strategies. What would you like to work on today?`,
      mentor: `Welcome ${roleDisplayName}! 👨‍🏫 I'm here to assist you with mentorship strategies, guidance frameworks, and helping your mentees succeed. How can I support your mentoring efforts?`,
      investor: `Hi ${roleDisplayName}! 💰 I can help you analyze investment opportunities, market trends, and due diligence processes. What investment topic interests you?`,
      admin: `Hello ${roleDisplayName}! ⚙️ I'm here to assist with system management, user analytics, and platform optimization. What administrative task can I help with?`,
      super_admin: `Welcome ${roleDisplayName}! 🔧 I have full access to help with any administrative tasks, system configuration, and advanced analytics. How can I assist you today?`,
      moderator: `Hi ${roleDisplayName}! 🛡️ I can help you with community management, content moderation, and user behavior analysis. What moderation challenge can I help with?`,
      user: `Hello! 👋 I'm Yasmeen, your AI assistant. I can help you explore business ideas, learn about entrepreneurship, and discover opportunities. What interests you?`
    };
    
    return welcomeMessages[role as keyof typeof welcomeMessages] || welcomeMessages.user;
  };

  const generateResponse = (message: string): string => {
    const lowerMessage = message.toLowerCase();
    
    // Role-specific intelligent responses
    if (primaryRole === 'entrepreneur') {
      if (lowerMessage.includes('business') || lowerMessage.includes('startup')) {
        return "Great question about business! 🚀 For entrepreneurs, I recommend focusing on these key areas: 1) Market validation - talk to potential customers, 2) MVP development - build the simplest version that solves the problem, 3) Business model - how will you make money? What specific aspect would you like to dive deeper into?";
      }
      if (lowerMessage.includes('funding') || lowerMessage.includes('investment')) {
        return "Funding is crucial for growth! 💰 Consider this progression: 1) Bootstrap with personal savings, 2) Friends & family round, 3) Angel investors, 4) Venture capital. Each stage requires different preparation. What stage are you currently at?";
      }
      if (lowerMessage.includes('marketing') || lowerMessage.includes('customers')) {
        return "Marketing is essential for customer acquisition! 📈 Start with: 1) Define your ideal customer persona, 2) Choose 1-2 marketing channels to focus on, 3) Create valuable content, 4) Measure and optimize. What's your target audience?";
      }
    }
    
    if (primaryRole === 'mentor') {
      if (lowerMessage.includes('guidance') || lowerMessage.includes('advice')) {
        return "As a mentor, effective guidance comes from active listening and asking the right questions. 🎯 Try the GROW model: Goal (what do they want?), Reality (current situation), Options (possible paths), Way forward (action steps). What mentoring challenge are you facing?";
      }
      if (lowerMessage.includes('mentee') || lowerMessage.includes('student')) {
        return "Working with mentees requires patience and structure. 📚 Set clear expectations, establish regular check-ins, and focus on their goals. Remember: guide, don't solve for them. What's your mentee struggling with?";
      }
    }
    
    if (primaryRole === 'investor') {
      if (lowerMessage.includes('investment') || lowerMessage.includes('opportunity')) {
        return "Investment analysis requires thorough due diligence. 🔍 Key factors: 1) Market size and growth potential, 2) Team experience and execution ability, 3) Business model and unit economics, 4) Competitive advantage. What deal are you evaluating?";
      }
      if (lowerMessage.includes('market') || lowerMessage.includes('trends')) {
        return "Market analysis is fundamental to investment decisions. 📊 Look at: 1) Total Addressable Market (TAM), 2) Market growth rate, 3) Competitive landscape, 4) Regulatory environment. Which market sector interests you?";
      }
    }

    // General helpful responses
    if (lowerMessage.includes('help') || lowerMessage.includes('assist')) {
      return `I'm here to help, ${displayName}! 🤝 Based on your role as ${primaryRole}, I can assist with relevant topics and strategies. What specific area would you like guidance on?`;
    }
    
    if (lowerMessage.includes('what') || lowerMessage.includes('how')) {
      return `That's a great question! 🤔 To give you the most helpful answer, could you provide a bit more context? I'm here to help with ${primaryRole}-related topics and general business guidance.`;
    }

    // Default personalized response
    const responses = [
      `Thanks for sharing that, ${displayName}! That's an interesting perspective. Could you tell me more about what you're hoping to achieve?`,
      `I appreciate you bringing that up. As a ${displayName}, what's your main goal or challenge in this area?`,
      `That's a thoughtful point. How can I best help you explore this topic further?`,
      `Interesting! Based on your experience as a ${displayName}, what specific aspect would you like to focus on?`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = inputMessage.trim();
    setInputMessage('');
    setIsLoading(true);

    // Add user message
    addMessage('user', userMessage);

    // Simulate thinking time
    setTimeout(() => {
      const response = generateResponse(userMessage);
      addMessage('ai', response);
      setIsLoading(false);
    }, 1000 + Math.random() * 1000); // 1-2 seconds
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (timestamp: Date): string => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`flex flex-col bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl ${height} ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
            <Bot size={16} className="text-white" />
          </div>
          <div>
            <h3 className="text-white font-medium">
              {t('ai.chat.title', 'Yasmeen AI Assistant')}
            </h3>
            <p className="text-gray-400 text-xs">
              {displayName} • {t('ai.status.online', 'Online')}
            </p>
          </div>
        </div>
        
        <div className="text-xs text-gray-400">
          {t('ai.permission.level', 'Level')}: {permissionLevel}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex items-start space-x-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
              <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                message.type === 'user' 
                  ? 'bg-blue-500' 
                  : 'bg-gradient-to-r from-purple-500 to-blue-500'
              }`}>
                {message.type === 'user' ? (
                  <User size={12} className="text-white" />
                ) : (
                  <Bot size={12} className="text-white" />
                )}
              </div>
              
              <div className={`rounded-lg p-3 ${
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white/10 text-gray-100'
              }`}>
                <p className="text-sm whitespace-pre-wrap leading-relaxed">{message.content}</p>
                <p className="text-xs opacity-70 mt-1">
                  {formatTimestamp(message.timestamp)}
                </p>
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                <Bot size={12} className="text-white" />
              </div>
              <div className="bg-white/10 rounded-lg p-3">
                <div className="flex items-center space-x-2">
                  <Loader2 size={16} className="text-purple-400 animate-spin" />
                  <span className="text-gray-300 text-sm">
                    {t('ai.thinking', 'Thinking...')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-white/10">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={t('ai.chat.placeholder', 'Type your message...')}
            className="flex-1 bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-colors"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg p-2 transition-all"
          >
            <Send size={16} className="text-white" />
          </button>
        </div>
        
        <div className="mt-2 text-xs text-gray-500 text-center">
          {t('ai.demo.notice', '💡 Demo mode - responses are simulated for demonstration')}
        </div>
      </div>
    </div>
  );
};

export default SimpleWorkingChat;
