/**
 * Real Data Integration Test Suite
 * Tests that all components are properly integrated with real API data
 * and no longer use mock data or hardcoded content
 */

import { describe, it, expect, beforeAll, afterAll, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { store } from '../store/store';

// Import components to test
import MenteesManagementPage from '../pages/dashboard/MenteesManagementPage';
import ModerationAnalyticsPage from '../pages/dashboard/ModerationAnalyticsPage';
import InvestorOpportunitiesPage from '../pages/dashboard/InvestorOpportunitiesPage';
import { ConsolidatedWelcomeSection } from '../components/dashboard/shared/ConsolidatedWelcomeSection';

// Mock fetch globally
global.fetch = vi.fn();

const mockFetch = fetch as vi.MockedFunction<typeof fetch>;

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </QueryClientProvider>
    </Provider>
  );
};

describe('Real Data Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(() => 'mock-token'),
        setItem: vi.fn(),
        removeItem: vi.fn(),
      },
      writable: true,
    });
  });

  describe('MenteesManagementPage', () => {
    it('should fetch real mentee data from API', async () => {
      const mockMentees = [
        {
          id: '1',
          name: 'John Doe',
          email: '<EMAIL>',
          businessIdea: 'AI Platform',
          progress: 75,
          status: 'active'
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ results: mockMentees }),
      } as Response);

      render(
        <TestWrapper>
          <MenteesManagementPage />
        </TestWrapper>
      );

      // Verify API call was made
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          '/api/roles/mentor/my-mentees/',
          expect.objectContaining({
            headers: expect.objectContaining({
              'Authorization': 'Bearer mock-token',
              'Content-Type': 'application/json',
            }),
          })
        );
      });

      // Verify real data is displayed
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('AI Platform')).toBeInTheDocument();
      });
    });

    it('should handle API errors without showing mock data', async () => {
      mockFetch.mockRejectedValueOnce(new Error('API Error'));

      render(
        <TestWrapper>
          <MenteesManagementPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalled();
      });

      // Should show error message, not mock data
      await waitFor(() => {
        expect(screen.getByText(/Failed to load mentees/i)).toBeInTheDocument();
      });

      // Should NOT show any hardcoded mentee names
      expect(screen.queryByText('Alex Chen')).not.toBeInTheDocument();
      expect(screen.queryByText('Sarah Johnson')).not.toBeInTheDocument();
    });
  });

  describe('ModerationAnalyticsPage', () => {
    it('should fetch real moderation analytics from API', async () => {
      const mockAnalytics = {
        overview: {
          totalReports: 25,
          resolvedReports: 20,
          pendingReports: 5,
          averageResolutionTime: 3.2,
        },
        reportTrends: [],
        categoryBreakdown: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockAnalytics,
      } as Response);

      render(
        <TestWrapper>
          <ModerationAnalyticsPage />
        </TestWrapper>
      );

      // Verify API call was made
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/admin/moderation-analytics/'),
          expect.objectContaining({
            headers: expect.objectContaining({
              'Authorization': 'Bearer mock-token',
            }),
          })
        );
      });

      // Verify real data is displayed
      await waitFor(() => {
        expect(screen.getByText('25')).toBeInTheDocument(); // totalReports
        expect(screen.getByText('20')).toBeInTheDocument(); // resolvedReports
      });
    });

    it('should not use setTimeout for API simulation', async () => {
      const setTimeoutSpy = vi.spyOn(global, 'setTimeout');

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ overview: {}, reportTrends: [], categoryBreakdown: [] }),
      } as Response);

      render(
        <TestWrapper>
          <ModerationAnalyticsPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalled();
      });

      // Should not use setTimeout for API simulation
      expect(setTimeoutSpy).not.toHaveBeenCalledWith(
        expect.any(Function),
        expect.any(Number)
      );

      setTimeoutSpy.mockRestore();
    });
  });

  describe('InvestorOpportunitiesPage', () => {
    it('should fetch real investment opportunities from API', async () => {
      const mockOpportunities = [
        {
          id: '1',
          companyName: 'TechStart AI',
          industry: 'AI',
          stage: 'Series A',
          fundingGoal: 1000000,
          description: 'AI platform for businesses'
        }
      ];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ results: mockOpportunities }),
      } as Response);

      render(
        <TestWrapper>
          <InvestorOpportunitiesPage />
        </TestWrapper>
      );

      // Verify API call was made
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          '/api/incubator/funding-opportunities/',
          expect.objectContaining({
            headers: expect.objectContaining({
              'Authorization': 'Bearer mock-token',
            }),
          })
        );
      });

      // Verify real data is displayed
      await waitFor(() => {
        expect(screen.getByText('TechStart AI')).toBeInTheDocument();
        expect(screen.getByText('AI platform for businesses')).toBeInTheDocument();
      });
    });
  });

  describe('ConsolidatedWelcomeSection', () => {
    it('should fetch real dashboard stats from API', async () => {
      const mockUser = {
        id: 1,
        username: 'testuser',
        profile: { roles: ['admin'] }
      };

      const mockStats = {
        activeUsers: 150,
        todayActivity: 45,
        growthRate: 12.5,
        systemStatus: 'online'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockStats,
      } as Response);

      render(
        <TestWrapper>
          <ConsolidatedWelcomeSection user={mockUser} showStats={true} />
        </TestWrapper>
      );

      // Verify API call was made
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith(
          '/api/roles/admin/dashboard-stats/',
          expect.objectContaining({
            headers: expect.objectContaining({
              'Authorization': 'Bearer mock-token',
            }),
          })
        );
      });

      // Verify real data is displayed
      await waitFor(() => {
        expect(screen.getByText('150')).toBeInTheDocument(); // activeUsers
        expect(screen.getByText('45')).toBeInTheDocument(); // todayActivity
      });
    });

    it('should not show hardcoded stats values', async () => {
      const mockUser = {
        id: 1,
        username: 'testuser',
        profile: { roles: ['admin'] }
      };

      mockFetch.mockRejectedValueOnce(new Error('API Error'));

      render(
        <TestWrapper>
          <ConsolidatedWelcomeSection user={mockUser} showStats={true} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalled();
      });

      // Should show zero values, not hardcoded mock values
      await waitFor(() => {
        expect(screen.getByText('0')).toBeInTheDocument();
      });

      // Should NOT show hardcoded values like 1247, 856, etc.
      expect(screen.queryByText('1247')).not.toBeInTheDocument();
      expect(screen.queryByText('856')).not.toBeInTheDocument();
      expect(screen.queryByText('3456')).not.toBeInTheDocument();
    });
  });

  describe('API Service Integration', () => {
    it('should throw errors instead of returning empty arrays', async () => {
      // This test ensures our API services properly throw errors
      // instead of returning mock data fallbacks
      
      const { businessPlansAPI } = await import('../services/businessPlanApi');
      
      mockFetch.mockRejectedValueOnce(new Error('Network Error'));

      await expect(businessPlansAPI.getPlans()).rejects.toThrow('Network Error');
      
      // Should not return empty array
      expect(mockFetch).toHaveBeenCalled();
    });
  });
});

describe('Mock Data Elimination Verification', () => {
  it('should not contain setTimeout API simulations in components', () => {
    // This is a static analysis test to ensure no setTimeout is used for API simulation
    const setTimeoutSpy = vi.spyOn(global, 'setTimeout');
    
    // Import and render a component that previously used setTimeout
    render(
      <TestWrapper>
        <ModerationAnalyticsPage />
      </TestWrapper>
    );

    // Should not use setTimeout for API calls
    expect(setTimeoutSpy).not.toHaveBeenCalledWith(
      expect.any(Function),
      1000 // Common timeout value used in mock API calls
    );

    setTimeoutSpy.mockRestore();
  });

  it('should not contain hardcoded mock data arrays', () => {
    // This test verifies that components don't contain hardcoded data
    // by checking that they make actual API calls
    
    render(
      <TestWrapper>
        <InvestorOpportunitiesPage />
      </TestWrapper>
    );

    // Should make API call instead of using hardcoded data
    expect(mockFetch).toHaveBeenCalledWith(
      '/api/incubator/funding-opportunities/',
      expect.any(Object)
    );
  });
});
