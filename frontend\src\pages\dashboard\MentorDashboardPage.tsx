import React from 'react';
import { UnifiedDashboard } from '../../components/dashboard/unified';

/**
 * ✅ CONSOLIDATED MENTOR DASHBOARD PAGE
 * 
 * Now uses UnifiedDashboard with mentor role configuration.
 * Provides mentorship management through unified architecture.
 * 
 * Key Features:
 * - Unified dashboard with mentor privileges
 * - Mentee management and tracking
 * - Session scheduling and analytics
 * - Progress monitoring and feedback
 * - Consistent UI with role-specific features
 * - Maintainable single-source architecture
 */
const MentorDashboardPage: React.FC = () => {
  return <UnifiedDashboard role="mentor" />;
};

export default MentorDashboardPage;
