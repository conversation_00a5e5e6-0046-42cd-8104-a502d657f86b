import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Registration state interface
interface RegistrationState {
  formData: {
    firstName: string;
    lastName: string;
    username: string;
    email: string;
    selectedRole: string;
    phone?: string;
    location?: string;
    company?: string;
    jobTitle?: string;
    bio?: string;
    // Role-specific fields
    businessName?: string;
    businessStage?: string;
    industry?: string;
    fundingNeeded?: string;
    businessDescription?: string;
    expertise?: string;
    experience?: string;
    mentorshipAreas?: string;
    availability?: string;
    investmentRange?: string;
    investmentStage?: string;
    preferredIndustries?: string;
    investmentCriteria?: string;
    interests?: string;
    goals?: string;
  };
  registrationStatus: {
    isCompleted: boolean;
    requiresApproval: boolean;
    registeredAt?: string;
  };
  // Add validation and error tracking
  validationErrors: Record<string, string>;
  isSubmitting: boolean;
  submitError: string | null;
  currentStep: number;
  totalSteps: number;
}

// Initial state
const initialState: RegistrationState = {
  formData: {
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    selectedRole: '',
    phone: '',
    location: '',
    company: '',
    jobTitle: '',
    bio: '',
    businessName: '',
    businessStage: '',
    industry: '',
    fundingNeeded: '',
    businessDescription: '',
    expertise: '',
    experience: '',
    mentorshipAreas: '',
    availability: '',
    investmentRange: '',
    investmentStage: '',
    preferredIndustries: '',
    investmentCriteria: '',
    interests: '',
    goals: '',
  },
  registrationStatus: {
    isCompleted: false,
    requiresApproval: false,
  },
  validationErrors: {},
  isSubmitting: false,
  submitError: null,
  currentStep: 1,
  totalSteps: 3,
};

// Registration slice
const registrationSlice = createSlice({
  name: 'registration',
  initialState,
  reducers: {
    setRegistrationData: (state, action: PayloadAction<Partial<RegistrationState['formData']>>) => {
      state.formData = { ...state.formData, ...action.payload };
    },
    setRegistrationStatus: (state, action: PayloadAction<{
      isCompleted: boolean;
      requiresApproval: boolean;
      registeredAt?: string;
    }>) => {
      state.registrationStatus = action.payload;
    },
    completeRegistration: (state, action: PayloadAction<{
      requiresApproval: boolean;
    }>) => {
      state.registrationStatus.isCompleted = true;
      state.registrationStatus.requiresApproval = action.payload.requiresApproval;
      state.registrationStatus.registeredAt = new Date().toISOString();
      state.isSubmitting = false;
      state.submitError = null;
    },
    clearRegistrationData: (state) => {
      state.formData = initialState.formData;
      state.registrationStatus = initialState.registrationStatus;
      state.validationErrors = {};
      state.isSubmitting = false;
      state.submitError = null;
      state.currentStep = 1;
    },
    // New actions for better state management
    setValidationErrors: (state, action: PayloadAction<Record<string, string>>) => {
      state.validationErrors = action.payload;
    },
    clearValidationErrors: (state) => {
      state.validationErrors = {};
    },
    setSubmitting: (state, action: PayloadAction<boolean>) => {
      state.isSubmitting = action.payload;
    },
    setSubmitError: (state, action: PayloadAction<string | null>) => {
      state.submitError = action.payload;
    },
    setCurrentStep: (state, action: PayloadAction<number>) => {
      state.currentStep = action.payload;
    },
    nextStep: (state) => {
      if (state.currentStep < state.totalSteps) {
        state.currentStep += 1;
      }
    },
    previousStep: (state) => {
      if (state.currentStep > 1) {
        state.currentStep -= 1;
      }
    },
  },
});

// Export actions and reducer
export const {
  setRegistrationData,
  setRegistrationStatus,
  completeRegistration,
  clearRegistrationData,
  setValidationErrors,
  clearValidationErrors,
  setSubmitting,
  setSubmitError,
  setCurrentStep,
  nextStep,
  previousStep
} = registrationSlice.actions;

export default registrationSlice.reducer;
