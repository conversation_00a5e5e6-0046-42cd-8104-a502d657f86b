"""
Marketing URLs
URL configuration for marketing campaign APIs
"""

from django.urls import path
from .marketing_api import (
    get_vertical_campaign_strategy_api,
    generate_content_calendar_api,
    get_influencer_strategy_api,
    get_marketing_analytics_api,
    optimize_campaign_performance_api,
    get_competitor_analysis_api
)

app_name = 'marketing'

urlpatterns = [
    # ========================================
    # VERTICAL MARKETING CAMPAIGN ENDPOINTS
    # ========================================
    
    # Get campaign strategy for specific vertical
    path('campaigns/strategy/', get_vertical_campaign_strategy_api, name='get_campaign_strategy'),
    
    # Generate automated content calendar
    path('campaigns/content-calendar/', generate_content_calendar_api, name='generate_content_calendar'),
    
    # Get influencer marketing strategy
    path('campaigns/influencer-strategy/', get_influencer_strategy_api, name='get_influencer_strategy'),
    
    # ========================================
    # MARKETING ANALYTICS ENDPOINTS
    # ========================================
    
    # Get marketing analytics and performance metrics
    path('analytics/', get_marketing_analytics_api, name='get_marketing_analytics'),
    
    # Get campaign optimization recommendations
    path('analytics/optimize/', optimize_campaign_performance_api, name='optimize_campaign_performance'),
    
    # Get competitor analysis
    path('analytics/competitors/', get_competitor_analysis_api, name='get_competitor_analysis'),
]
