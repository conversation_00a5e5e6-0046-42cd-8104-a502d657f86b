/**
 * Registration Success Page
 * Confirmation page after successful registration with next steps
 */

import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useLanguage } from '../components/LanguageProvider';
import { useAppSelector } from '../store/hooks';
import RegistrationSuccessDemo from '../components/RegistrationSuccessDemo';
import {
  CheckCircle,
  Mail,
  Clock,
  Users,
  FileText,
  DollarSign,
  ArrowRight,
  Home,
  MessageSquare,
  Shield,
  Briefcase,
  TrendingUp
} from 'lucide-react';

const RegistrationSuccessPage: React.FC = () => {
  const { language } = useLanguage();
  const navigate = useNavigate();

  // Get registration data from Redux
  const { formData, registrationStatus } = useAppSelector(state => state.registration);

  // Extract user info from Redux
  const selectedRole = formData.selectedRole || 'user';
  const firstName = formData.firstName;
  const lastName = formData.lastName;
  const requiresApproval = registrationStatus.requiresApproval;

  // Community users don't need approval
  const isApprovalNeeded = selectedRole !== 'user' && requiresApproval;

  // Get personalized role-specific information
  const getRoleInfo = (role: string, name: string) => {
    const displayName = name || (language === 'ar' ? 'صديقنا العزيز' : 'Dear Friend');
    switch (role) {
      case 'entrepreneur':
        return {
          icon: <Briefcase className="w-8 h-8 text-blue-400" />,
          title: language === 'ar' ? `${displayName} - رائد أعمال مستقبلي` : `${displayName} - Future Entrepreneur`,
          description: language === 'ar'
            ? `${displayName}، نحن متحمسون لرؤية أفكارك تتحول إلى واقع! سيتم مراجعة طلبك خلال 3-4 أيام عمل.`
            : `${displayName}, we're excited to see your ideas come to life! Your application will be reviewed within 3-4 business days.`,
          timeframe: language === 'ar' ? '3-4 أيام عمل' : '3-4 business days',
          benefits: language === 'ar'
            ? ['أدوات إنشاء خطط العمل المتقدمة', 'التواصل المباشر مع الموجهين والمستثمرين', 'الانضمام لمجتمع رواد الأعمال النشط', 'ورش عمل حصرية ومناسبات شبكية']
            : ['Advanced business plan creation tools', 'Direct access to mentors and investors', 'Join our active entrepreneurial community', 'Exclusive workshops and networking events']
        };
      case 'mentor':
        return {
          icon: <Users className="w-8 h-8 text-green-400" />,
          title: language === 'ar' ? `${displayName} - موجه ملهم` : `${displayName} - Inspiring Mentor`,
          description: language === 'ar'
            ? `${displayName}، شكراً لرغبتك في مشاركة خبرتك! سيتم مراجعة طلبك خلال 3-4 أيام عمل.`
            : `${displayName}, thank you for wanting to share your expertise! Your application will be reviewed within 3-4 business days.`,
          timeframe: language === 'ar' ? '3-4 أيام عمل' : '3-4 business days',
          benefits: language === 'ar'
            ? ['توجيه الجيل القادم من رواد الأعمال', 'أدوات التوجيه والمتابعة المتقدمة', 'بناء شبكة مهنية قوية', 'مكافآت وتقدير خاص للموجهين المتميزين']
            : ['Guide the next generation of entrepreneurs', 'Advanced mentoring and tracking tools', 'Build a strong professional network', 'Special rewards and recognition for outstanding mentors']
        };
      case 'investor':
        return {
          icon: <DollarSign className="w-8 h-8 text-purple-400" />,
          title: language === 'ar' ? `${displayName} - مستثمر استراتيجي` : `${displayName} - Strategic Investor`,
          description: language === 'ar'
            ? `${displayName}، نقدر اهتمامك بدعم الابتكار! سيتم التحقق من اعتمادك ومراجعة طلبك خلال 3-4 أيام عمل.`
            : `${displayName}, we appreciate your interest in supporting innovation! Your credentials will be verified within 3-4 business days.`,
          timeframe: language === 'ar' ? '3-4 أيام عمل' : '3-4 business days',
          benefits: language === 'ar'
            ? ['الوصول الحصري لفرص الاستثمار المبكر', 'تقييم وتحليل خطط العمل المتقدمة', 'التواصل المباشر مع رواد الأعمال الواعدين', 'تقارير استثمارية مفصلة وتحليلات السوق']
            : ['Exclusive access to early-stage investment opportunities', 'Advanced business plan evaluation and analysis', 'Direct connection with promising entrepreneurs', 'Detailed investment reports and market analytics']
        };
      case 'user':
        return {
          icon: <Users className="w-8 h-8 text-green-400" />,
          title: language === 'ar' ? `أهلاً وسهلاً ${displayName}!` : `Welcome ${displayName}!`,
          description: language === 'ar'
            ? `${displayName}، مرحباً بك في عائلة ياسمين! حسابك جاهز الآن ويمكنك البدء في استكشاف عالم ريادة الأعمال.`
            : `${displayName}, welcome to the Yasmeen family! Your account is ready and you can start exploring the world of entrepreneurship.`,
          timeframe: language === 'ar' ? 'متاح الآن!' : 'Available Now!',
          benefits: language === 'ar'
            ? ['الوصول الفوري لجميع المحتويات التعليمية', 'المشاركة في المناقشات والمنتديات', 'التواصل مع المجتمع وبناء العلاقات', 'استكشاف الأدوات والموارد المجانية', 'حضور الفعاليات والورش التعليمية']
            : ['Immediate access to all educational content', 'Participate in discussions and forums', 'Connect with the community and build relationships', 'Explore free tools and resources', 'Attend educational events and workshops']
        };
      default:
        return {
          icon: <Users className="w-8 h-8 text-blue-400" />,
          title: language === 'ar' ? `مرحباً ${displayName}` : `Hello ${displayName}`,
          description: language === 'ar'
            ? `${displayName}، شكراً لانضمامك إلينا! سيتم مراجعة طلبك خلال 3-4 أيام عمل.`
            : `${displayName}, thank you for joining us! Your application will be reviewed within 3-4 business days.`,
          timeframe: language === 'ar' ? '3-4 أيام عمل' : '3-4 business days',
          benefits: language === 'ar'
            ? ['الوصول للمنصة', 'المشاركة في المجتمع']
            : ['Platform access', 'Community participation']
        };
    }
  };

  const roleInfo = getRoleInfo(selectedRole || 'user', firstName);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4">
      {/* Demo Component for Testing */}
      <RegistrationSuccessDemo />

      <div className="max-w-4xl w-full">
        {/* Success Header */}
        <div className="text-center mb-12">
          <Link to="/" className="inline-block mb-4">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400">
              Yasmeen AI
            </h1>
          </Link>
          <div className="w-20 h-20 bg-green-500/20 border border-green-400/30 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-12 h-12 text-green-400" />
          </div>

          <h2 className="text-3xl font-bold text-white mb-4">
            {isApprovalNeeded
              ? (language === 'ar'
                  ? `مرحباً ${firstName}! تم تسجيل حسابك بنجاح!`
                  : `Welcome ${firstName}! Registration Submitted!`)
              : (language === 'ar'
                  ? `مرحباً ${firstName}! مرحباً بك في المجتمع!`
                  : `Welcome ${firstName}! You're now part of the community!`)
            }
          </h2>

          <p className="text-gray-300 text-lg mb-6">
            {isApprovalNeeded
              ? (language === 'ar'
                  ? `عزيزي ${firstName}، تم إرسال طلب التسجيل بنجاح. حسابك قيد المراجعة من قبل فريق الإدارة.`
                  : `Dear ${firstName}, your registration has been submitted successfully. Your account is under review by our admin team.`
                )
              : (language === 'ar'
                  ? `عزيزي ${firstName}، تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول والبدء في استكشاف المجتمع.`
                  : `Dear ${firstName}, your account has been created successfully! You can now login and start exploring the community.`
                )
            }
          </p>

          {/* Personalized Welcome Message */}
          <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30 rounded-lg p-6 mb-6">
            <div className="text-center">
              <h3 className="text-xl font-bold text-white mb-3">
                {selectedRole === 'entrepreneur' && (language === 'ar'
                  ? `${firstName}، نحن متحمسون لدعم رحلتك الريادية!`
                  : `${firstName}, we're excited to support your entrepreneurial journey!`)}
                {selectedRole === 'mentor' && (language === 'ar'
                  ? `${firstName}، شكراً لرغبتك في إلهام الآخرين!`
                  : `${firstName}, thank you for wanting to inspire others!`)}
                {selectedRole === 'investor' && (language === 'ar'
                  ? `${firstName}، نقدر استثمارك في مستقبل الابتكار!`
                  : `${firstName}, we appreciate your investment in the future of innovation!`)}
                {selectedRole === 'user' && (language === 'ar'
                  ? `${firstName}، أهلاً بك في عائلة ياسمين الكبيرة!`
                  : `${firstName}, welcome to the big Yasmeen family!`)}
              </h3>
              <p className="text-gray-300">
                {selectedRole === 'entrepreneur' && (language === 'ar'
                  ? 'ستحصل على جميع الأدوات والموارد اللازمة لتحويل فكرتك إلى مشروع ناجح.'
                  : 'You\'ll get all the tools and resources needed to turn your idea into a successful venture.')}
                {selectedRole === 'mentor' && (language === 'ar'
                  ? 'ستتمكن من مشاركة خبرتك ومساعدة الجيل القادم من رواد الأعمال.'
                  : 'You\'ll be able to share your expertise and help the next generation of entrepreneurs.')}
                {selectedRole === 'investor' && (language === 'ar'
                  ? 'ستحصل على وصول حصري لأفضل الفرص الاستثمارية في المنطقة.'
                  : 'You\'ll get exclusive access to the best investment opportunities in the region.')}
                {selectedRole === 'user' && (language === 'ar'
                  ? 'ستستمتع بتجربة تعليمية غنية ومجتمع داعم من المهتمين بريادة الأعمال.'
                  : 'You\'ll enjoy a rich educational experience and a supportive community of entrepreneurship enthusiasts.')}
              </p>
            </div>
          </div>
        </div>

        {/* Role-Specific Information */}
        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 mb-8">
          <div className="flex items-center mb-4">
            {roleInfo.icon}
            <h3 className="text-xl font-bold text-white ml-3">{roleInfo.title}</h3>
          </div>

          <p className="text-gray-300 mb-4">{roleInfo.description}</p>

          {isApprovalNeeded ? (
            <div className="bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-4 mb-4">
              <div className="flex items-center">
                <Clock className="w-5 h-5 text-yellow-400 mr-2" />
                <span className="text-yellow-300 font-semibold">
                  {language === 'ar' ? 'الإطار الزمني: ' : 'Timeline: '}{roleInfo.timeframe}
                </span>
              </div>
            </div>
          ) : (
            <div className="bg-green-500/20 border border-green-400/30 rounded-lg p-4 mb-4">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-400 mr-2" />
                <span className="text-green-300 font-semibold">
                  {language === 'ar' ? 'حسابك جاهز للاستخدام!' : 'Your account is ready to use!'}
                </span>
              </div>
            </div>
          )}

          {roleInfo.benefits.length > 0 && (
            <div>
              <h4 className="text-lg font-semibold text-white mb-3">
                {language === 'ar' ? 'ما يمكنك فعله الآن:' : 'What you can do now:'}
              </h4>
              <ul className="space-y-2">
                {roleInfo.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-center text-gray-300">
                    <CheckCircle className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                    {benefit}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Next Steps */}
        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 mb-8">
          <h3 className="text-xl font-bold text-white mb-6">
            {language === 'ar' ? 'الخطوات التالية' : 'Next Steps'}
          </h3>

          <div className="space-y-4">
            {isApprovalNeeded ? (
              <>
                <div className="flex items-center p-4 bg-yellow-500/20 border border-yellow-400/30 rounded-lg">
                  <Mail className="w-6 h-6 text-yellow-400 mr-3" />
                  <div>
                    <h4 className="font-semibold text-yellow-300">
                      {language === 'ar' ? 'راقب بريدك الإلكتروني' : 'Check Your Email'}
                    </h4>
                    <p className="text-yellow-200 text-sm">
                      {language === 'ar'
                        ? 'ستصلك رسالة تأكيد خلال 3-4 أيام عمل عند الموافقة على حسابك مع تعليمات تسجيل الدخول.'
                        : 'You will receive a confirmation email within 3-4 business days once your account is approved with login instructions.'
                      }
                    </p>
                  </div>
                </div>

                <div className="flex items-center p-4 bg-blue-500/20 border border-blue-400/30 rounded-lg">
                  <Clock className="w-6 h-6 text-blue-400 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-300">
                      {language === 'ar' ? 'ماذا يحدث الآن؟' : 'What Happens Next?'}
                    </h4>
                    <p className="text-blue-200 text-sm">
                      {language === 'ar'
                        ? 'فريق الإدارة سيراجع طلبك ويتحقق من المعلومات المقدمة. لا حاجة لاتخاذ أي إجراء من جانبك.'
                        : 'Our admin team will review your application and verify the information provided. No action needed from your side.'
                      }
                    </p>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="flex items-center p-4 bg-green-500/20 border border-green-400/30 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-400 mr-3" />
                  <div>
                    <h4 className="font-semibold text-green-300">
                      {language === 'ar' ? `${firstName}، ابدأ رحلتك الآن!` : `${firstName}, Get Started Now!`}
                    </h4>
                    <p className="text-green-200 text-sm">
                      {language === 'ar'
                        ? `${firstName}، حسابك جاهز! يمكنك تسجيل الدخول فوراً والبدء في استكشاف المجتمع والمحتوى التعليمي.`
                        : `${firstName}, your account is ready! You can login immediately and start exploring the community and educational content.`
                      }
                    </p>
                  </div>
                </div>

                <div className="flex items-center p-4 bg-blue-500/20 border border-blue-400/30 rounded-lg">
                  <Users className="w-6 h-6 text-blue-400 mr-3" />
                  <div>
                    <h4 className="font-semibold text-blue-300">
                      {language === 'ar' ? `${firstName}، انضم لمجتمعنا النشط` : `${firstName}, Join Our Active Community`}
                    </h4>
                    <p className="text-blue-200 text-sm">
                      {language === 'ar'
                        ? `${firstName}، شارك في المناقشات، تواصل مع الأعضاء الآخرين، واستفد من الموارد التعليمية المتاحة.`
                        : `${firstName}, participate in discussions, connect with other members, and benefit from available educational resources.`
                      }
                    </p>
                  </div>
                </div>
              </>
            )}

            <div className="flex items-center p-4 bg-purple-500/20 border border-purple-400/30 rounded-lg">
              <MessageSquare className="w-6 h-6 text-purple-400 mr-3" />
              <div>
                <h4 className="font-semibold text-purple-300">
                  {language === 'ar' ? 'هل تحتاج مساعدة؟' : 'Need Help?'}
                </h4>
                <p className="text-purple-200 text-sm">
                  {language === 'ar'
                    ? 'إذا كان لديك أي أسئلة أو تحتاج مساعدة، تواصل مع فريق الدعم.'
                    : 'If you have any questions or need assistance, contact our support team.'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-center gap-4 mt-8">
          {isApprovalNeeded ? (
            <>
              <button
                onClick={() => navigate('/')}
                className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-medium text-white hover:from-purple-700 hover:to-blue-700 transition-all duration-200 flex items-center justify-center"
              >
                <Home className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'العودة للصفحة الرئيسية' : 'Back to Home'}
              </button>

              <button
                onClick={() => window.open('mailto:<EMAIL>?subject=Registration Support&body=Hello, I just registered and have a question about my application status.', '_blank')}
                className="px-6 py-3 border border-white/30 rounded-lg font-medium text-white hover:bg-white/10 transition-all duration-200 flex items-center justify-center"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'تواصل مع الدعم' : 'Contact Support'}
              </button>

              <button
                onClick={() => navigate('/register')}
                className="px-6 py-3 border border-green-500/50 bg-green-500/10 rounded-lg font-medium text-green-300 hover:bg-green-500/20 transition-all duration-200 flex items-center justify-center"
              >
                <Users className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'مساعدة شخص آخر' : 'Help Someone Register'}
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => navigate('/login')}
                className="px-8 py-4 bg-gradient-to-r from-green-600 to-blue-600 rounded-lg font-bold text-white hover:from-green-700 hover:to-blue-700 transition-all duration-200 flex items-center justify-center text-lg"
              >
                <CheckCircle className="w-5 h-5 mr-2" />
                {language === 'ar' ? 'تسجيل الدخول الآن' : 'Login Now'}
              </button>

              <button
                onClick={() => navigate('/')}
                className="px-6 py-3 border border-white/30 rounded-lg font-medium text-white hover:bg-white/10 transition-all duration-200 flex items-center justify-center"
              >
                <Home className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'العودة للصفحة الرئيسية' : 'Back to Home'}
              </button>

              <button
                onClick={() => navigate('/register')}
                className="px-6 py-3 border border-green-500/50 bg-green-500/10 rounded-lg font-medium text-green-300 hover:bg-green-500/20 transition-all duration-200 flex items-center justify-center"
              >
                <Users className="w-4 h-4 mr-2" />
                {language === 'ar' ? 'مساعدة شخص آخر' : 'Help Someone Register'}
              </button>
            </>
          )}
        </div>

        {/* Footer Note */}
        <div className="text-center mt-12">
          <p className="text-gray-400">
            {language === 'ar'
              ? `${firstName}، مرحباً بك في رحلة ريادة الأعمال! نحن هنا لدعمك في كل خطوة.`
              : `${firstName}, welcome to your entrepreneurial journey! We're here to support you every step of the way.`
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegistrationSuccessPage;
