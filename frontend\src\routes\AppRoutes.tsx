import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAppSelector } from '../store/hooks';
import useReduxRoles from '../hooks/useReduxRoles';

// Core Pages
import HomePage from '../pages/HomePage';
import LoginPage from '../pages/LoginPage';
import EnhancedRegisterPage from '../pages/EnhancedRegisterPage';
import RegistrationSuccessPage from '../pages/RegistrationSuccessPage';
import LogoutPage from '../pages/LogoutPage';
import DashboardPage from '../pages/DashboardPage';
import NotFoundPage from '../pages/NotFoundPage';
import AccessDeniedPage from '../pages/AccessDeniedPage';
import UserProfilePage from '../pages/UserProfilePage';
import SettingsPage from '../pages/SettingsPage';
import { FeaturesPage } from '../pages/FeaturesPage';

// Layout
import Layout from '../components/layout/Layout';

// AI Chat
import AIChatPage from '../pages/ai/AIChatPage';

// Loading component
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
  </div>
);

/**
 * ✅ REDUX-BASED ROUTING SYSTEM
 * Clean, maintainable routing with Redux role management
 */
const AppRoutes: React.FC = () => {
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const { getDashboardRoute } = useReduxRoles();

  // ✅ DON'T BLOCK PUBLIC ROUTES - Only show loading for protected routes

  return (
    <Suspense fallback={<LoadingFallback />}>
      <Routes>
        {/* ===== PUBLIC ROUTES ===== */}
        <Route path="/" element={<HomePage />} />
        <Route path="/features" element={<FeaturesPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<EnhancedRegisterPage />} />
        <Route path="/registration-success" element={<RegistrationSuccessPage />} />
        <Route path="/register/success" element={<RegistrationSuccessPage />} />
        <Route path="/signup/success" element={<RegistrationSuccessPage />} />
        <Route path="/logout" element={<LogoutPage />} />
        <Route path="/access-denied" element={<AccessDeniedPage />} />

        {/* ===== PROTECTED ROUTES ===== */}
        {isAuthenticated ? (
          <>
            {/* ===== REGULAR AUTHENTICATED ROUTES ===== */}
            <Route path="/" element={<Layout />}>
              {/* Dashboard Routes - Dynamic based on role */}
              <Route path="/user/dashboard" element={<DashboardPage />} />
              <Route path="/entrepreneur/dashboard" element={<DashboardPage />} />
              <Route path="/mentor/dashboard" element={<DashboardPage />} />
              <Route path="/investor/dashboard" element={<DashboardPage />} />
              <Route path="/admin/dashboard" element={<DashboardPage />} />
              <Route path="/super-admin/dashboard" element={<DashboardPage />} />
              <Route path="/moderator/dashboard" element={<DashboardPage />} />

              {/* Role-specific Profile Routes */}
              <Route path="/user/profile" element={<UserProfilePage />} />
              <Route path="/entrepreneur/profile" element={<UserProfilePage />} />
              <Route path="/mentor/profile" element={<UserProfilePage />} />
              <Route path="/investor/profile" element={<UserProfilePage />} />
              <Route path="/admin/profile" element={<UserProfilePage />} />
              <Route path="/super-admin/profile" element={<UserProfilePage />} />
              <Route path="/moderator/profile" element={<UserProfilePage />} />

              {/* Role-specific Settings Routes */}
              <Route path="/user/settings" element={<SettingsPage />} />
              <Route path="/entrepreneur/settings" element={<SettingsPage />} />
              <Route path="/mentor/settings" element={<SettingsPage />} />
              <Route path="/investor/settings" element={<SettingsPage />} />
              <Route path="/admin/settings" element={<SettingsPage />} />
              <Route path="/super-admin/settings" element={<SettingsPage />} />
              <Route path="/moderator/settings" element={<SettingsPage />} />

              {/* General Routes (fallback) */}
              <Route path="/profile" element={<UserProfilePage />} />
              <Route path="/settings" element={<SettingsPage />} />
            </Route>

            {/* ===== AI-FOCUSED ROUTES (Clean interface without header) ===== */}
            <Route path="/" element={<Layout type="ai-focused" />}>
              {/* Role-specific AI Chat Routes */}
              <Route path="/user/ai-chat" element={<AIChatPage />} />
              <Route path="/entrepreneur/ai-chat" element={<AIChatPage />} />
              <Route path="/mentor/ai-chat" element={<AIChatPage />} />
              <Route path="/investor/ai-chat" element={<AIChatPage />} />
              <Route path="/admin/ai-chat" element={<AIChatPage />} />
              <Route path="/super-admin/ai-chat" element={<AIChatPage />} />
              <Route path="/moderator/ai-chat" element={<AIChatPage />} />

              {/* General AI Chat (fallback) */}
              <Route path="/ai-chat" element={<AIChatPage />} />
            </Route>

            {/* Redirect /dashboard to role-specific dashboard */}
            <Route 
              path="/dashboard" 
              element={<Navigate to={getDashboardRoute()} replace />} 
            />
          </>
        ) : (
          /* Redirect unauthenticated users to login */
          <Route path="/dashboard/*" element={<Navigate to="/login" replace />} />
        )}

        {/* ===== 404 ROUTE ===== */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
