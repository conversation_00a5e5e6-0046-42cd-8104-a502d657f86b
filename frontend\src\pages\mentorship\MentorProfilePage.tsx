/**
 * Mentor Profile Page
 * Detailed mentor profile with reviews, availability, and booking functionality
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Star, 
  MapPin, 
  Briefcase, 
  Users, 
  Clock,
  MessageSquare,
  Award,
  Calendar,
  Video,
  Phone,
  Globe,
  CheckCircle,
  ArrowLeft,
  Heart,
  Share2
} from 'lucide-react';

interface MentorProfile {
  id: string;
  name: string;
  title: string;
  company: string;
  industry: string[];
  expertise: string[];
  location: string;
  experience: number;
  rating: number;
  reviewCount: number;
  menteeCount: number;
  successStories: number;
  languages: string[];
  availability: 'available' | 'limited' | 'unavailable';
  hourlyRate?: number;
  bio: string;
  bioAr: string;
  avatar?: string;
  responseTime: string;
  sessionTypes: string[];
  achievements: string[];
  education: Array<{
    degree: string;
    institution: string;
    year: string;
  }>;
  workExperience: Array<{
    title: string;
    company: string;
    duration: string;
    description: string;
  }>;
  specializations: string[];
  mentorshipAreas: string[];
  availableSlots: Array<{
    date: string;
    time: string;
    duration: number;
  }>;
}

interface Review {
  id: string;
  mentee: string;
  rating: number;
  comment: string;
  date: string;
  verified: boolean;
}

const MentorProfilePage: React.FC = () => {
  const { mentorId } = useParams();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [mentor, setMentor] = useState<MentorProfile | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadMentorProfile();
  }, [mentorId]);

  const loadMentorProfile = async () => {
    try {
      // Mock data - replace with actual API call
      const mockMentor: MentorProfile = {
        id: mentorId || '1',
        name: 'Dr. Ahmed Al-Rashid',
        title: 'Senior FinTech Advisor',
        company: 'MENA Ventures',
        industry: ['FinTech', 'Banking', 'Investment'],
        expertise: ['Digital Payments', 'Blockchain', 'Islamic Finance', 'Regulatory Compliance', 'Startup Strategy'],
        location: 'Riyadh, Saudi Arabia',
        experience: 15,
        rating: 4.9,
        reviewCount: 47,
        menteeCount: 23,
        successStories: 18,
        languages: ['Arabic', 'English'],
        availability: 'available',
        hourlyRate: 150,
        bio: 'Former VP of Digital Innovation at Saudi National Bank with 15+ years in FinTech. Led digital transformation initiatives across MENA region, launched 5 successful fintech products, and mentored 50+ entrepreneurs. Passionate about Islamic finance innovation and regulatory technology.',
        bioAr: 'نائب رئيس سابق للابتكار الرقمي في البنك الأهلي السعودي مع أكثر من 15 عامًا في التكنولوجيا المالية. قاد مبادرات التحول الرقمي عبر منطقة الشرق الأوسط وشمال أفريقيا، وأطلق 5 منتجات تكنولوجيا مالية ناجحة، وأرشد أكثر من 50 رائد أعمال.',
        responseTime: '< 2 hours',
        sessionTypes: ['Video Call', 'Phone', 'In-Person'],
        achievements: [
          'Forbes 40 Under 40 MENA 2020',
          'FinTech Innovation Award 2019',
          'Digital Banking Excellence Award 2018'
        ],
        education: [
          {
            degree: 'PhD in Financial Technology',
            institution: 'King Fahd University',
            year: '2010'
          },
          {
            degree: 'MBA in Finance',
            institution: 'INSEAD',
            year: '2006'
          }
        ],
        workExperience: [
          {
            title: 'VP Digital Innovation',
            company: 'Saudi National Bank',
            duration: '2018 - 2023',
            description: 'Led digital transformation initiatives and fintech partnerships'
          },
          {
            title: 'Senior Consultant',
            company: 'McKinsey & Company',
            duration: '2015 - 2018',
            description: 'Advised banks and fintech companies on digital strategy'
          }
        ],
        specializations: ['Digital Banking', 'Payment Systems', 'Blockchain Technology', 'Islamic Finance'],
        mentorshipAreas: ['Business Strategy', 'Product Development', 'Fundraising', 'Market Entry'],
        availableSlots: [
          { date: '2024-01-25', time: '14:00', duration: 60 },
          { date: '2024-01-26', time: '10:00', duration: 45 },
          { date: '2024-01-27', time: '16:00', duration: 60 }
        ]
      };

      const mockReviews: Review[] = [
        {
          id: '1',
          mentee: 'Sarah Al-Mahmoud',
          rating: 5,
          comment: 'Dr. Ahmed provided invaluable insights into the FinTech regulatory landscape. His guidance helped us navigate complex compliance requirements.',
          date: '2024-01-15',
          verified: true
        },
        {
          id: '2',
          mentee: 'Omar Hassan',
          rating: 5,
          comment: 'Exceptional mentor with deep industry knowledge. His strategic advice was crucial for our Series A funding round.',
          date: '2024-01-10',
          verified: true
        },
        {
          id: '3',
          mentee: 'Fatima Al-Zahra',
          rating: 4,
          comment: 'Great insights into digital banking trends. Very responsive and professional.',
          date: '2024-01-05',
          verified: true
        }
      ];

      setMentor(mockMentor);
      setReviews(mockReviews);
    } catch (error) {
      console.error('Error loading mentor profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBookSession = () => {
    navigate(`/dashboard/mentorship/book/${mentorId}`);
  };

  const handleSendMessage = () => {
    navigate(`/dashboard/messages/new?mentor=${mentorId}`);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (!mentor) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
            {language === 'ar' ? 'لم يتم العثور على الموجه' : 'Mentor Not Found'}
          </ArabicTypography>
          <ArabicButton onClick={() => navigate('/dashboard/mentorship/discover')}>
            {language === 'ar' ? 'العودة للبحث' : 'Back to Discovery'}
          </ArabicButton>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <ArabicButton
              variant="ghost"
              onClick={() => navigate(-1)}
              icon={<ArrowLeft className="w-4 h-4" />}
              className={isRTL ? 'ml-4' : 'mr-4'}
            >
              {language === 'ar' ? 'رجوع' : 'Back'}
            </ArabicButton>
            <ArabicTypography variant="h1" className="text-gray-900 font-bold">
              {language === 'ar' ? 'ملف الموجه' : 'Mentor Profile'}
            </ArabicTypography>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Profile */}
          <div className="lg:col-span-2">
            {/* Profile Header */}
            <ArabicCard className="mb-8">
              <div className={`flex flex-col md:flex-row items-start ${isRTL ? 'md:flex-row-reverse' : ''}`}>
                <div className={`flex-shrink-0 ${isRTL ? 'md:ml-6' : 'md:mr-6'} mb-4 md:mb-0`}>
                  <div className="w-32 h-32 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-4xl font-bold text-blue-600">
                      {mentor.name.charAt(0)}
                    </span>
                  </div>
                </div>
                
                <div className="flex-1">
                  <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div>
                      <ArabicTypography variant="h2" className="text-gray-900 font-bold mb-2">
                        {mentor.name}
                      </ArabicTypography>
                      <ArabicTypography variant="h5" className="text-blue-600 font-semibold mb-2">
                        {mentor.title}
                      </ArabicTypography>
                      <ArabicTypography variant="body1" className="text-gray-600 mb-4">
                        {mentor.company}
                      </ArabicTypography>
                    </div>
                    
                    <div className={`flex space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                      <ArabicButton
                        variant="outline"
                        size="sm"
                        icon={<Heart className="w-4 h-4" />}
                      >
                        {language === 'ar' ? 'حفظ' : 'Save'}
                      </ArabicButton>
                      <ArabicButton
                        variant="outline"
                        size="sm"
                        icon={<Share2 className="w-4 h-4" />}
                      >
                        {language === 'ar' ? 'مشاركة' : 'Share'}
                      </ArabicButton>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Star className="w-5 h-5 text-yellow-500" />
                        <span className={`text-lg font-bold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                          {mentor.rating}
                        </span>
                      </div>
                      <ArabicTypography variant="caption" color="secondary">
                        ({mentor.reviewCount} {language === 'ar' ? 'تقييم' : 'reviews'})
                      </ArabicTypography>
                    </div>
                    
                    <div className="text-center">
                      <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Users className="w-5 h-5 text-blue-500" />
                        <span className={`text-lg font-bold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                          {mentor.menteeCount}
                        </span>
                      </div>
                      <ArabicTypography variant="caption" color="secondary">
                        {language === 'ar' ? 'متدرب' : 'mentees'}
                      </ArabicTypography>
                    </div>
                    
                    <div className="text-center">
                      <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Award className="w-5 h-5 text-green-500" />
                        <span className={`text-lg font-bold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                          {mentor.successStories}
                        </span>
                      </div>
                      <ArabicTypography variant="caption" color="secondary">
                        {language === 'ar' ? 'قصة نجاح' : 'success stories'}
                      </ArabicTypography>
                    </div>
                    
                    <div className="text-center">
                      <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Briefcase className="w-5 h-5 text-purple-500" />
                        <span className={`text-lg font-bold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                          {mentor.experience}
                        </span>
                      </div>
                      <ArabicTypography variant="caption" color="secondary">
                        {language === 'ar' ? 'سنة خبرة' : 'years exp'}
                      </ArabicTypography>
                    </div>
                  </div>

                  {/* Quick Info */}
                  <div className="space-y-2">
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <MapPin className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <ArabicTypography variant="body2" color="secondary">
                        {mentor.location}
                      </ArabicTypography>
                    </div>
                    
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Clock className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <ArabicTypography variant="body2" color="secondary">
                        {language === 'ar' ? 'يرد خلال' : 'Responds within'} {mentor.responseTime}
                      </ArabicTypography>
                    </div>
                    
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Globe className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <ArabicTypography variant="body2" color="secondary">
                        {mentor.languages.join(', ')}
                      </ArabicTypography>
                    </div>
                  </div>
                </div>
              </div>
            </ArabicCard>

            {/* Tabs */}
            <div className="mb-6">
              <div className={`flex border-b ${isRTL ? 'flex-row-reverse' : ''}`}>
                {[
                  { id: 'overview', label: language === 'ar' ? 'نظرة عامة' : 'Overview' },
                  { id: 'experience', label: language === 'ar' ? 'الخبرة' : 'Experience' },
                  { id: 'reviews', label: language === 'ar' ? 'التقييمات' : 'Reviews' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors font-arabic ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Bio */}
                <ArabicCard title={language === 'ar' ? 'نبذة عن الموجه' : 'About'}>
                  <ArabicTypography variant="body1" className="leading-relaxed">
                    {language === 'ar' ? mentor.bioAr : mentor.bio}
                  </ArabicTypography>
                </ArabicCard>

                {/* Expertise */}
                <ArabicCard title={language === 'ar' ? 'مجالات الخبرة' : 'Areas of Expertise'}>
                  <div className="flex flex-wrap gap-2">
                    {mentor.expertise.map((skill, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-sm font-arabic"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </ArabicCard>

                {/* Mentorship Areas */}
                <ArabicCard title={language === 'ar' ? 'مجالات الإرشاد' : 'Mentorship Areas'}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {mentor.mentorshipAreas.map((area, index) => (
                      <div key={index} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <ArabicTypography variant="body2">
                          {area}
                        </ArabicTypography>
                      </div>
                    ))}
                  </div>
                </ArabicCard>
              </div>
            )}

            {activeTab === 'experience' && (
              <div className="space-y-6">
                {/* Work Experience */}
                <ArabicCard title={language === 'ar' ? 'الخبرة العملية' : 'Work Experience'}>
                  <div className="space-y-4">
                    {mentor.workExperience.map((exp, index) => (
                      <div key={index} className="border-l-2 border-blue-200 pl-4">
                        <ArabicTypography variant="h5" className="font-semibold">
                          {exp.title}
                        </ArabicTypography>
                        <ArabicTypography variant="body1" className="text-blue-600 font-semibold">
                          {exp.company}
                        </ArabicTypography>
                        <ArabicTypography variant="body2" color="secondary" className="mb-2">
                          {exp.duration}
                        </ArabicTypography>
                        <ArabicTypography variant="body2">
                          {exp.description}
                        </ArabicTypography>
                      </div>
                    ))}
                  </div>
                </ArabicCard>

                {/* Education */}
                <ArabicCard title={language === 'ar' ? 'التعليم' : 'Education'}>
                  <div className="space-y-3">
                    {mentor.education.map((edu, index) => (
                      <div key={index}>
                        <ArabicTypography variant="body1" className="font-semibold">
                          {edu.degree}
                        </ArabicTypography>
                        <ArabicTypography variant="body2" className="text-blue-600">
                          {edu.institution}
                        </ArabicTypography>
                        <ArabicTypography variant="body2" color="secondary">
                          {edu.year}
                        </ArabicTypography>
                      </div>
                    ))}
                  </div>
                </ArabicCard>

                {/* Achievements */}
                <ArabicCard title={language === 'ar' ? 'الإنجازات والجوائز' : 'Achievements & Awards'}>
                  <div className="space-y-2">
                    {mentor.achievements.map((achievement, index) => (
                      <div key={index} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Award className={`w-4 h-4 text-yellow-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <ArabicTypography variant="body2">
                          {achievement}
                        </ArabicTypography>
                      </div>
                    ))}
                  </div>
                </ArabicCard>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="space-y-6">
                {reviews.map((review) => (
                  <ArabicCard key={review.id}>
                    <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className="flex-1">
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} mb-2`}>
                          <ArabicTypography variant="body1" className="font-semibold">
                            {review.mentee}
                          </ArabicTypography>
                          {review.verified && (
                            <CheckCircle className={`w-4 h-4 text-green-500 ${isRTL ? 'mr-2' : 'ml-2'}`} />
                          )}
                        </div>
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} mb-2`}>
                          {renderStars(review.rating)}
                          <ArabicTypography variant="caption" color="secondary" className={isRTL ? 'mr-2' : 'ml-2'}>
                            {new Date(review.date).toLocaleDateString()}
                          </ArabicTypography>
                        </div>
                        <ArabicTypography variant="body2">
                          {review.comment}
                        </ArabicTypography>
                      </div>
                    </div>
                  </ArabicCard>
                ))}
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Booking Card */}
            <ArabicCard>
              <div className="text-center mb-4">
                {mentor.hourlyRate && (
                  <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
                    ${mentor.hourlyRate}/{language === 'ar' ? 'ساعة' : 'hour'}
                  </ArabicTypography>
                )}
                
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${
                  mentor.availability === 'available' ? 'bg-green-100 text-green-800' :
                  mentor.availability === 'limited' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                } mb-4`}>
                  {mentor.availability === 'available' ? (language === 'ar' ? 'متاح' : 'Available') :
                   mentor.availability === 'limited' ? (language === 'ar' ? 'محدود' : 'Limited') :
                   (language === 'ar' ? 'غير متاح' : 'Unavailable')}
                </div>
              </div>

              <div className="space-y-3">
                <ArabicButton
                  className="w-full"
                  onClick={handleBookSession}
                  icon={<Calendar className="w-4 h-4" />}
                  disabled={mentor.availability === 'unavailable'}
                >
                  {language === 'ar' ? 'حجز جلسة' : 'Book Session'}
                </ArabicButton>
                
                <ArabicButton
                  variant="outline"
                  className="w-full"
                  onClick={handleSendMessage}
                  icon={<MessageSquare className="w-4 h-4" />}
                >
                  {language === 'ar' ? 'إرسال رسالة' : 'Send Message'}
                </ArabicButton>
              </div>
            </ArabicCard>

            {/* Session Types */}
            <ArabicCard title={language === 'ar' ? 'أنواع الجلسات' : 'Session Types'}>
              <div className="space-y-3">
                {mentor.sessionTypes.map((type, index) => (
                  <div key={index} className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    {type === 'Video Call' && <Video className={`w-4 h-4 text-blue-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />}
                    {type === 'Phone' && <Phone className={`w-4 h-4 text-green-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />}
                    {type === 'In-Person' && <Users className={`w-4 h-4 text-purple-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />}
                    <ArabicTypography variant="body2">
                      {type === 'Video Call' ? (language === 'ar' ? 'مكالمة فيديو' : 'Video Call') :
                       type === 'Phone' ? (language === 'ar' ? 'مكالمة هاتفية' : 'Phone Call') :
                       type === 'In-Person' ? (language === 'ar' ? 'لقاء شخصي' : 'In-Person') : type}
                    </ArabicTypography>
                  </div>
                ))}
              </div>
            </ArabicCard>

            {/* Available Slots */}
            <ArabicCard title={language === 'ar' ? 'المواعيد المتاحة' : 'Available Slots'}>
              <div className="space-y-2">
                {mentor.availableSlots.slice(0, 3).map((slot, index) => (
                  <div key={index} className="p-2 border rounded-lg">
                    <ArabicTypography variant="body2" className="font-semibold">
                      {new Date(slot.date).toLocaleDateString()}
                    </ArabicTypography>
                    <ArabicTypography variant="caption" color="secondary">
                      {slot.time} • {slot.duration} {language === 'ar' ? 'دقيقة' : 'min'}
                    </ArabicTypography>
                  </div>
                ))}
                <ArabicButton
                  variant="ghost"
                  size="sm"
                  className="w-full"
                  onClick={handleBookSession}
                >
                  {language === 'ar' ? 'عرض جميع المواعيد' : 'View All Slots'}
                </ArabicButton>
              </div>
            </ArabicCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MentorProfilePage;
