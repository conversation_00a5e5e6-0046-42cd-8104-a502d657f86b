#!/usr/bin/env python3
"""
Missing Pages Audit Script
Systematically checks which page components are imported in consolidatedRoutes.ts but don't exist
"""

import os
import re
from pathlib import Path

def extract_lazy_imports(file_path):
    """Extract all lazy import statements from consolidatedRoutes.ts"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all lazy import statements
        lazy_pattern = r"const\s+(\w+)\s*=\s*lazy\(\s*\(\)\s*=>\s*import\(['\"]([^'\"]+)['\"]\)\s*\)"
        matches = re.findall(lazy_pattern, content)
        
        return matches
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []

def check_file_exists(import_path, base_dir):
    """Check if the imported file actually exists"""
    # Convert import path to file path
    # Remove leading '../' and convert to actual path
    clean_path = import_path.replace('../', '')
    
    # Try different extensions
    possible_extensions = ['.tsx', '.ts', '.jsx', '.js']
    
    for ext in possible_extensions:
        full_path = os.path.join(base_dir, clean_path + ext)
        if os.path.exists(full_path):
            return True, full_path
    
    return False, None

def categorize_missing_pages(missing_pages):
    """Categorize missing pages by type"""
    categories = {
        'dashboard': [],
        'admin': [],
        'super_admin': [],
        'mentor': [],
        'investor': [],
        'entrepreneur': [],
        'moderator': [],
        'components': [],
        'other': []
    }
    
    for component_name, import_path in missing_pages:
        if 'dashboard' in import_path.lower():
            categories['dashboard'].append((component_name, import_path))
        elif 'admin' in import_path.lower() and 'super-admin' not in import_path.lower():
            categories['admin'].append((component_name, import_path))
        elif 'super-admin' in import_path.lower():
            categories['super_admin'].append((component_name, import_path))
        elif 'mentor' in import_path.lower():
            categories['mentor'].append((component_name, import_path))
        elif 'investor' in import_path.lower():
            categories['investor'].append((component_name, import_path))
        elif 'entrepreneur' in import_path.lower():
            categories['entrepreneur'].append((component_name, import_path))
        elif 'moderator' in import_path.lower():
            categories['moderator'].append((component_name, import_path))
        elif 'components' in import_path.lower():
            categories['components'].append((component_name, import_path))
        else:
            categories['other'].append((component_name, import_path))
    
    return categories

def suggest_solutions(categories):
    """Suggest solutions for missing pages"""
    solutions = []
    
    for category, pages in categories.items():
        if not pages:
            continue
            
        if category == 'dashboard':
            solutions.append({
                'category': 'Dashboard Pages',
                'count': len(pages),
                'priority': 'HIGH',
                'solution': 'Create missing dashboard pages or redirect to existing unified dashboard',
                'pages': pages
            })
        elif category == 'admin':
            solutions.append({
                'category': 'Admin Components',
                'count': len(pages),
                'priority': 'HIGH',
                'solution': 'Create missing admin components or use existing unified admin components',
                'pages': pages
            })
        elif category == 'components':
            solutions.append({
                'category': 'UI Components',
                'count': len(pages),
                'priority': 'MEDIUM',
                'solution': 'Create missing UI components or use existing alternatives',
                'pages': pages
            })
        else:
            solutions.append({
                'category': f'{category.title()} Pages',
                'count': len(pages),
                'priority': 'MEDIUM',
                'solution': f'Create missing {category} pages or redirect to existing pages',
                'pages': pages
            })
    
    return solutions

def main():
    """Main audit function"""
    print("🔍 MISSING PAGES AUDIT")
    print("=" * 60)
    
    # Path to the consolidatedRoutes.ts file
    routes_file = "frontend/src/routes/consolidatedRoutes.ts"
    base_dir = "frontend/src"
    
    if not os.path.exists(routes_file):
        print(f"❌ Could not find {routes_file}")
        return
    
    print(f"📁 Analyzing: {routes_file}")
    print(f"📁 Base directory: {base_dir}")
    
    # Extract all lazy imports
    lazy_imports = extract_lazy_imports(routes_file)
    print(f"📊 Found {len(lazy_imports)} lazy import statements")
    
    # Check which files exist
    existing_pages = []
    missing_pages = []
    
    for component_name, import_path in lazy_imports:
        exists, full_path = check_file_exists(import_path, base_dir)
        if exists:
            existing_pages.append((component_name, import_path, full_path))
        else:
            missing_pages.append((component_name, import_path))
    
    print(f"\n📈 SUMMARY:")
    print(f"   ✅ Existing pages: {len(existing_pages)}")
    print(f"   ❌ Missing pages: {len(missing_pages)}")
    print(f"   📊 Total coverage: {len(existing_pages)}/{len(lazy_imports)} ({len(existing_pages)/len(lazy_imports)*100:.1f}%)")
    
    if missing_pages:
        print(f"\n❌ MISSING PAGES ({len(missing_pages)}):")
        print("-" * 60)
        
        # Categorize missing pages
        categories = categorize_missing_pages(missing_pages)
        
        for category, pages in categories.items():
            if pages:
                print(f"\n📂 {category.upper()} ({len(pages)} missing):")
                for component_name, import_path in pages:
                    expected_path = import_path.replace('../', 'frontend/src/') + '.tsx'
                    print(f"   ❌ {component_name}")
                    print(f"      Import: {import_path}")
                    print(f"      Expected: {expected_path}")
        
        # Suggest solutions
        print(f"\n💡 SUGGESTED SOLUTIONS:")
        print("-" * 60)
        solutions = suggest_solutions(categories)
        
        for solution in solutions:
            print(f"\n🎯 {solution['category']} - Priority: {solution['priority']}")
            print(f"   Count: {solution['count']} missing")
            print(f"   Solution: {solution['solution']}")
            print(f"   Pages: {', '.join([p[0] for p in solution['pages'][:3]])}{'...' if len(solution['pages']) > 3 else ''}")
    
    else:
        print(f"\n🎉 ALL PAGES EXIST! No missing page components found.")
    
    # Show some existing pages for reference
    if existing_pages:
        print(f"\n✅ SAMPLE EXISTING PAGES:")
        print("-" * 60)
        for component_name, import_path, full_path in existing_pages[:10]:
            print(f"   ✅ {component_name} -> {full_path}")
        if len(existing_pages) > 10:
            print(f"   ... and {len(existing_pages) - 10} more")
    
    return {
        'total_imports': len(lazy_imports),
        'existing_count': len(existing_pages),
        'missing_count': len(missing_pages),
        'missing_pages': missing_pages,
        'existing_pages': existing_pages,
        'categories': categorize_missing_pages(missing_pages) if missing_pages else {}
    }

if __name__ == "__main__":
    results = main()
    
    # Exit with error code if there are missing pages
    if results['missing_count'] > 0:
        print(f"\n⚠️  Found {results['missing_count']} missing pages that need attention!")
        exit(1)
    else:
        print(f"\n✅ All {results['total_imports']} page imports are valid!")
        exit(0)
