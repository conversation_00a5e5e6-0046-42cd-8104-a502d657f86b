# Generated by Django 5.2.1 on 2025-07-22 12:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AIModelMetadata",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("version", models.Char<PERSON>ield(max_length=20)),
                ("description", models.TextField()),
                ("model_type", models.CharField(max_length=50)),
                ("file_path", models.CharField(max_length=255)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "AI Model Metadata",
                "verbose_name_plural": "AI Model Metadata",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PredictionLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("model_name", models.CharField(max_length=100)),
                ("input_data", models.JSONField()),
                ("prediction_result", models.JSONField()),
                ("confidence_score", models.FloatField(blank=True, null=True)),
                (
                    "processing_time",
                    models.FloatField(help_text="Processing time in seconds"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Prediction Log",
                "verbose_name_plural": "Prediction Logs",
                "ordering": ["-created_at"],
            },
        ),
    ]
