/**
 * Funding Discovery Page
 * Discover funding opportunities, investors, and grants for startups
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography, ArabicInput } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Search, 
  Filter, 
  DollarSign, 
  TrendingUp, 
  Users, 
  Building,
  Award,
  Clock,
  MapPin,
  Eye,
  Heart,
  Star,
  Target,
  Briefcase
} from 'lucide-react';

interface FundingOpportunity {
  id: string;
  name: string;
  type: 'vc' | 'angel' | 'grant' | 'accelerator' | 'crowdfunding';
  description: string;
  descriptionAr: string;
  fundingRange: {
    min: number;
    max: number;
  };
  stage: string[];
  industry: string[];
  location: string;
  requirements: string[];
  applicationDeadline?: string;
  responseTime: string;
  successRate: number;
  portfolioCount: number;
  averageTicketSize: number;
  website: string;
  contactEmail: string;
  logo?: string;
  matchScore?: number;
  tags: string[];
  isActive: boolean;
}

interface FilterOptions {
  type: string;
  stage: string;
  industry: string;
  location: string;
  minAmount: number;
  maxAmount: number;
  activeOnly: boolean;
}

const FundingDiscoveryPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [opportunities, setOpportunities] = useState<FundingOpportunity[]>([]);
  const [filteredOpportunities, setFilteredOpportunities] = useState<FundingOpportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    type: '',
    stage: '',
    industry: '',
    location: '',
    minAmount: 0,
    maxAmount: 0,
    activeOnly: true
  });

  useEffect(() => {
    loadFundingOpportunities();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [searchQuery, filters, opportunities]);

  const loadFundingOpportunities = async () => {
    try {
      // Mock data - replace with actual API call
      const mockOpportunities: FundingOpportunity[] = [
        {
          id: '1',
          name: 'MENA Ventures',
          type: 'vc',
          description: 'Leading VC fund focused on early-stage startups in MENA region',
          descriptionAr: 'صندوق استثمار رائد يركز على الشركات الناشئة في مراحلها المبكرة في منطقة الشرق الأوسط وشمال أفريقيا',
          fundingRange: { min: 100000, max: 2000000 },
          stage: ['Seed', 'Series A'],
          industry: ['FinTech', 'HealthTech', 'EdTech'],
          location: 'Dubai, UAE',
          requirements: ['MVP ready', 'Traction metrics', 'Strong team'],
          responseTime: '2-4 weeks',
          successRate: 15,
          portfolioCount: 45,
          averageTicketSize: 500000,
          website: 'https://menaventures.com',
          contactEmail: '<EMAIL>',
          matchScore: 92,
          tags: ['Tech-focused', 'Regional expertise', 'Post-investment support'],
          isActive: true
        },
        {
          id: '2',
          name: 'Saudi Vision 2030 Fund',
          type: 'grant',
          description: 'Government grant program supporting innovative startups aligned with Vision 2030',
          descriptionAr: 'برنامج منح حكومي يدعم الشركات الناشئة المبتكرة المتماشية مع رؤية 2030',
          fundingRange: { min: 50000, max: 500000 },
          stage: ['Pre-Seed', 'Seed'],
          industry: ['CleanTech', 'HealthTech', 'EdTech', 'FinTech'],
          location: 'Riyadh, Saudi Arabia',
          requirements: ['Saudi-based', 'Innovation focus', 'Vision 2030 alignment'],
          applicationDeadline: '2024-03-31',
          responseTime: '6-8 weeks',
          successRate: 25,
          portfolioCount: 120,
          averageTicketSize: 200000,
          website: 'https://vision2030.gov.sa',
          contactEmail: '<EMAIL>',
          matchScore: 88,
          tags: ['Government backed', 'Non-dilutive', 'Strategic support'],
          isActive: true
        },
        {
          id: '3',
          name: 'Cairo Angels',
          type: 'angel',
          description: 'Network of angel investors supporting Egyptian and regional startups',
          descriptionAr: 'شبكة من المستثمرين الملائكيين الذين يدعمون الشركات الناشئة المصرية والإقليمية',
          fundingRange: { min: 25000, max: 250000 },
          stage: ['Pre-Seed', 'Seed'],
          industry: ['E-commerce', 'FinTech', 'EdTech', 'FoodTech'],
          location: 'Cairo, Egypt',
          requirements: ['Local presence', 'Scalable model', 'Experienced team'],
          responseTime: '3-5 weeks',
          successRate: 20,
          portfolioCount: 78,
          averageTicketSize: 75000,
          website: 'https://cairoangels.com',
          contactEmail: '<EMAIL>',
          matchScore: 85,
          tags: ['Local network', 'Mentorship included', 'Quick decisions'],
          isActive: true
        },
        {
          id: '4',
          name: 'Techstars Dubai',
          type: 'accelerator',
          description: 'Global accelerator program with strong MENA presence and network',
          descriptionAr: 'برنامج تسريع عالمي مع حضور قوي في منطقة الشرق الأوسط وشمال أفريقيا',
          fundingRange: { min: 100000, max: 120000 },
          stage: ['Pre-Seed', 'Seed'],
          industry: ['All verticals'],
          location: 'Dubai, UAE',
          requirements: ['3-month commitment', 'Relocate to Dubai', 'High growth potential'],
          applicationDeadline: '2024-02-15',
          responseTime: '4-6 weeks',
          successRate: 8,
          portfolioCount: 200,
          averageTicketSize: 110000,
          website: 'https://techstars.com/dubai',
          contactEmail: '<EMAIL>',
          matchScore: 78,
          tags: ['Global network', 'Intensive program', 'Demo day'],
          isActive: true
        }
      ];

      setOpportunities(mockOpportunities);
      setFilteredOpportunities(mockOpportunities);
    } catch (error) {
      console.error('Error loading funding opportunities:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = opportunities;

    // Search query filter
    if (searchQuery) {
      filtered = filtered.filter(opp =>
        opp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        opp.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        opp.industry.some(ind => ind.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Type filter
    if (filters.type) {
      filtered = filtered.filter(opp => opp.type === filters.type);
    }

    // Stage filter
    if (filters.stage) {
      filtered = filtered.filter(opp => opp.stage.includes(filters.stage));
    }

    // Industry filter
    if (filters.industry) {
      filtered = filtered.filter(opp => opp.industry.includes(filters.industry));
    }

    // Amount range filter
    if (filters.minAmount > 0) {
      filtered = filtered.filter(opp => opp.fundingRange.max >= filters.minAmount);
    }
    if (filters.maxAmount > 0) {
      filtered = filtered.filter(opp => opp.fundingRange.min <= filters.maxAmount);
    }

    // Active only filter
    if (filters.activeOnly) {
      filtered = filtered.filter(opp => opp.isActive);
    }

    setFilteredOpportunities(filtered);
  };

  const formatCurrency = (amount: number): string => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(0)}K`;
    }
    return `$${amount.toLocaleString()}`;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'vc':
        return 'bg-blue-100 text-blue-800';
      case 'angel':
        return 'bg-green-100 text-green-800';
      case 'grant':
        return 'bg-purple-100 text-purple-800';
      case 'accelerator':
        return 'bg-orange-100 text-orange-800';
      case 'crowdfunding':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeText = (type: string) => {
    const types = {
      en: {
        vc: 'Venture Capital',
        angel: 'Angel Investor',
        grant: 'Grant',
        accelerator: 'Accelerator',
        crowdfunding: 'Crowdfunding'
      },
      ar: {
        vc: 'رأس مال مخاطر',
        angel: 'مستثمر ملائكي',
        grant: 'منحة',
        accelerator: 'مسرع أعمال',
        crowdfunding: 'تمويل جماعي'
      }
    };
    return types[language][type as keyof typeof types.en] || type;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <ArabicTypography variant="h1" className="text-gray-900 font-bold mb-2">
            {language === 'ar' ? 'اكتشف فرص التمويل' : 'Discover Funding Opportunities'}
          </ArabicTypography>
          <ArabicTypography variant="body1" color="secondary">
            {language === 'ar' 
              ? 'اعثر على المستثمرين والمنح والمسرعات المناسبة لشركتك الناشئة'
              : 'Find the right investors, grants, and accelerators for your startup'
            }
          </ArabicTypography>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8">
          <div className={`flex flex-col md:flex-row gap-4 ${isRTL ? 'md:flex-row-reverse' : ''}`}>
            <div className="flex-1">
              <ArabicInput
                placeholder={language === 'ar' ? 'ابحث عن فرص التمويل...' : 'Search funding opportunities...'}
                value={searchQuery}
                onChange={setSearchQuery}
                icon={<Search className="w-5 h-5" />}
                iconPosition="start"
              />
            </div>
            <ArabicButton
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              icon={<Filter className="w-4 h-4" />}
            >
              {language === 'ar' ? 'تصفية' : 'Filters'}
            </ArabicButton>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <ArabicCard className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'نوع التمويل' : 'Funding Type'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.type}
                    onChange={(e) => setFilters({...filters, type: e.target.value})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'جميع الأنواع' : 'All Types'}</option>
                    <option value="vc">{getTypeText('vc')}</option>
                    <option value="angel">{getTypeText('angel')}</option>
                    <option value="grant">{getTypeText('grant')}</option>
                    <option value="accelerator">{getTypeText('accelerator')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'مرحلة الشركة' : 'Company Stage'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.stage}
                    onChange={(e) => setFilters({...filters, stage: e.target.value})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'جميع المراحل' : 'All Stages'}</option>
                    <option value="Pre-Seed">{language === 'ar' ? 'ما قبل البذرة' : 'Pre-Seed'}</option>
                    <option value="Seed">{language === 'ar' ? 'البذرة' : 'Seed'}</option>
                    <option value="Series A">{language === 'ar' ? 'الجولة أ' : 'Series A'}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'الصناعة' : 'Industry'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={filters.industry}
                    onChange={(e) => setFilters({...filters, industry: e.target.value})}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'جميع الصناعات' : 'All Industries'}</option>
                    <option value="FinTech">{language === 'ar' ? 'التكنولوجيا المالية' : 'FinTech'}</option>
                    <option value="HealthTech">{language === 'ar' ? 'التكنولوجيا الصحية' : 'HealthTech'}</option>
                    <option value="EdTech">{language === 'ar' ? 'التكنولوجيا التعليمية' : 'EdTech'}</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <ArabicButton
                    variant="outline"
                    onClick={() => setFilters({
                      type: '',
                      stage: '',
                      industry: '',
                      location: '',
                      minAmount: 0,
                      maxAmount: 0,
                      activeOnly: true
                    })}
                    className="w-full"
                  >
                    {language === 'ar' ? 'مسح الفلاتر' : 'Clear Filters'}
                  </ArabicButton>
                </div>
              </div>
            </ArabicCard>
          )}
        </div>

        {/* Results Header */}
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicTypography variant="h4" className="text-gray-900 font-semibold">
            {language === 'ar' 
              ? `${filteredOpportunities.length} فرصة تمويل متاحة`
              : `${filteredOpportunities.length} funding opportunities available`
            }
          </ArabicTypography>
          <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
            <span className="text-sm text-gray-500 font-arabic">
              {language === 'ar' ? 'ترتيب حسب:' : 'Sort by:'}
            </span>
            <select className="text-sm border border-gray-300 rounded px-2 py-1 font-arabic">
              <option>{language === 'ar' ? 'الأفضل تطابقاً' : 'Best Match'}</option>
              <option>{language === 'ar' ? 'أعلى مبلغ' : 'Highest Amount'}</option>
              <option>{language === 'ar' ? 'معدل النجاح' : 'Success Rate'}</option>
            </select>
          </div>
        </div>

        {/* Opportunities Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredOpportunities.map((opportunity) => (
            <ArabicCard key={opportunity.id} className="hover:shadow-lg transition-shadow">
              <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="flex-1">
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} mb-3`}>
                    <div className={`w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                      <Building className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <ArabicTypography variant="h5" className="text-gray-900 font-bold">
                        {opportunity.name}
                      </ArabicTypography>
                      <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getTypeColor(opportunity.type)} font-arabic`}>
                        {getTypeText(opportunity.type)}
                      </span>
                    </div>
                  </div>

                  {/* Match Score */}
                  {opportunity.matchScore && (
                    <div className="mb-3">
                      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold ${
                        opportunity.matchScore >= 90 ? 'bg-green-100 text-green-800' :
                        opportunity.matchScore >= 80 ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        <Target className={`w-4 h-4 ${isRTL ? 'ml-1' : 'mr-1'}`} />
                        <span className="font-arabic">
                          {opportunity.matchScore}% {language === 'ar' ? 'تطابق' : 'match'}
                        </span>
                      </div>
                    </div>
                  )}

                  <ArabicTypography variant="body2" color="secondary" className="mb-4">
                    {language === 'ar' ? opportunity.descriptionAr : opportunity.description}
                  </ArabicTypography>

                  {/* Key Metrics */}
                  <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                    <div>
                      <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <DollarSign className="w-4 h-4 text-green-500" />
                        <span className={`text-sm font-semibold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                          {formatCurrency(opportunity.averageTicketSize)}
                        </span>
                      </div>
                      <ArabicTypography variant="caption" color="secondary">
                        {language === 'ar' ? 'متوسط الاستثمار' : 'Avg. Investment'}
                      </ArabicTypography>
                    </div>
                    
                    <div>
                      <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <TrendingUp className="w-4 h-4 text-blue-500" />
                        <span className={`text-sm font-semibold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                          {opportunity.successRate}%
                        </span>
                      </div>
                      <ArabicTypography variant="caption" color="secondary">
                        {language === 'ar' ? 'معدل النجاح' : 'Success Rate'}
                      </ArabicTypography>
                    </div>
                    
                    <div>
                      <div className={`flex items-center justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Briefcase className="w-4 h-4 text-purple-500" />
                        <span className={`text-sm font-semibold ${isRTL ? 'mr-1' : 'ml-1'} font-arabic`}>
                          {opportunity.portfolioCount}
                        </span>
                      </div>
                      <ArabicTypography variant="caption" color="secondary">
                        {language === 'ar' ? 'المحفظة' : 'Portfolio'}
                      </ArabicTypography>
                    </div>
                  </div>

                  {/* Funding Range */}
                  <div className="mb-4">
                    <ArabicTypography variant="body2" className="font-semibold text-gray-900 mb-1">
                      {language === 'ar' ? 'نطاق التمويل:' : 'Funding Range:'}
                    </ArabicTypography>
                    <ArabicTypography variant="body1" className="text-blue-600 font-bold">
                      {formatCurrency(opportunity.fundingRange.min)} - {formatCurrency(opportunity.fundingRange.max)}
                    </ArabicTypography>
                  </div>

                  {/* Industries and Stages */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2 mb-2">
                      {opportunity.industry.slice(0, 3).map((industry, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full font-arabic"
                        >
                          {industry}
                        </span>
                      ))}
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {opportunity.stage.map((stage, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-green-50 text-green-700 text-xs rounded-full font-arabic"
                        >
                          {stage}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Location and Response Time */}
                  <div className="space-y-2 mb-4">
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <MapPin className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <ArabicTypography variant="body2" color="secondary">
                        {opportunity.location}
                      </ArabicTypography>
                    </div>
                    
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Clock className={`w-4 h-4 text-gray-500 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      <ArabicTypography variant="body2" color="secondary">
                        {language === 'ar' ? 'وقت الرد:' : 'Response time:'} {opportunity.responseTime}
                      </ArabicTypography>
                    </div>
                  </div>

                  {/* Application Deadline */}
                  {opportunity.applicationDeadline && (
                    <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Clock className={`w-4 h-4 text-yellow-600 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <ArabicTypography variant="body2" className="text-yellow-800 font-semibold">
                          {language === 'ar' ? 'آخر موعد للتقديم:' : 'Application deadline:'} {new Date(opportunity.applicationDeadline).toLocaleDateString()}
                        </ArabicTypography>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className={`flex space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                    <ArabicButton
                      size="sm"
                      className="flex-1"
                      onClick={() => navigate(`/dashboard/funding/apply/${opportunity.id}`)}
                    >
                      {language === 'ar' ? 'تقدم الآن' : 'Apply Now'}
                    </ArabicButton>
                    
                    <ArabicButton
                      size="sm"
                      variant="outline"
                      onClick={() => navigate(`/dashboard/funding/opportunity/${opportunity.id}`)}
                      icon={<Eye className="w-4 h-4" />}
                    >
                      {language === 'ar' ? 'عرض' : 'View'}
                    </ArabicButton>
                    
                    <ArabicButton
                      size="sm"
                      variant="outline"
                      icon={<Heart className="w-4 h-4" />}
                    >
                      {language === 'ar' ? 'حفظ' : 'Save'}
                    </ArabicButton>
                  </div>
                </div>
              </div>
            </ArabicCard>
          ))}
        </div>

        {/* No Results */}
        {filteredOpportunities.length === 0 && (
          <div className="text-center py-12">
            <DollarSign className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <ArabicTypography variant="h4" className="text-gray-500 mb-2">
              {language === 'ar' ? 'لم يتم العثور على فرص تمويل' : 'No funding opportunities found'}
            </ArabicTypography>
            <ArabicTypography variant="body1" color="secondary">
              {language === 'ar' 
                ? 'جرب تعديل معايير البحث أو الفلاتر'
                : 'Try adjusting your search criteria or filters'
              }
            </ArabicTypography>
          </div>
        )}
      </div>
    </div>
  );
};

export default FundingDiscoveryPage;
