#!/usr/bin/env python3
"""
Database Population Script
Creates sample data for testing the real API endpoints
"""

import os
import sys
import django
from django.utils import timezone
from datetime import timedelta
import random

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from incubator.models import BusinessIdea, IncubatorResource, FundingOpportunity
from api.models import Post, Event, Resource, Tag
from users.models import UserProfile

def create_users():
    """Create sample users with different roles"""
    print("👥 Creating sample users...")
    
    users_data = [
        {'username': 'admin_user', 'email': '<EMAIL>', 'first_name': 'Admin', 'last_name': 'User', 'is_staff': True, 'is_superuser': True},
        {'username': 'john_mentor', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': '<PERSON>'},
        {'username': 'sarah_investor', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': '<PERSON>'},
        {'username': 'mike_entrepreneur', 'email': '<EMAIL>', 'first_name': 'Mike', 'last_name': 'Wilson'},
        {'username': 'lisa_moderator', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': 'Brown'},
    ]
    
    created_users = []
    for user_data in users_data:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'email': user_data['email'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'is_staff': user_data.get('is_staff', False),
                'is_superuser': user_data.get('is_superuser', False),
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
            print(f"  ✅ Created user: {user.username}")
        else:
            print(f"  ℹ️  User exists: {user.username}")
        
        created_users.append(user)
    
    return created_users

def create_business_ideas(users):
    """Create sample business ideas"""
    print("💡 Creating sample business ideas...")
    
    ideas_data = [
        {
            'title': 'AI-Powered Learning Platform',
            'description': 'An innovative platform that uses artificial intelligence to personalize learning experiences for students of all ages.',
            'industry': 'EdTech',
            'current_stage': 'MVP',
            'funding_needed': 500000,
            'status': 'approved'
        },
        {
            'title': 'Sustainable Fashion Marketplace',
            'description': 'A marketplace connecting eco-conscious consumers with sustainable fashion brands and second-hand clothing.',
            'industry': 'Fashion',
            'current_stage': 'Idea',
            'funding_needed': 250000,
            'status': 'pending'
        },
        {
            'title': 'Smart Home Energy Management',
            'description': 'IoT solution for optimizing home energy consumption and reducing utility costs through smart automation.',
            'industry': 'Technology',
            'current_stage': 'Prototype',
            'funding_needed': 750000,
            'status': 'approved'
        },
        {
            'title': 'Local Food Delivery Network',
            'description': 'Connecting local farmers and restaurants with consumers for fresh, locally-sourced food delivery.',
            'industry': 'Food & Beverage',
            'current_stage': 'Launch',
            'funding_needed': 300000,
            'status': 'approved'
        }
    ]
    
    created_ideas = []
    for i, idea_data in enumerate(ideas_data):
        user = users[i % len(users)]  # Distribute ideas among users
        
        idea, created = BusinessIdea.objects.get_or_create(
            title=idea_data['title'],
            defaults={
                'user': user,
                'description': idea_data['description'],
                'industry': idea_data['industry'],
                'current_stage': idea_data['current_stage'],
                'funding_needed': idea_data['funding_needed'],
                'status': idea_data['status'],
                'created_at': timezone.now() - timedelta(days=random.randint(1, 30))
            }
        )
        
        if created:
            print(f"  ✅ Created business idea: {idea.title}")
        else:
            print(f"  ℹ️  Business idea exists: {idea.title}")
        
        created_ideas.append(idea)
    
    return created_ideas

def create_posts(users):
    """Create sample forum posts"""
    print("📝 Creating sample posts...")
    
    posts_data = [
        {
            'title': 'How to validate your business idea?',
            'content': 'I have a great business idea but I\'m not sure how to validate it. What are the best methods to test market demand?',
            'is_flagged': False
        },
        {
            'title': 'Looking for co-founder in tech',
            'content': 'I\'m building a SaaS platform and looking for a technical co-founder. Anyone interested in joining a promising startup?',
            'is_flagged': False
        },
        {
            'title': 'Spam post about get rich quick',
            'content': 'Make money fast with this amazing opportunity! Click here to learn more!',
            'is_flagged': True
        }
    ]
    
    created_posts = []
    for i, post_data in enumerate(posts_data):
        user = users[i % len(users)]
        
        post, created = Post.objects.get_or_create(
            title=post_data['title'],
            defaults={
                'author': user,
                'content': post_data['content'],
                'is_flagged': post_data['is_flagged'],
                'created_at': timezone.now() - timedelta(days=random.randint(1, 15))
            }
        )
        
        if created:
            print(f"  ✅ Created post: {post.title}")
        else:
            print(f"  ℹ️  Post exists: {post.title}")
        
        created_posts.append(post)
    
    return created_posts

def create_resources():
    """Create sample incubator resources"""
    print("📚 Creating sample resources...")
    
    resources_data = [
        {
            'title': 'Business Plan Template',
            'description': 'A comprehensive template for creating professional business plans.',
            'resource_type': 'template',
            'category': 'planning'
        },
        {
            'title': 'Market Research Guide',
            'description': 'Step-by-step guide for conducting effective market research.',
            'resource_type': 'article',
            'category': 'validation'
        },
        {
            'title': 'Pitch Deck Essentials',
            'description': 'Learn how to create compelling pitch decks that attract investors.',
            'resource_type': 'video',
            'category': 'finance'
        }
    ]
    
    created_resources = []
    for resource_data in resources_data:
        resource, created = IncubatorResource.objects.get_or_create(
            title=resource_data['title'],
            defaults={
                'description': resource_data['description'],
                'resource_type': resource_data['resource_type'],
                'category': resource_data['category'],
                'is_featured': True,
                'created_at': timezone.now() - timedelta(days=random.randint(1, 60))
            }
        )
        
        if created:
            print(f"  ✅ Created resource: {resource.title}")
        else:
            print(f"  ℹ️  Resource exists: {resource.title}")
        
        created_resources.append(resource)
    
    return created_resources

def main():
    """Main function to populate test data"""
    print("🚀 Starting Database Population...")
    print("=" * 50)
    
    try:
        # Create users
        users = create_users()
        
        # Create business ideas
        business_ideas = create_business_ideas(users)
        
        # Create posts
        posts = create_posts(users)
        
        # Create resources
        resources = create_resources()
        
        print("\n🎉 Database Population Complete!")
        print(f"Created: {len(users)} users, {len(business_ideas)} business ideas, {len(posts)} posts, {len(resources)} resources")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error during database population: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
