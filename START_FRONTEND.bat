@echo off
echo ⚛️ Starting React Frontend Server...
echo.

REM Get the directory where this batch file is located
set "PROJECT_DIR=%~dp0"
echo Project directory: %PROJECT_DIR%

REM Navigate to frontend directory
cd /d "%PROJECT_DIR%frontend"
echo Current directory: %CD%

REM Check if package.json exists
if exist "package.json" (
    echo ✅ Found package.json
    echo Starting React development server...
    echo.
    npm run dev
) else (
    echo ❌ package.json not found in frontend directory
    echo Please make sure you're running this from the project root directory
    dir
)

echo.
echo Press any key to exit...
pause >nul
