#!/usr/bin/env python3
"""
Quick script to check existing users and their credentials
"""

import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from django.contrib.auth import authenticate

def check_users():
    """Check all users and test authentication"""
    print("🔍 Checking all users in the database...")
    
    users = User.objects.all().order_by('username')
    
    print(f"\n📊 Total users found: {users.count()}")
    print("\n👥 User List:")
    print("-" * 80)
    
    for user in users:
        print(f"👤 {user.username}")
        print(f"   Email: {user.email}")
        print(f"   Active: {user.is_active}")
        print(f"   Staff: {user.is_staff}")
        print(f"   Superuser: {user.is_superuser}")
        print(f"   Last login: {user.last_login}")
        print(f"   Date joined: {user.date_joined}")
        
        # Check profile
        if hasattr(user, 'profile'):
            print(f"   Profile role: {user.profile.requested_role_name}")
        else:
            print(f"   Profile: No profile")
            
        # Check approval status
        try:
            approval = user.approval_status
            print(f"   Approval: {approval.status}")
        except:
            print(f"   Approval: No approval record")
        
        print()

def test_login_credentials():
    """Test login with common credentials"""
    print("\n🧪 Testing common login credentials...")
    
    test_credentials = [
        ('admin', 'admin123'),
        ('testadmin', 'testpass123'),
        ('testsuperadmin', 'testpass123'),
        ('superadmin', 'superadmin123'),
        ('testuser', 'testpass123'),
        ('testentrepreneur', 'testpass123'),
        ('entrepreneur1', 'entrepreneur123'),
    ]
    
    for username, password in test_credentials:
        try:
            user = authenticate(username=username, password=password)
            if user:
                if user.is_active:
                    print(f"✅ {username} / {password} - LOGIN SUCCESS")
                else:
                    print(f"⚠️  {username} / {password} - User exists but INACTIVE")
            else:
                # Check if user exists
                if User.objects.filter(username=username).exists():
                    print(f"❌ {username} / {password} - User exists but WRONG PASSWORD")
                else:
                    print(f"❌ {username} / {password} - USER DOES NOT EXIST")
        except Exception as e:
            print(f"❌ {username} / {password} - ERROR: {e}")

if __name__ == '__main__':
    check_users()
    test_login_credentials()
