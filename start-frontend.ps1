# PowerShell script to start the frontend server
Write-Host "⚛️ Starting React Frontend Server..." -ForegroundColor Green

# Get the current directory
$currentDir = Get-Location
Write-Host "Current directory: $currentDir" -ForegroundColor Yellow

# Navigate to frontend directory
$frontendPath = Join-Path $currentDir "frontend"
Write-Host "Frontend path: $frontendPath" -ForegroundColor Yellow

if (Test-Path $frontendPath) {
    Set-Location $frontendPath
    Write-Host "✅ Found frontend directory" -ForegroundColor Green
    
    # Check if package.json exists
    if (Test-Path "package.json") {
        Write-Host "✅ Found package.json" -ForegroundColor Green
        Write-Host "Starting React development server..." -ForegroundColor Cyan
        
        # Start the React server
        npm run dev
    } else {
        Write-Host "❌ package.json not found in frontend directory" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Frontend directory not found: $frontendPath" -ForegroundColor Red
    Write-Host "Available directories:" -ForegroundColor Yellow
    Get-ChildItem -Directory | Select-Object Name
}

# Keep the window open
Read-Host "Press Enter to exit"
