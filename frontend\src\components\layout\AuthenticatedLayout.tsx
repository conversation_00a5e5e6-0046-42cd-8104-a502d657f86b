import React, { useState } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import UniversalSidebar from './UniversalSidebar';
import { AIStatusIndicator } from '../ai/AIStatusIndicator';
import { <PERSON><PERSON>, <PERSON>, User } from 'lucide-react';
import { useAppSelector } from '../../store/hooks';

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
}

/**
 * ✅ SIMPLE REDUX-BASED AuthenticatedLayout
 * Clean, minimal layout using Redux state management
 * Shows: Sidebar + Content (no footer, no complex logic)
 */
const AuthenticatedLayout: React.FC<AuthenticatedLayoutProps> = ({ children }) => {
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isDesktopSidebarCollapsed, setIsDesktopSidebarCollapsed] = useState(false);

  return (
    <div className={`min-h-screen flex ${isRTL ? "flex-row-reverse" : ""}`}>
      {/* ✅ CLEAN REDUX SIDEBAR */}
      <UniversalSidebar
        variant="desktop"
        isCollapsed={isDesktopSidebarCollapsed}
        onToggle={() => setIsDesktopSidebarCollapsed(!isDesktopSidebarCollapsed)}
      />

      {/* Mobile Sidebar */}
      {isMobileSidebarOpen && (
        <UniversalSidebar
          variant="mobile"
          isOpen={isMobileSidebarOpen}
          onClose={() => setIsMobileSidebarOpen(false)}
        />
      )}

      {/* ✅ CLEAN CONTENT AREA */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* ✅ HEADER WITH AI CHAT DROPDOWN */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileSidebarOpen(true)}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              aria-label="Open Menu"
            >
              <Menu className="w-6 h-6 text-gray-600 dark:text-gray-300" />
            </button>

            {/* Page Title */}
            <div className="flex-1 lg:flex-none">
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
                {user?.first_name ? `Welcome, ${user.first_name}` : 'Dashboard'}
              </h1>
            </div>

            {/* ✅ AI CHAT DROPDOWN */}
            <div className="flex items-center space-x-4">
              <AIStatusIndicator />

              {/* User Profile */}
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <span className="hidden md:block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {user?.username || 'User'}
                </span>
              </div>
            </div>
          </div>
        </header>

        {/* ✅ MAIN CONTENT WITH PROPER RTL SPACING */}
        <main className={`flex-grow transition-all duration-300 ease-in-out ${
          isRTL
            ? (isDesktopSidebarCollapsed ? 'lg:mr-16' : 'lg:mr-80')
            : (isDesktopSidebarCollapsed ? 'lg:ml-16' : 'lg:ml-80')
        }`} role="main">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AuthenticatedLayout;
