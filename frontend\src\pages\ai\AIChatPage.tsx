/**
 * Clean AI Chat Page
 * Full-width chat interface without sidebar duplicates
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import EnhancedAIChat from '../../components/ai/EnhancedAIChat';

const AIChatPage: React.FC = () => {
  const { isRTL } = useLanguage();

  // ✅ SIMPLIFIED STATE - NO SIDEBAR FEATURES
  const [chatStats, setChatStats] = useState({
    messageCount: 0,
    sessionDuration: 0,
    isConnected: false
  });

  // ✅ NEW CHAT SESSION STATE (FOR HEADER DROPDOWN INTEGRATION)
  const [shouldStartNewChat, setShouldStartNewChat] = useState(false);
  const [featureMessage, setFeatureMessage] = useState('');

  // ✅ SYRIAN CHAT TYPE STATE
  const [chatType, setChatType] = useState<'syrian_business' | 'damascus_local' | 'aleppo_local' | 'general'>('syrian_business');

  // ✅ FORCE CHAT RESTART WHEN CHAT TYPE CHANGES
  const [chatKey, setChatKey] = useState(0);

  // ✅ HANDLE CHAT TYPE CHANGES - RESTART CHAT WITH NEW CONTEXT
  useEffect(() => {
    // Force chat component to restart with new Syrian context
    setChatKey(prev => prev + 1);
    setShouldStartNewChat(true);
  }, [chatType]);

  // ✅ STATS UPDATE HANDLER
  const handleStatsUpdate = (stats: any) => {
    setChatStats(stats);
  };

  // ✅ REMOVED FEATURE CLICK HANDLER - NO SIDEBAR FEATURES

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900 p-2 sm:p-4">
      <div className="max-w-7xl mx-auto h-screen flex flex-col">
        {/* ✅ REMOVED DUPLICATE HEADER - USING COMPONENT HEADER ONLY */}



        {/* ✅ CLEAN FULL-WIDTH CHAT INTERFACE - NO SIDEBAR DUPLICATES */}
        <div className="flex-1 w-full">
          <EnhancedAIChat
            key={chatKey} // ✅ FORCE RE-RENDER WHEN CHAT TYPE CHANGES
            height="h-[600px] sm:h-[700px]"
            className="w-full"
            showFeatures={true}
            onStatsUpdate={(stats: any) => setChatStats(stats)}
            featureMessage={featureMessage}
            onFeatureMessageProcessed={() => setFeatureMessage('')}
            startNewChat={shouldStartNewChat}
            onNewChatStarted={() => setShouldStartNewChat(false)}
            chatType={chatType}
          />
        </div>
      </div>
    </div>
  );
};

// Function removed - welcome messages now handled in EnhancedAIChat component

export default AIChatPage;
