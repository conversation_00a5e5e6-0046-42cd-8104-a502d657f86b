/**
 * Clean AI Chat Page
 * Full-width chat interface without sidebar duplicates
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import EnhancedAIChat from '../../components/ai/EnhancedAIChat';

const AIChatPage: React.FC = () => {
  const { isRTL } = useLanguage();

  // ✅ SIMPLIFIED STATE - NO SIDEBAR FEATURES
  const [chatStats, setChatStats] = useState({
    messageCount: 0,
    sessionDuration: 0,
    isConnected: false
  });

  // ✅ NEW CHAT SESSION STATE (FOR HEADER DROPDOWN INTEGRATION)
  const [shouldStartNewChat, setShouldStartNewChat] = useState(false);
  const [featureMessage, setFeatureMessage] = useState('');

  // ✅ SYRIAN CHAT TYPE STATE
  const [chatType, setChatType] = useState<'syrian_business' | 'damascus_local' | 'aleppo_local' | 'general'>('syrian_business');

  // ✅ FORCE CHAT RESTART WHEN CHAT TYPE CHANGES
  const [chatKey, setChatKey] = useState(0);

  // ✅ HANDLE CHAT TYPE CHANGES - RESTART CHAT WITH NEW CONTEXT
  useEffect(() => {
    // Force chat component to restart with new Syrian context
    setChatKey(prev => prev + 1);
    setShouldStartNewChat(true);
  }, [chatType]);

  // ✅ STATS UPDATE HANDLER
  const handleStatsUpdate = (stats: any) => {
    setChatStats(stats);
  };

  // ✅ REMOVED FEATURE CLICK HANDLER - NO SIDEBAR FEATURES

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900 p-2 sm:p-4">
      <div className="max-w-7xl mx-auto h-screen flex flex-col">
        {/* ✅ REMOVED DUPLICATE HEADER - USING COMPONENT HEADER ONLY */}

        {/* ✅ SYRIAN CHAT TYPE SELECTOR */}
        <div className="mb-4">
          <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-4">
            <h3 className={`text-white font-semibold mb-3 text-sm sm:text-base ${isRTL ? 'text-right' : 'text-left'}`}>
              نوع المحادثة السورية
            </h3>
            <select
              value={chatType}
              onChange={(e) => setChatType(e.target.value as any)}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="syrian_business">🏢 أعمال سورية - ريادة الأعمال والاستثمار</option>
              <option value="damascus_local">🏛️ دمشق المحلية - العاصمة وأحياؤها</option>
              <option value="aleppo_local">🏭 حلب المحلية - العاصمة الاقتصادية</option>
              <option value="general">🇸🇾 عام سوري - جميع المحافظات</option>
            </select>
          </div>
        </div>

        {/* ✅ CLEAN FULL-WIDTH CHAT INTERFACE - NO SIDEBAR DUPLICATES */}
        <div className="flex-1 w-full">
          <EnhancedAIChat
            key={chatKey} // ✅ FORCE RE-RENDER WHEN CHAT TYPE CHANGES
            height="h-[600px] sm:h-[700px]"
            className="w-full"
            showFeatures={true}
            onStatsUpdate={(stats: any) => setChatStats(stats)}
            featureMessage={featureMessage}
            onFeatureMessageProcessed={() => setFeatureMessage('')}
            startNewChat={shouldStartNewChat}
            onNewChatStarted={() => setShouldStartNewChat(false)}
            chatType={chatType}
          />
        </div>
      </div>
    </div>
  );
};

// Function removed - welcome messages now handled in EnhancedAIChat component

export default AIChatPage;
