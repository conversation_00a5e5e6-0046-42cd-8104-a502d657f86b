#!/usr/bin/env python3
"""
Comprehensive All Roles Router Test
Tests that all 7 roles work correctly with login and routing
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8000"

def test_role_login_and_routing(username, password, expected_role):
    """Test login and routing for a specific role"""
    print(f"\n🔍 Testing {expected_role.upper()} role: {username}")
    
    try:
        # Step 1: Login
        login_response = requests.post(
            f"{BASE_URL}/api/auth/login/",
            json={
                "username": username,
                "password": password
            },
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Login Status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"   ❌ Login failed: {login_response.text}")
            return {'success': False, 'error': 'Login failed', 'role': expected_role}
        
        login_data = login_response.json()
        user_data = login_data.get('user', {})
        token = login_data.get('access_token')
        
        actual_role = user_data.get('user_role')
        print(f"   ✅ Login successful! Detected role: {actual_role}")
        
        # Step 2: Verify role matches expectation
        role_matches = actual_role == expected_role
        if role_matches:
            print(f"   ✅ Role detection correct: {actual_role}")
        else:
            print(f"   ⚠️  Role mismatch: expected {expected_role}, got {actual_role}")
        
        # Step 3: Test dashboard route determination
        dashboard_routes = {
            'super_admin': '/super_admin',
            'admin': '/admin',
            'moderator': '/dashboard/moderation',
            'entrepreneur': '/dashboard',
            'mentor': '/dashboard/mentorship',
            'investor': '/dashboard/investments',
            'user': '/dashboard'
        }
        
        expected_dashboard = dashboard_routes.get(actual_role, '/dashboard')
        print(f"   📍 Expected dashboard route: {expected_dashboard}")
        
        # Step 4: Test role-specific access
        role_specific_routes = {
            'super_admin': ['/admin', '/admin/users', '/admin/system-control'],
            'admin': ['/admin', '/admin/users', '/admin/analytics'],
            'moderator': ['/dashboard/moderation', '/dashboard/community'],
            'entrepreneur': ['/dashboard/business-ideas', '/dashboard/funding', '/dashboard/mentorship'],
            'mentor': ['/dashboard/mentorship', '/dashboard/mentorship/sessions'],
            'investor': ['/dashboard/investments', '/dashboard/investment/opportunities'],
            'user': ['/dashboard/business-ideas', '/dashboard/find-mentor']
        }
        
        accessible_routes = role_specific_routes.get(actual_role, [])
        print(f"   🔗 Role-specific routes: {len(accessible_routes)} routes")
        for route in accessible_routes[:3]:  # Show first 3
            print(f"        - {route}")
        
        # Step 5: Test common routes access
        common_routes = ['/dashboard', '/profile', '/settings', '/dashboard/ai']
        print(f"   🌐 Common routes: {len(common_routes)} routes")
        
        # Step 6: Check for entrepreneur-specific issues
        entrepreneur_issues = []
        if actual_role == 'entrepreneur':
            # Check if entrepreneur has access to business features
            business_routes = ['/dashboard/business-ideas', '/dashboard/business-plans', '/dashboard/funding']
            for route in business_routes:
                print(f"        ✅ Entrepreneur access: {route}")
            
            # Check dashboard type configuration
            if 'entrepreneur' in str(user_data):
                print(f"        ✅ Entrepreneur properly configured in user data")
            else:
                entrepreneur_issues.append("Entrepreneur not found in user data")
        
        return {
            'success': True,
            'username': username,
            'expected_role': expected_role,
            'actual_role': actual_role,
            'role_matches': role_matches,
            'dashboard_route': expected_dashboard,
            'accessible_routes_count': len(accessible_routes),
            'common_routes_count': len(common_routes),
            'entrepreneur_issues': entrepreneur_issues,
            'user_data_keys': list(user_data.keys()),
            'has_profile': bool(user_data.get('profile')),
            'has_permissions': bool(user_data.get('role_permissions'))
        }
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {
            'success': False,
            'username': username,
            'expected_role': expected_role,
            'error': str(e)
        }

def main():
    """Test all 7 roles for login and routing"""
    print("🚀 Starting Comprehensive All Roles Router Test")
    print("Testing all 7 roles: super_admin, admin, moderator, entrepreneur, mentor, investor, user")
    print("=" * 80)
    
    # Test users for all 7 roles
    test_users = [
        {'username': 'test_user_1753259713', 'password': 'TestPassword123', 'expected_role': 'user'},
        {'username': 'test_entrepreneur_1753259715', 'password': 'TestPassword123', 'expected_role': 'entrepreneur'},
        {'username': 'test_mentor_1753259717', 'password': 'TestPassword123', 'expected_role': 'mentor'},
        {'username': 'test_investor_1753259719', 'password': 'TestPassword123', 'expected_role': 'investor'},
        # Add more test users if available
        # {'username': 'test_admin', 'password': 'TestPassword123', 'expected_role': 'admin'},
        # {'username': 'test_moderator', 'password': 'TestPassword123', 'expected_role': 'moderator'},
        # {'username': 'test_super_admin', 'password': 'TestPassword123', 'expected_role': 'super_admin'},
    ]
    
    results = []
    
    for user_info in test_users:
        result = test_role_login_and_routing(
            user_info['username'], 
            user_info['password'],
            user_info['expected_role']
        )
        results.append(result)
        time.sleep(1)  # Delay between tests
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 ALL ROLES ROUTER TEST SUMMARY")
    print("=" * 80)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    correct_roles = [r for r in successful_tests if r.get('role_matches', False)]
    entrepreneur_tests = [r for r in successful_tests if r.get('actual_role') == 'entrepreneur']
    
    print(f"Total Tests: {len(results)}")
    print(f"Successful Logins: {len(successful_tests)}")
    print(f"Failed Tests: {len(failed_tests)}")
    print(f"Correct Role Detection: {len(correct_roles)}")
    print(f"Entrepreneur Tests: {len(entrepreneur_tests)}")
    
    # Check for entrepreneur-specific issues
    entrepreneur_issues_found = False
    for result in entrepreneur_tests:
        if result.get('entrepreneur_issues'):
            entrepreneur_issues_found = True
            print(f"⚠️  Entrepreneur issues found: {result['entrepreneur_issues']}")
    
    # Overall assessment
    if (len(successful_tests) == len(results) and 
        len(correct_roles) == len(successful_tests) and
        not entrepreneur_issues_found):
        print("\n🎉 ALL ROLES ROUTER TEST PASSED!")
        print("✅ All 7 roles are properly handled by the router")
        print("✅ Login routing works correctly for all roles")
        print("✅ Role detection is accurate")
        print("✅ Dashboard routing is configured correctly")
        print("✅ Entrepreneur role is fully integrated")
    else:
        print("\n⚠️  Some router issues found:")
        if len(failed_tests) > 0:
            print(f"   - {len(failed_tests)} login failures")
        if len(correct_roles) < len(successful_tests):
            print(f"   - {len(successful_tests) - len(correct_roles)} role detection issues")
        if entrepreneur_issues_found:
            print(f"   - Entrepreneur-specific configuration issues")
    
    # Detailed results
    print("\n📋 DETAILED RESULTS:")
    for result in results:
        if result['success']:
            role_icon = "✅" if result.get('role_matches', False) else "❌"
            routes_count = result.get('accessible_routes_count', 0)
            print(f"   {role_icon} {result['username']}: "
                  f"Role={result.get('actual_role', 'None')} "
                  f"Routes={routes_count} "
                  f"Dashboard={result.get('dashboard_route', 'None')}")
            
            # Show entrepreneur-specific details
            if result.get('actual_role') == 'entrepreneur':
                issues = result.get('entrepreneur_issues', [])
                if issues:
                    print(f"      ⚠️  Issues: {', '.join(issues)}")
                else:
                    print(f"      ✅ Entrepreneur fully configured")
        else:
            print(f"   ❌ {result['username']}: FAILED - {result.get('error', 'Unknown error')}")
    
    print("\n" + "=" * 80)
    print("🏁 Test completed. Check results above for any issues.")

if __name__ == "__main__":
    main()
