"""
Email Verification Views
Handles email verification endpoints
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth.models import User
from django.utils import timezone
from django.shortcuts import get_object_or_404

from ..models.email_verification import (
    EmailVerificationToken, 
    EmailVerificationAttempt,
    EmailVerificationSettings
)
from ..serializers.email_verification import (
    EmailVerificationRequestSerializer,
    EmailVerificationConfirmSerializer,
    EmailVerificationStatusSerializer,
    EmailVerificationTokenSerializer
)


class EmailVerificationRequestView(APIView):
    """
    Request email verification
    POST /api/auth/email/request-verification/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        Send verification email to user
        """
        serializer = EmailVerificationRequestSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            try:
                result = serializer.save()
                return Response({
                    'success': True,
                    'message': result['message'],
                    'email': result['email'],
                    'expires_at': result['expires_at']
                }, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({
                    'success': False,
                    'message': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class EmailVerificationConfirmView(APIView):
    """
    Confirm email verification
    POST /api/auth/email/verify/
    """
    permission_classes = [permissions.AllowAny]  # Allow unauthenticated access
    
    def post(self, request):
        """
        Verify email using token
        """
        serializer = EmailVerificationConfirmSerializer(data=request.data)
        
        if serializer.is_valid():
            try:
                result = serializer.save()
                return Response({
                    'success': True,
                    'message': result['message'],
                    'email': result['email'],
                    'verified_at': result['verified_at']
                }, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({
                    'success': False,
                    'message': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class EmailVerificationStatusView(APIView):
    """
    Get email verification status
    GET /api/auth/email/status/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        """
        Get current user's email verification status
        """
        serializer = EmailVerificationStatusSerializer(request.user)
        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def verify_email_token(request, token):
    """
    Verify email token via GET request (for email links)
    GET /api/auth/email/verify/{token}/
    """
    try:
        # Verify the token
        success, message = EmailVerificationToken.verify_token(token)
        
        if success:
            return Response({
                'success': True,
                'message': message,
                'redirect_url': '/login?verified=true'
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'message': message,
                'redirect_url': '/login?verified=false'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        return Response({
            'success': False,
            'message': f'Verification failed: {str(e)}',
            'redirect_url': '/login?verified=error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EmailVerificationResendView(APIView):
    """
    Resend verification email
    POST /api/auth/email/resend/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """
        Resend verification email for current user
        """
        user = request.user
        email = request.data.get('email', user.email)
        
        # Check if user can send verification email
        can_send, message = EmailVerificationAttempt.can_send_verification(user, email)
        if not can_send:
            return Response({
                'success': False,
                'message': message
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        
        try:
            # Create new verification token
            token = EmailVerificationToken.create_for_user(user, email)
            
            # Send verification email
            success = token.send_verification_email(request)
            
            # Record attempt
            EmailVerificationAttempt.record_attempt(
                user=user,
                email=email,
                success=success,
                ip_address=self.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            if success:
                return Response({
                    'success': True,
                    'message': 'Verification email sent successfully',
                    'email': email,
                    'expires_at': token.expires_at
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'message': 'Failed to send verification email'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'Failed to resend verification: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class EmailVerificationSettingsView(APIView):
    """
    Get email verification settings
    GET /api/auth/email/settings/
    """
    permission_classes = [permissions.AllowAny]
    
    def get(self, request):
        """
        Get current email verification settings
        """
        settings = EmailVerificationSettings.get_settings()
        
        return Response({
            'success': True,
            'data': {
                'require_verification': settings.require_verification,
                'token_expiry_hours': settings.token_expiry_hours,
                'max_resend_attempts': settings.max_resend_attempts,
                'resend_cooldown_minutes': settings.resend_cooldown_minutes
            }
        }, status=status.HTTP_200_OK)


# Admin views for managing email verification
class EmailVerificationTokenListView(APIView):
    """
    List email verification tokens (admin only)
    GET /api/admin/email-verification/tokens/
    """
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """
        Get list of email verification tokens
        """
        tokens = EmailVerificationToken.objects.select_related('user').all()
        
        # Filter by user if specified
        user_id = request.query_params.get('user_id')
        if user_id:
            tokens = tokens.filter(user_id=user_id)
        
        # Filter by status
        status_filter = request.query_params.get('status')
        if status_filter == 'valid':
            tokens = tokens.filter(is_used=False, expires_at__gt=timezone.now())
        elif status_filter == 'expired':
            tokens = tokens.filter(is_used=False, expires_at__lte=timezone.now())
        elif status_filter == 'used':
            tokens = tokens.filter(is_used=True)
        
        serializer = EmailVerificationTokenSerializer(tokens, many=True)
        return Response({
            'success': True,
            'data': serializer.data,
            'count': tokens.count()
        }, status=status.HTTP_200_OK)


class EmailVerificationAttemptListView(APIView):
    """
    List email verification attempts (admin only)
    GET /api/admin/email-verification/attempts/
    """
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """
        Get list of email verification attempts
        """
        attempts = EmailVerificationAttempt.objects.select_related('user').all()
        
        # Filter by user if specified
        user_id = request.query_params.get('user_id')
        if user_id:
            attempts = attempts.filter(user_id=user_id)
        
        # Filter by success status
        success_filter = request.query_params.get('success')
        if success_filter is not None:
            attempts = attempts.filter(success=success_filter.lower() == 'true')
        
        serializer = EmailVerificationAttemptSerializer(attempts, many=True)
        return Response({
            'success': True,
            'data': serializer.data,
            'count': attempts.count()
        }, status=status.HTTP_200_OK)
