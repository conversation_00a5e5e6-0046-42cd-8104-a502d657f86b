#!/usr/bin/env python3
"""
Test script to verify role detection and assignment after login
Tests the complete role flow from registration to login
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:8000"

def test_user_login_and_role_detection(username, password):
    """Test login and role detection for a specific user"""
    print(f"\n🔐 Testing login and role detection for: {username}")
    
    try:
        # Step 1: Login
        login_response = requests.post(
            f"{BASE_URL}/api/auth/login/",
            json={
                "username": username,
                "password": password
            },
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Login Status Code: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            user_data = login_data.get('user', {})
            
            print(f"   ✅ Login successful!")
            print(f"   👤 User ID: {user_data.get('id')}")
            print(f"   📧 Email: {user_data.get('email')}")
            print(f"   🏷️  User Role Field: {user_data.get('user_role')}")
            print(f"   🔧 Is Staff: {user_data.get('is_staff')}")
            print(f"   🔧 Is Superuser: {user_data.get('is_superuser')}")
            
            # Check profile data
            profile = user_data.get('profile', {})
            if profile:
                print(f"   📋 Profile Role: {profile.get('role')}")
                print(f"   📋 Primary Role: {profile.get('primary_role')}")
                print(f"   📋 Active Roles: {profile.get('active_roles')}")
                print(f"   📋 Requested Role: {profile.get('requested_role_name', 'Not set')}")
            
            # Step 2: Get current user (to test role detection)
            access_token = login_data.get('access')
            if access_token:
                user_response = requests.get(
                    f"{BASE_URL}/api/auth/user/",
                    headers={
                        'Authorization': f'Bearer {access_token}',
                        'Content-Type': 'application/json'
                    }
                )
                
                print(f"   Get User Status Code: {user_response.status_code}")
                
                if user_response.status_code == 200:
                    current_user = user_response.json()
                    print(f"   🔄 Current User Role: {current_user.get('user_role')}")
                    print(f"   🔄 Current User Profile: {current_user.get('profile', {}).get('primary_role')}")
                else:
                    print(f"   ❌ Failed to get current user: {user_response.text}")
            
            return {
                'success': True,
                'username': username,
                'user_role': user_data.get('user_role'),
                'profile_role': profile.get('primary_role'),
                'is_staff': user_data.get('is_staff'),
                'is_superuser': user_data.get('is_superuser')
            }
            
        else:
            print(f"   ❌ Login failed!")
            print(f"   Response: {login_response.text}")
            return {
                'success': False,
                'username': username,
                'error': login_response.text
            }
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {
            'success': False,
            'username': username,
            'error': str(e)
        }

def main():
    """Test role detection for different user types"""
    print("🚀 Starting Role Detection Tests")
    print("=" * 50)
    
    # Test users created earlier
    test_users = [
        {'username': 'test_user_1753259713', 'password': 'TestPassword123', 'expected_role': 'user'},
        {'username': 'test_entrepreneur_1753259715', 'password': 'TestPassword123', 'expected_role': 'entrepreneur'},
        {'username': 'test_mentor_1753259717', 'password': 'TestPassword123', 'expected_role': 'mentor'},
        {'username': 'test_investor_1753259719', 'password': 'TestPassword123', 'expected_role': 'investor'},
    ]
    
    results = []
    
    for user_info in test_users:
        result = test_user_login_and_role_detection(
            user_info['username'], 
            user_info['password']
        )
        result['expected_role'] = user_info['expected_role']
        results.append(result)
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 ROLE DETECTION TEST SUMMARY")
    print("=" * 50)
    
    successful_logins = [r for r in results if r['success']]
    failed_logins = [r for r in results if not r['success']]
    correct_roles = [r for r in successful_logins if r.get('user_role') == r.get('expected_role')]
    
    print(f"Total Tests: {len(results)}")
    print(f"Successful Logins: {len(successful_logins)}")
    print(f"Failed Logins: {len(failed_logins)}")
    print(f"Correct Role Detection: {len(correct_roles)}")
    
    if len(correct_roles) == len(successful_logins) and len(failed_logins) == 0:
        print("\n🎉 ALL TESTS PASSED! Role detection is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
    
    # Detailed results
    print("\n📋 DETAILED RESULTS:")
    for result in results:
        if result['success']:
            role_correct = result.get('user_role') == result.get('expected_role')
            status_icon = "✅" if role_correct else "❌"
            print(f"   {status_icon} {result['username']}: "
                  f"Expected={result['expected_role']}, "
                  f"Detected={result.get('user_role', 'None')}, "
                  f"Staff={result.get('is_staff', False)}")
        else:
            print(f"   ❌ {result['username']}: LOGIN FAILED - {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
