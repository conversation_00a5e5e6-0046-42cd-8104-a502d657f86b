"""
User approval management views for admin interface
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth.models import User
from django.utils import timezone
from django.db.models import Q
from .models import UserApproval
from .serializers import UserSerializer
from .permissions import IsAdminUser
import logging

logger = logging.getLogger(__name__)


class UserApprovalViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user approvals
    Only accessible by admin users
    """
    permission_classes = [IsAuthenticated, IsAdminUser]
    
    def get_queryset(self):
        """Get all user approvals with related user data"""
        return UserApproval.objects.select_related('user', 'reviewed_by').all()
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action in ['list', 'retrieve']:
            return UserApprovalSerializer
        return UserApprovalSerializer
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a user registration"""
        try:
            approval = self.get_object()
            
            if approval.status != 'pending':
                return Response(
                    {'error': f'User is already {approval.status}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Approve the user
            approval.approve(request.user)
            
            logger.info(f"User {approval.user.username} approved by {request.user.username}")
            
            return Response({
                'message': f'User {approval.user.username} has been approved',
                'status': 'approved'
            })
            
        except Exception as e:
            logger.error(f"Error approving user {pk}: {e}")
            return Response(
                {'error': 'Failed to approve user'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject a user registration with reason"""
        try:
            approval = self.get_object()
            reason = request.data.get('reason', '')
            
            if approval.status != 'pending':
                return Response(
                    {'error': f'User is already {approval.status}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if not reason.strip():
                return Response(
                    {'error': 'Rejection reason is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Reject the user
            approval.reject(request.user, reason)
            
            logger.info(f"User {approval.user.username} rejected by {request.user.username}")
            
            return Response({
                'message': f'User {approval.user.username} has been rejected',
                'status': 'rejected'
            })
            
        except Exception as e:
            logger.error(f"Error rejecting user {pk}: {e}")
            return Response(
                {'error': 'Failed to reject user'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def pending(self, request):
        """Get all pending user approvals"""
        pending_approvals = self.get_queryset().filter(status='pending')
        serializer = self.get_serializer(pending_approvals, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get approval statistics"""
        queryset = self.get_queryset()

        stats = {
            'total': queryset.count(),
            'pending': queryset.filter(status='pending').count(),
            'approved': queryset.filter(status='approved').count(),
            'rejected': queryset.filter(status='rejected').count(),
        }

        return Response(stats)

    @action(detail=True, methods=['get'])
    def detailed_review(self, request, pk=None):
        """Get detailed information for admin review"""
        try:
            approval = self.get_object()
            user = approval.user

            # Get comprehensive user data
            detailed_data = {
                'approval_info': UserApprovalSerializer(approval).data,
                'user_basic': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'date_joined': user.date_joined,
                    'is_active': user.is_active
                },
                'profile_data': None,
                'role_applications': []
            }

            # Get profile data if exists
            if hasattr(user, 'profile'):
                profile = user.profile
                detailed_data['profile_data'] = {
                    'bio': profile.bio,
                    'location': profile.location,
                    'company': profile.company,
                    'job_title': profile.job_title,
                    'industry': profile.industry,
                    'phone_number': profile.phone_number,
                    'website': profile.website,
                    'linkedin_url': profile.linkedin_url,
                    'language': profile.language,
                    'requested_role_name': profile.requested_role_name,
                    # Role-specific fields
                    'entrepreneur_data': {
                        'business_name': profile.business_name,
                        'business_stage': profile.business_stage,
                        'funding_needed': profile.funding_needed,
                        'business_description': profile.business_description
                    } if profile.business_name or profile.business_stage else None,
                    'mentor_data': {
                        'expertise': profile.expertise,
                        'mentor_experience': profile.mentor_experience,
                        'mentorship_areas': profile.mentorship_areas,
                        'availability': profile.availability
                    } if profile.expertise or profile.mentor_experience else None,
                    'investor_data': {
                        'investment_range': profile.investment_range,
                        'investment_stage': profile.investment_stage,
                        'preferred_industries': profile.preferred_industries,
                        'investment_criteria': profile.investment_criteria
                    } if profile.investment_range or profile.investment_stage else None,
                    'user_data': {
                        'interests': profile.interests,
                        'goals': profile.goals
                    } if profile.interests or profile.goals else None
                }

            return Response(detailed_data)

        except Exception as e:
            logger.error(f"Error in detailed_review for approval {pk}: {e}")
            return Response(
                {'error': 'Failed to fetch detailed review data'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


from rest_framework import serializers

class UserApprovalSerializer(serializers.ModelSerializer):
    """Enhanced serializer for UserApproval model with role-specific information"""
    user = UserSerializer(read_only=True)
    reviewed_by = UserSerializer(read_only=True)
    user_full_name = serializers.SerializerMethodField()
    days_pending = serializers.SerializerMethodField()
    requested_role_info = serializers.SerializerMethodField()
    role_specific_data = serializers.SerializerMethodField()
    profile_summary = serializers.SerializerMethodField()

    class Meta:
        model = UserApproval
        fields = [
            'id', 'user', 'status', 'reviewed_by', 'reviewed_at',
            'rejection_reason', 'admin_notes', 'created_at', 'updated_at',
            'user_full_name', 'days_pending', 'requested_role_info',
            'role_specific_data', 'profile_summary'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_user_full_name(self, obj):
        """Get user's full name or username"""
        if obj.user.first_name and obj.user.last_name:
            return f"{obj.user.first_name} {obj.user.last_name}"
        return obj.user.username

    def get_days_pending(self, obj):
        """Calculate days since registration"""
        if obj.status == 'pending':
            delta = timezone.now() - obj.created_at
            return delta.days
        return None

    def get_requested_role_info(self, obj):
        """Get information about the requested role"""
        try:
            # Check if user has any role applications
            from .models import RoleApplication
            role_applications = RoleApplication.objects.filter(user=obj.user).order_by('-created_at')

            if role_applications.exists():
                latest_application = role_applications.first()
                return {
                    'role_name': latest_application.requested_role.name,
                    'role_display_name': latest_application.requested_role.display_name,
                    'application_status': latest_application.status,
                    'motivation': latest_application.motivation,
                    'qualifications': latest_application.qualifications,
                    'applied_at': latest_application.created_at
                }

            # Fallback to profile requested role
            if hasattr(obj.user, 'profile') and obj.user.profile.requested_role_name:
                return {
                    'role_name': obj.user.profile.requested_role_name,
                    'role_display_name': obj.user.profile.requested_role_name.title(),
                    'application_status': 'pending',
                    'motivation': 'Applied during registration',
                    'qualifications': 'Provided during registration process',
                    'applied_at': obj.created_at
                }

            return None
        except Exception:
            return None

    def get_role_specific_data(self, obj):
        """Get role-specific data based on user's requested role"""
        if not hasattr(obj.user, 'profile'):
            return None

        profile = obj.user.profile
        role_data = {}

        # Get requested role from role applications or profile
        requested_role = None
        try:
            from .models import RoleApplication
            role_app = RoleApplication.objects.filter(user=obj.user).order_by('-created_at').first()
            if role_app:
                requested_role = role_app.requested_role.name
        except:
            pass

        if not requested_role and profile.requested_role_name:
            requested_role = profile.requested_role_name

        # Extract role-specific fields based on requested role
        if requested_role == 'entrepreneur':
            role_data = {
                'business_name': profile.business_name,
                'business_stage': profile.business_stage,
                'funding_needed': profile.funding_needed,
                'business_description': profile.business_description,
                'industry': profile.industry
            }
        elif requested_role == 'mentor':
            role_data = {
                'expertise': profile.expertise,
                'mentor_experience': profile.mentor_experience,
                'mentorship_areas': profile.mentorship_areas,
                'availability': profile.availability
            }
        elif requested_role == 'investor':
            role_data = {
                'investment_range': profile.investment_range,
                'investment_stage': profile.investment_stage,
                'preferred_industries': profile.preferred_industries,
                'investment_criteria': profile.investment_criteria
            }
        elif requested_role == 'user':
            role_data = {
                'interests': profile.interests,
                'goals': profile.goals
            }

        # Also include any additional role info from JSON field
        if profile.role_additional_info:
            role_data.update(profile.role_additional_info)

        return role_data if role_data else None

    def get_profile_summary(self, obj):
        """Get a summary of user's profile information"""
        if not hasattr(obj.user, 'profile'):
            return None

        profile = obj.user.profile
        return {
            'location': profile.location,
            'company': profile.company,
            'job_title': profile.job_title,
            'industry': profile.industry,
            'phone_number': profile.phone_number,
            'bio': profile.bio,
            'language': profile.language,
            'website': profile.website,
            'linkedin_url': profile.linkedin_url
        }