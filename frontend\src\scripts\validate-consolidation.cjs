/**
 * DASHBOARD CONSOLIDATION VALIDATION SCRIPT
 * Validates that dashboard consolidation was successful
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function validateConsolidation() {
  log('\n🔍 DASHBOARD CONSOLIDATION VALIDATION', 'bold');
  log('=' .repeat(50), 'blue');

  const results = {
    passed: 0,
    failed: 0,
    warnings: 0,
    issues: []
  };

  // Test 1: Check that dashboard pages use UnifiedDashboard
  log('\n📋 Test 1: Dashboard Page Consolidation', 'blue');
  
  const dashboardPages = [
    'src/pages/admin/AdminDashboardPage.tsx',
    'src/pages/super-admin/SuperAdminDashboardPage.tsx',
    'src/pages/moderator/ModeratorDashboardPage.tsx',
    'src/pages/investor/InvestorDashboardPage.tsx',
    'src/pages/dashboard/MentorDashboardPage.tsx',
    'src/pages/dashboard/UserDashboardPage.tsx'
  ];

  dashboardPages.forEach(pagePath => {
    try {
      const content = fs.readFileSync(pagePath, 'utf8');
      
      if (content.includes('UnifiedDashboard')) {
        log(`  ✅ ${path.basename(pagePath)} uses UnifiedDashboard`, 'green');
        results.passed++;
      } else {
        log(`  ❌ ${path.basename(pagePath)} does NOT use UnifiedDashboard`, 'red');
        results.failed++;
        results.issues.push(`${pagePath} should use UnifiedDashboard`);
      }
      
      // Check for old dashboard imports
      const oldImports = [
        'AdminDashboard',
        'SuperAdminDashboard',
        'ModeratorDashboard',
        'InvestorDashboard',
        'MentorDashboard',
        'UserDashboard'
      ];
      
      oldImports.forEach(oldImport => {
        if (content.includes(`import ${oldImport}`) || content.includes(`from '../../components/dashboard/`)) {
          log(`  ⚠️  ${path.basename(pagePath)} may have old dashboard imports`, 'yellow');
          results.warnings++;
        }
      });
      
    } catch (error) {
      log(`  ❌ Could not read ${pagePath}: ${error.message}`, 'red');
      results.failed++;
    }
  });

  // Test 2: Check DashboardRouter simplification
  log('\n📋 Test 2: DashboardRouter Simplification', 'blue');
  
  try {
    const routerPath = 'src/components/routing/DashboardRouter.tsx';
    const routerContent = fs.readFileSync(routerPath, 'utf8');
    
    if (routerContent.includes('UnifiedDashboard')) {
      log('  ✅ DashboardRouter uses UnifiedDashboard', 'green');
      results.passed++;
    } else {
      log('  ❌ DashboardRouter does NOT use UnifiedDashboard', 'red');
      results.failed++;
      results.issues.push('DashboardRouter should use UnifiedDashboard');
    }
    
    // Check for complex routing logic
    if (routerContent.includes('getDashboardRoute') || routerContent.includes('switch (role)')) {
      log('  ⚠️  DashboardRouter may still have complex routing logic', 'yellow');
      results.warnings++;
    } else {
      log('  ✅ DashboardRouter routing logic simplified', 'green');
      results.passed++;
    }
    
  } catch (error) {
    log(`  ❌ Could not read DashboardRouter: ${error.message}`, 'red');
    results.failed++;
  }

  // Test 3: Check for duplicate dashboard components
  log('\n📋 Test 3: Duplicate Component Detection', 'blue');
  
  const dashboardDirs = [
    'src/components/dashboard/admin-dashboard',
    'src/components/dashboard/super-admin-dashboard',
    'src/components/dashboard/moderator-dashboard',
    'src/components/dashboard/investor-dashboard',
    'src/components/dashboard/mentor-dashboard'
  ];
  
  dashboardDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      log(`  ⚠️  Duplicate dashboard directory still exists: ${dir}`, 'yellow');
      results.warnings++;
      results.issues.push(`Consider removing duplicate directory: ${dir}`);
    } else {
      log(`  ✅ No duplicate directory: ${path.basename(dir)}`, 'green');
      results.passed++;
    }
  });

  // Test 4: Check UnifiedDashboard exists and is properly structured
  log('\n📋 Test 4: UnifiedDashboard Architecture', 'blue');
  
  try {
    const unifiedPath = 'src/components/dashboard/unified/UnifiedDashboard.tsx';
    const unifiedContent = fs.readFileSync(unifiedPath, 'utf8');
    
    const requiredFeatures = [
      'role',
      'DashboardRole',
      'getUserRole',
      'sections'
    ];
    
    requiredFeatures.forEach(feature => {
      if (unifiedContent.includes(feature)) {
        log(`  ✅ UnifiedDashboard includes ${feature}`, 'green');
        results.passed++;
      } else {
        log(`  ❌ UnifiedDashboard missing ${feature}`, 'red');
        results.failed++;
        results.issues.push(`UnifiedDashboard should include ${feature}`);
      }
    });
    
  } catch (error) {
    log(`  ❌ Could not read UnifiedDashboard: ${error.message}`, 'red');
    results.failed++;
  }

  // Summary
  log('\n📊 CONSOLIDATION VALIDATION SUMMARY', 'bold');
  log('=' .repeat(50), 'blue');
  log(`✅ Passed: ${results.passed}`, 'green');
  log(`❌ Failed: ${results.failed}`, results.failed > 0 ? 'red' : 'green');
  log(`⚠️  Warnings: ${results.warnings}`, results.warnings > 0 ? 'yellow' : 'green');

  if (results.issues.length > 0) {
    log('\n🔧 ISSUES TO ADDRESS:', 'yellow');
    results.issues.forEach((issue, index) => {
      log(`${index + 1}. ${issue}`, 'yellow');
    });
  }

  const successRate = (results.passed / (results.passed + results.failed)) * 100;
  log(`\n📈 Success Rate: ${successRate.toFixed(1)}%`, successRate > 80 ? 'green' : 'red');

  if (results.failed === 0) {
    log('\n🎉 DASHBOARD CONSOLIDATION SUCCESSFUL!', 'green');
    log('All dashboard components have been properly consolidated.', 'green');
  } else {
    log('\n⚠️  CONSOLIDATION NEEDS ATTENTION', 'yellow');
    log('Some issues were found that should be addressed.', 'yellow');
  }

  return results;
}

// Run validation
if (require.main === module) {
  validateConsolidation();
}

module.exports = { validateConsolidation };
