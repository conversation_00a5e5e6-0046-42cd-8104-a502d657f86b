/**
 * Registration Flow Fixes Test Suite
 * 
 * Tests all the fixes applied to resolve registration form issues:
 * ✅ Form validation errors preventing submission
 * ✅ Required fields not properly configured
 * ✅ Multi-step form flow problems
 * ✅ Registration doesn't complete successfully
 * ✅ Form gets stuck and doesn't proceed to success page
 * ✅ Validation feedback is unclear
 * ✅ Error messages are not helpful
 * ✅ Redux state management issues
 * ✅ Form submission logic bugs
 * ✅ Navigation between steps is problematic
 */

import * as React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';

import EnhancedRegisterPage from '../pages/EnhancedRegisterPage';
import registrationReducer from '../store/registrationSlice';
import { LanguageProvider } from '../components/LanguageProvider';

// Mock API
jest.mock('../services/api', () => ({
  api: {
    post: jest.fn()
  }
}));

// Mock navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

// Test store setup
const createTestStore = () => {
  return configureStore({
    reducer: {
      registration: registrationReducer,
      // Add minimal required reducers
      auth: (state = { user: null, isAuthenticated: false }) => state,
      language: (state = { currentLanguage: 'en' }) => state
    }
  });
};

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const store = createTestStore();
  
  return (
    <Provider store={store}>
      <BrowserRouter>
        <LanguageProvider>
          {children}
        </LanguageProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('Registration Flow Fixes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  describe('✅ Form Validation Fixes', () => {
    it('should not block progression when async validation is in checking state', async () => {
      render(
        <TestWrapper>
          <EnhancedRegisterPage />
        </TestWrapper>
      );

      // Fill required fields
      fireEvent.change(screen.getByPlaceholderText(/enter first name/i), {
        target: { value: 'John' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter last name/i), {
        target: { value: 'Doe' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter username/i), {
        target: { value: 'johndoe123' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter password/i), {
        target: { value: 'password123' }
      });
      fireEvent.change(screen.getByPlaceholderText(/re-enter password/i), {
        target: { value: 'password123' }
      });

      // Should be able to proceed to next step even if validation is checking
      const nextButton = screen.getByText(/next/i);
      fireEvent.click(nextButton);

      // Should move to step 2 (role selection)
      await waitFor(() => {
        expect(screen.getByText(/role selection/i)).toBeInTheDocument();
      });
    });

    it('should make location field optional to match backend', async () => {
      render(
        <TestWrapper>
          <EnhancedRegisterPage />
        </TestWrapper>
      );

      // Fill required fields but leave location empty
      fireEvent.change(screen.getByPlaceholderText(/enter first name/i), {
        target: { value: 'John' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter last name/i), {
        target: { value: 'Doe' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter username/i), {
        target: { value: 'johndoe123' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter password/i), {
        target: { value: 'password123' }
      });
      fireEvent.change(screen.getByPlaceholderText(/re-enter password/i), {
        target: { value: 'password123' }
      });
      // Location is left empty

      const nextButton = screen.getByText(/next/i);
      fireEvent.click(nextButton);

      // Should proceed without location error
      await waitFor(() => {
        expect(screen.getByText(/role selection/i)).toBeInTheDocument();
      });
    });
  });

  describe('✅ Multi-Step Form Flow Fixes', () => {
    it('should navigate between steps smoothly', async () => {
      render(
        <TestWrapper>
          <EnhancedRegisterPage />
        </TestWrapper>
      );

      // Start at step 1
      expect(screen.getByText(/basic information/i)).toBeInTheDocument();

      // Fill step 1 and proceed
      fireEvent.change(screen.getByPlaceholderText(/enter first name/i), {
        target: { value: 'John' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter last name/i), {
        target: { value: 'Doe' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter username/i), {
        target: { value: 'johndoe123' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter password/i), {
        target: { value: 'password123' }
      });
      fireEvent.change(screen.getByPlaceholderText(/re-enter password/i), {
        target: { value: 'password123' }
      });

      fireEvent.click(screen.getByText(/next/i));

      // Should be at step 2
      await waitFor(() => {
        expect(screen.getByText(/role selection/i)).toBeInTheDocument();
      });

      // Select a role and proceed
      const entrepreneurRole = screen.getByText(/entrepreneur/i);
      fireEvent.click(entrepreneurRole);

      fireEvent.click(screen.getByText(/next/i));

      // Should be at step 3
      await waitFor(() => {
        expect(screen.getByText(/terms & conditions/i)).toBeInTheDocument();
      });

      // Should be able to go back
      fireEvent.click(screen.getByText(/previous/i));

      await waitFor(() => {
        expect(screen.getByText(/role selection/i)).toBeInTheDocument();
      });
    });
  });

  describe('✅ Enhanced Error Messages', () => {
    it('should display clear validation messages with icons', async () => {
      render(
        <TestWrapper>
          <EnhancedRegisterPage />
        </TestWrapper>
      );

      // Try to proceed without filling required fields
      fireEvent.click(screen.getByText(/next/i));

      // Should show clear error messages
      await waitFor(() => {
        expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/last name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/username is required/i)).toBeInTheDocument();
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
        expect(screen.getByText(/password is required/i)).toBeInTheDocument();
      });
    });

    it('should show improved step progress indicator', () => {
      render(
        <TestWrapper>
          <EnhancedRegisterPage />
        </TestWrapper>
      );

      // Should show step progress with clear labels
      expect(screen.getByText(/basic information/i)).toBeInTheDocument();
      expect(screen.getByText(/role selection/i)).toBeInTheDocument();
      expect(screen.getByText(/terms & conditions/i)).toBeInTheDocument();
    });
  });

  describe('✅ Form Submission Fixes', () => {
    it('should handle successful registration with proper navigation', async () => {
      const { api } = await import('../services/api');
      
      // Mock successful API response
      (api.post as any).mockResolvedValueOnce({
        status: 201,
        data: {
          user: { id: 1, username: 'johndoe123' },
          message: 'Registration successful'
        }
      });

      render(
        <TestWrapper>
          <EnhancedRegisterPage />
        </TestWrapper>
      );

      // Complete the entire form
      // Step 1: Basic Information
      fireEvent.change(screen.getByPlaceholderText(/enter first name/i), {
        target: { value: 'John' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter last name/i), {
        target: { value: 'Doe' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter username/i), {
        target: { value: 'johndoe123' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter email/i), {
        target: { value: '<EMAIL>' }
      });
      fireEvent.change(screen.getByPlaceholderText(/enter password/i), {
        target: { value: 'password123' }
      });
      fireEvent.change(screen.getByPlaceholderText(/re-enter password/i), {
        target: { value: 'password123' }
      });

      fireEvent.click(screen.getByText(/next/i));

      // Step 2: Role Selection
      await waitFor(() => {
        const entrepreneurRole = screen.getByText(/entrepreneur/i);
        fireEvent.click(entrepreneurRole);
      });

      fireEvent.click(screen.getByText(/next/i));

      // Step 3: Terms & Conditions
      await waitFor(() => {
        const termsCheckbox = screen.getByLabelText(/terms and conditions/i);
        const privacyCheckbox = screen.getByLabelText(/privacy policy/i);
        
        fireEvent.click(termsCheckbox);
        fireEvent.click(privacyCheckbox);
      });

      // Submit the form
      const submitButton = screen.getByText(/create account/i);
      fireEvent.click(submitButton);

      // Should show success message and navigate
      await waitFor(() => {
        expect(screen.getByText(/account created successfully/i)).toBeInTheDocument();
      });

      // Should eventually navigate to success page
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/register/success');
      }, { timeout: 6000 });
    });
  });
});
