/**
 * PERFORMANCE AUDIT SCRIPT
 * Comprehensive performance analysis for the virtual incubator platform
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzePackageJson() {
  log('\n📦 FRONTEND DEPENDENCY ANALYSIS', 'bold');
  log('=' .repeat(50), 'blue');

  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const deps = packageJson.dependencies || {};
    const devDeps = packageJson.devDependencies || {};
    
    log(`📋 Production Dependencies: ${Object.keys(deps).length}`, 'cyan');
    log(`🔧 Development Dependencies: ${Object.keys(devDeps).length}`, 'cyan');
    log(`📊 Total Dependencies: ${Object.keys(deps).length + Object.keys(devDeps).length}`, 'cyan');

    // Analyze heavy dependencies
    const heavyDeps = [
      '@reduxjs/toolkit',
      '@tanstack/react-query',
      'chart.js',
      'react-chartjs-2',
      'recharts',
      'lucide-react',
      'react-quill',
      'jspdf',
      'xlsx'
    ];

    log('\n🔍 Heavy Dependencies Analysis:', 'yellow');
    heavyDeps.forEach(dep => {
      if (deps[dep] || devDeps[dep]) {
        log(`  ✅ ${dep} - ${deps[dep] || devDeps[dep]}`, 'green');
      } else {
        log(`  ❌ ${dep} - Not found`, 'red');
      }
    });

    // Check for duplicate functionality
    log('\n⚠️  Potential Duplicate Functionality:', 'yellow');
    
    const chartLibs = ['chart.js', 'react-chartjs-2', 'recharts'];
    const foundChartLibs = chartLibs.filter(lib => deps[lib] || devDeps[lib]);
    if (foundChartLibs.length > 1) {
      log(`  📊 Multiple chart libraries: ${foundChartLibs.join(', ')}`, 'yellow');
    }

    const testingLibs = ['jest', 'cypress', '@playwright/test'];
    const foundTestingLibs = testingLibs.filter(lib => deps[lib] || devDeps[lib]);
    if (foundTestingLibs.length > 2) {
      log(`  🧪 Multiple testing frameworks: ${foundTestingLibs.join(', ')}`, 'yellow');
    }

    return {
      totalDeps: Object.keys(deps).length + Object.keys(devDeps).length,
      prodDeps: Object.keys(deps).length,
      devDeps: Object.keys(devDeps).length,
      heavyDeps: foundChartLibs.length + (foundTestingLibs.length > 2 ? 1 : 0)
    };

  } catch (error) {
    log(`❌ Error analyzing package.json: ${error.message}`, 'red');
    return null;
  }
}

function analyzeBundleSize() {
  log('\n📦 BUNDLE SIZE ANALYSIS', 'bold');
  log('=' .repeat(50), 'blue');

  try {
    // Check if build exists
    if (!fs.existsSync('dist')) {
      log('⚠️  No build found. Running build...', 'yellow');
      try {
        execSync('npm run build', { stdio: 'inherit' });
      } catch (error) {
        log('❌ Build failed. Skipping bundle analysis.', 'red');
        return null;
      }
    }

    // Analyze dist folder
    const distPath = 'dist';
    let totalSize = 0;
    const files = [];

    function analyzeDirectory(dir) {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const itemPath = path.join(dir, item);
        const stats = fs.statSync(itemPath);
        
        if (stats.isDirectory()) {
          analyzeDirectory(itemPath);
        } else {
          totalSize += stats.size;
          files.push({
            path: itemPath,
            size: stats.size,
            name: item
          });
        }
      });
    }

    analyzeDirectory(distPath);

    // Sort files by size
    files.sort((a, b) => b.size - a.size);

    log(`📊 Total Bundle Size: ${formatBytes(totalSize)}`, 'cyan');
    log(`📁 Total Files: ${files.length}`, 'cyan');

    log('\n🔍 Largest Files:', 'yellow');
    files.slice(0, 10).forEach((file, index) => {
      const color = file.size > 1024 * 1024 ? 'red' : file.size > 512 * 1024 ? 'yellow' : 'green';
      log(`  ${index + 1}. ${file.name} - ${formatBytes(file.size)}`, color);
    });

    // Performance recommendations
    log('\n💡 Bundle Optimization Recommendations:', 'magenta');
    
    if (totalSize > 5 * 1024 * 1024) {
      log('  ⚠️  Bundle size > 5MB - Consider code splitting', 'yellow');
    }
    
    const largeFiles = files.filter(f => f.size > 1024 * 1024);
    if (largeFiles.length > 0) {
      log(`  ⚠️  ${largeFiles.length} files > 1MB - Consider optimization`, 'yellow');
    }

    const jsFiles = files.filter(f => f.name.endsWith('.js'));
    const cssFiles = files.filter(f => f.name.endsWith('.css'));
    
    log(`  📊 JavaScript files: ${jsFiles.length} (${formatBytes(jsFiles.reduce((sum, f) => sum + f.size, 0))})`, 'cyan');
    log(`  🎨 CSS files: ${cssFiles.length} (${formatBytes(cssFiles.reduce((sum, f) => sum + f.size, 0))})`, 'cyan');

    return {
      totalSize,
      fileCount: files.length,
      largestFile: files[0],
      jsSize: jsFiles.reduce((sum, f) => sum + f.size, 0),
      cssSize: cssFiles.reduce((sum, f) => sum + f.size, 0)
    };

  } catch (error) {
    log(`❌ Error analyzing bundle: ${error.message}`, 'red');
    return null;
  }
}

function analyzeNodeModules() {
  log('\n📁 NODE_MODULES ANALYSIS', 'bold');
  log('=' .repeat(50), 'blue');

  try {
    if (!fs.existsSync('node_modules')) {
      log('❌ node_modules not found', 'red');
      return null;
    }

    let totalSize = 0;
    let fileCount = 0;
    const packages = [];

    function analyzeDirectory(dir, packageName = '') {
      try {
        const items = fs.readdirSync(dir);
        let packageSize = 0;
        
        items.forEach(item => {
          const itemPath = path.join(dir, item);
          try {
            const stats = fs.statSync(itemPath);
            
            if (stats.isDirectory()) {
              if (dir === 'node_modules' && !item.startsWith('.')) {
                // This is a package directory
                const subSize = analyzeDirectory(itemPath, item);
                packages.push({ name: item, size: subSize });
                totalSize += subSize;
              } else {
                packageSize += analyzeDirectory(itemPath, packageName);
              }
            } else {
              packageSize += stats.size;
              fileCount++;
            }
          } catch (err) {
            // Skip files that can't be accessed
          }
        });
        
        return packageSize;
      } catch (err) {
        return 0;
      }
    }

    analyzeDirectory('node_modules');

    // Sort packages by size
    packages.sort((a, b) => b.size - a.size);

    log(`📊 Total node_modules Size: ${formatBytes(totalSize)}`, 'cyan');
    log(`📁 Total Files: ${fileCount}`, 'cyan');
    log(`📦 Total Packages: ${packages.length}`, 'cyan');

    log('\n🔍 Largest Packages:', 'yellow');
    packages.slice(0, 15).forEach((pkg, index) => {
      const color = pkg.size > 50 * 1024 * 1024 ? 'red' : pkg.size > 10 * 1024 * 1024 ? 'yellow' : 'green';
      log(`  ${index + 1}. ${pkg.name} - ${formatBytes(pkg.size)}`, color);
    });

    return {
      totalSize,
      fileCount,
      packageCount: packages.length,
      largestPackage: packages[0]
    };

  } catch (error) {
    log(`❌ Error analyzing node_modules: ${error.message}`, 'red');
    return null;
  }
}

function generatePerformanceReport() {
  log('\n🚀 PERFORMANCE AUDIT REPORT', 'bold');
  log('=' .repeat(60), 'blue');

  const depAnalysis = analyzePackageJson();
  const bundleAnalysis = analyzeBundleSize();
  const nodeModulesAnalysis = analyzeNodeModules();

  // Generate overall score
  let score = 100;
  const issues = [];

  if (depAnalysis) {
    if (depAnalysis.totalDeps > 80) {
      score -= 20;
      issues.push('Too many dependencies (>80)');
    }
    if (depAnalysis.heavyDeps > 2) {
      score -= 15;
      issues.push('Multiple heavy/duplicate dependencies');
    }
  }

  if (bundleAnalysis) {
    if (bundleAnalysis.totalSize > 5 * 1024 * 1024) {
      score -= 25;
      issues.push('Bundle size too large (>5MB)');
    }
    if (bundleAnalysis.jsSize > 3 * 1024 * 1024) {
      score -= 15;
      issues.push('JavaScript bundle too large (>3MB)');
    }
  }

  if (nodeModulesAnalysis) {
    if (nodeModulesAnalysis.totalSize > 500 * 1024 * 1024) {
      score -= 10;
      issues.push('node_modules too large (>500MB)');
    }
  }

  log('\n📊 PERFORMANCE SCORE', 'bold');
  log('=' .repeat(30), 'blue');
  
  const scoreColor = score >= 80 ? 'green' : score >= 60 ? 'yellow' : 'red';
  log(`🎯 Overall Score: ${score}/100`, scoreColor);

  if (issues.length > 0) {
    log('\n⚠️  ISSUES FOUND:', 'yellow');
    issues.forEach((issue, index) => {
      log(`${index + 1}. ${issue}`, 'yellow');
    });
  }

  log('\n💡 OPTIMIZATION RECOMMENDATIONS:', 'magenta');
  
  if (depAnalysis && depAnalysis.totalDeps > 60) {
    log('  📦 Audit and remove unused dependencies', 'cyan');
  }
  
  if (bundleAnalysis && bundleAnalysis.totalSize > 3 * 1024 * 1024) {
    log('  ✂️  Implement code splitting and lazy loading', 'cyan');
    log('  🗜️  Enable compression (gzip/brotli)', 'cyan');
  }
  
  log('  🌳 Implement tree shaking for unused code', 'cyan');
  log('  📊 Use bundle analyzer to identify optimization opportunities', 'cyan');
  log('  ⚡ Consider switching to lighter alternatives for heavy libraries', 'cyan');

  return {
    score,
    issues,
    depAnalysis,
    bundleAnalysis,
    nodeModulesAnalysis
  };
}

// Run performance audit
if (require.main === module) {
  generatePerformanceReport();
}

module.exports = { generatePerformanceReport };
