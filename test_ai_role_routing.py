#!/usr/bin/env python3
"""
AI Role-Based Routing Test
Tests the new role-specific AI routes to ensure each role has access to their correct AI paths.
"""

import requests
import json
import sys
from typing import Dict, List

# Test configuration
BASE_URL = "http://localhost:3000"
API_BASE_URL = "http://localhost:8000"

# Role-specific AI routes mapping
ROLE_AI_ROUTES = {
    'user': ['/user/ai', '/user/ai/analytics', '/user/ai/intelligence'],
    'entrepreneur': ['/entrepreneur/ai', '/entrepreneur/ai/analytics', '/entrepreneur/ai/intelligence'],
    'mentor': ['/mentor/ai', '/mentor/ai/analytics', '/mentor/ai/intelligence'],
    'investor': ['/investor/ai', '/investor/ai/analytics', '/investor/ai/intelligence'],
    'moderator': ['/moderator/ai', '/moderator/ai/analytics', '/moderator/ai/intelligence'],
    'admin': ['/admin/ai', '/admin/ai/analytics', '/admin/ai/intelligence'],
    'super_admin': ['/super_admin/ai', '/super_admin/ai/analytics', '/super_admin/ai/intelligence']
}

# Test users for each role
TEST_USERS = {
    'user': {'username': 'testuser', 'password': 'testpass123'},
    'entrepreneur': {'username': 'entrepreneur1', 'password': 'testpass123'},
    'mentor': {'username': 'mentor1', 'password': 'testpass123'},
    'investor': {'username': 'investor1', 'password': 'testpass123'},
    'moderator': {'username': 'moderator1', 'password': 'testpass123'},
    'admin': {'username': 'admin1', 'password': 'testpass123'},
    'super_admin': {'username': 'superadmin', 'password': 'testpass123'}
}

def test_role_ai_access(role: str, credentials: Dict[str, str]) -> bool:
    """Test AI route access for a specific role"""
    print(f"\n🧪 Testing AI routes for role: {role}")
    
    try:
        # Step 1: Login
        login_response = requests.post(
            f"{API_BASE_URL}/api/auth/login/",
            json=credentials,
            headers={'Content-Type': 'application/json'}
        )
        
        if login_response.status_code != 200:
            print(f"   ❌ Login failed for {role}: {login_response.status_code}")
            return False
            
        login_data = login_response.json()
        token = login_data.get('access_token')
        user_data = login_data.get('user', {})
        actual_role = user_data.get('user_role')
        
        print(f"   ✅ Login successful! Actual role: {actual_role}")
        
        # Step 2: Test role-specific AI routes
        ai_routes = ROLE_AI_ROUTES.get(role, [])
        headers = {'Authorization': f'Bearer {token}'}
        
        success_count = 0
        total_routes = len(ai_routes)
        
        for route in ai_routes:
            try:
                # Test route access (we expect 200 or redirect, not 403/401)
                response = requests.get(f"{BASE_URL}{route}", headers=headers, allow_redirects=False)
                
                if response.status_code in [200, 302, 301]:
                    print(f"   ✅ {route} - Accessible (Status: {response.status_code})")
                    success_count += 1
                elif response.status_code in [401, 403]:
                    print(f"   ❌ {route} - Access denied (Status: {response.status_code})")
                else:
                    print(f"   ⚠️  {route} - Unexpected status: {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {route} - Error: {str(e)}")
        
        # Step 3: Test that other roles' AI routes are NOT accessible
        other_routes_blocked = 0
        other_routes_total = 0
        
        for other_role, other_routes in ROLE_AI_ROUTES.items():
            if other_role != role:
                for other_route in other_routes:
                    other_routes_total += 1
                    try:
                        response = requests.get(f"{BASE_URL}{other_route}", headers=headers, allow_redirects=False)
                        if response.status_code in [401, 403, 404]:
                            other_routes_blocked += 1
                        elif response.status_code in [200, 302, 301]:
                            # This might be OK for admin/super_admin who can access multiple routes
                            if role in ['admin', 'super_admin'] and other_role in ['admin', 'super_admin']:
                                other_routes_blocked += 1  # Count as properly handled
                            else:
                                print(f"   ⚠️  {other_route} (from {other_role}) - Unexpectedly accessible")
                    except Exception as e:
                        other_routes_blocked += 1  # Network errors count as blocked
        
        print(f"   📊 Role-specific routes: {success_count}/{total_routes} accessible")
        print(f"   🔒 Other roles' routes: {other_routes_blocked}/{other_routes_total} properly blocked")
        
        # Success criteria: All own routes accessible, most other routes blocked
        own_routes_success = success_count == total_routes
        security_success = other_routes_blocked >= (other_routes_total * 0.8)  # 80% threshold
        
        return own_routes_success and security_success
        
    except Exception as e:
        print(f"   ❌ Test failed for {role}: {str(e)}")
        return False

def test_ai_route_navigation():
    """Test that AI navigation uses correct role-specific paths"""
    print("\n🧭 Testing AI Navigation Configuration")
    
    # This would require frontend testing, so we'll just validate the route mappings
    for role, routes in ROLE_AI_ROUTES.items():
        expected_base = f"/{role}/ai" if role != 'super_admin' else "/super_admin/ai"
        
        base_route = routes[0]  # First route should be the base AI route
        if base_route == expected_base:
            print(f"   ✅ {role}: Correct base route {base_route}")
        else:
            print(f"   ❌ {role}: Expected {expected_base}, got {base_route}")
    
    return True

def main():
    """Run all AI role-based routing tests"""
    print("🚀 Starting AI Role-Based Routing Tests")
    print("=" * 50)
    
    # Test navigation configuration
    nav_success = test_ai_route_navigation()
    
    # Test each role's AI access
    role_results = {}
    for role, credentials in TEST_USERS.items():
        role_results[role] = test_role_ai_access(role, credentials)
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    print(f"Navigation Config: {'✅ PASS' if nav_success else '❌ FAIL'}")
    
    passed_roles = sum(1 for success in role_results.values() if success)
    total_roles = len(role_results)
    
    for role, success in role_results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{role.ljust(12)}: {status}")
    
    print(f"\nOverall: {passed_roles}/{total_roles} roles passed")
    
    if passed_roles == total_roles and nav_success:
        print("🎉 All AI role-based routing tests PASSED!")
        return 0
    else:
        print("💥 Some AI role-based routing tests FAILED!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
