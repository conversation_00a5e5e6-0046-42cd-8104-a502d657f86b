/**
 * Production Performance Monitoring System
 * Tracks critical metrics and errors in production environment
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  url: string;
  userAgent: string;
}

interface ErrorReport {
  message: string;
  stack?: string;
  url: string;
  timestamp: number;
  userAgent: string;
  userId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface UserSession {
  sessionId: string;
  userId?: string;
  startTime: number;
  lastActivity: number;
  pageViews: number;
  errors: number;
}

class ProductionMonitor {
  private isProduction: boolean;
  private sessionId: string;
  private session: UserSession;
  private metricsBuffer: PerformanceMetric[] = [];
  private errorsBuffer: ErrorReport[] = [];
  private flushInterval: number = 30000; // 30 seconds
  private flushIntervalId: number | null = null;
  private mutationObserver: MutationObserver | null = null;
  private performanceObserver: PerformanceObserver | null = null;

  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.sessionId = this.generateSessionId();
    this.session = {
      sessionId: this.sessionId,
      startTime: Date.now(),
      lastActivity: Date.now(),
      pageViews: 0,
      errors: 0
    };

    if (this.isProduction) {
      this.initializeMonitoring();
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeMonitoring(): void {
    // Track page load performance
    this.trackPageLoad();
    
    // Track navigation performance
    this.trackNavigation();
    
    // Track errors
    this.trackErrors();
    
    // Track user interactions
    this.trackUserActivity();
    
    // Set up periodic flushing
    this.flushIntervalId = window.setInterval(() => this.flushMetrics(), this.flushInterval);

    // Flush on page unload
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  }

  private trackPageLoad(): void {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        this.recordMetric('page_load_time', navigation.loadEventEnd - navigation.loadEventStart);
        this.recordMetric('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart);
        this.recordMetric('first_paint', this.getFirstPaint());
        this.recordMetric('largest_contentful_paint', this.getLargestContentfulPaint());
      }
      
      this.session.pageViews++;
    });
  }

  private trackNavigation(): void {
    // Track SPA navigation
    let lastUrl = window.location.href;

    this.mutationObserver = new MutationObserver(() => {
      if (window.location.href !== lastUrl) {
        lastUrl = window.location.href;
        this.session.pageViews++;
        this.session.lastActivity = Date.now();
        this.recordMetric('spa_navigation', Date.now());
      }
    });

    this.mutationObserver.observe(document.body, { childList: true, subtree: true });
  }

  private trackErrors(): void {
    // JavaScript errors
    window.addEventListener('error', (event) => {
      this.recordError({
        message: event.message,
        stack: event.error?.stack,
        url: event.filename || window.location.href,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        severity: 'high'
      });
    });

    // Promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        url: window.location.href,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        severity: 'medium'
      });
    });
  }

  private trackUserActivity(): void {
    const events = ['click', 'scroll', 'keydown', 'mousemove'];
    
    events.forEach(eventType => {
      document.addEventListener(eventType, () => {
        this.session.lastActivity = Date.now();
      }, { passive: true });
    });
  }

  private getFirstPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : 0;
  }

  private getLargestContentfulPaint(): number {
    try {
      if (this.performanceObserver) {
        this.performanceObserver.disconnect();
      }

      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        return lastEntry.startTime;
      });

      this.performanceObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      return 0; // Return 0 for now, as this is async
    } catch (error) {
      return 0;
    }
  }

  public recordMetric(name: string, value: number): void {
    if (!this.isProduction) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    this.metricsBuffer.push(metric);

    // Auto-flush if buffer is getting large
    if (this.metricsBuffer.length >= 50) {
      this.flushMetrics();
    }
  }

  public recordError(error: Omit<ErrorReport, 'timestamp' | 'userAgent'>): void {
    if (!this.isProduction) return;

    const errorReport: ErrorReport = {
      ...error,
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    };

    this.errorsBuffer.push(errorReport);
    this.session.errors++;

    // Immediately flush critical errors
    if (error.severity === 'critical') {
      this.flushMetrics();
    }
  }

  public recordCustomEvent(eventName: string, data?: any): void {
    if (!this.isProduction) return;

    this.recordMetric(`custom_${eventName}`, 1);
    
    // Store additional data if provided
    if (data) {
      console.log(`[ProductionMonitor] Custom event: ${eventName}`, data);
    }
  }

  private async flushMetrics(): Promise<void> {
    if (this.metricsBuffer.length === 0 && this.errorsBuffer.length === 0) return;

    const payload = {
      session: this.session,
      metrics: [...this.metricsBuffer],
      errors: [...this.errorsBuffer],
      timestamp: Date.now()
    };

    try {
      // In a real production environment, you would send this to your monitoring service
      // For now, we'll use a placeholder endpoint
      await fetch('/api/monitoring/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      // Clear buffers after successful send
      this.metricsBuffer = [];
      this.errorsBuffer = [];
    } catch (error) {
      // If monitoring fails, store in localStorage as fallback
      this.storeMetricsLocally(payload);
    }
  }

  private storeMetricsLocally(payload: any): void {
    try {
      const stored = localStorage.getItem('production_metrics') || '[]';
      const metrics = JSON.parse(stored);
      metrics.push(payload);
      
      // Keep only last 10 entries to prevent storage overflow
      if (metrics.length > 10) {
        metrics.splice(0, metrics.length - 10);
      }
      
      localStorage.setItem('production_metrics', JSON.stringify(metrics));
    } catch (error) {
      console.warn('[ProductionMonitor] Failed to store metrics locally:', error);
    }
  }

  public getSessionInfo(): UserSession {
    return { ...this.session };
  }

  public isEnabled(): boolean {
    return this.isProduction;
  }

  private handleBeforeUnload = () => {
    this.flushMetrics();
  };

  public cleanup(): void {
    // Clear interval
    if (this.flushIntervalId) {
      clearInterval(this.flushIntervalId);
      this.flushIntervalId = null;
    }

    // Disconnect observers
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
      this.mutationObserver = null;
    }

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }

    // Remove event listeners
    window.removeEventListener('beforeunload', this.handleBeforeUnload);

    // Final flush
    this.flushMetrics();
  }
}

// Create singleton instance
export const productionMonitor = new ProductionMonitor();

// Export types for external use
export type { PerformanceMetric, ErrorReport, UserSession };
