import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { getCurrentUserRoles, getAvailableRoles } from '../services/roleApi';

// Types
export interface UserRole {
  id: number;
  name: string;
  display_name: string;
  permission_level: string;
  assigned_at?: string;
  expires_at?: string;
}

export interface AvailableRole {
  id: number;
  name: string;
  display_name: string;
  description: string;
  permission_level: string;
  requires_approval: boolean;
}

export interface RoleState {
  // Current user roles
  userRoles: UserRole[];
  primaryRole: string;
  permissionLevel: string;
  
  // Available roles
  availableRoles: AvailableRole[];
  
  // Loading states
  isLoadingUserRoles: boolean;
  isLoadingAvailableRoles: boolean;
  
  // Error states
  userRolesError: string | null;
  availableRolesError: string | null;
  
  // Cache timestamps
  lastUserRolesFetch: number | null;
  lastAvailableRolesFetch: number | null;
}

const initialState: RoleState = {
  userRoles: [],
  primaryRole: 'user',
  permissionLevel: 'read',
  availableRoles: [],
  isLoadingUserRoles: false,
  isLoadingAvailableRoles: false,
  userRolesError: null,
  availableRolesError: null,
  lastUserRolesFetch: null,
  lastAvailableRolesFetch: null,
};

// Async thunks
export const fetchUserRoles = createAsyncThunk(
  'roles/fetchUserRoles',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getCurrentUserRoles();
      if (!response) {
        throw new Error('Failed to fetch user roles');
      }
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch user roles');
    }
  }
);

export const fetchAvailableRoles = createAsyncThunk(
  'roles/fetchAvailableRoles',
  async (_, { rejectWithValue }) => {
    try {
      const response = await getAvailableRoles();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch available roles');
    }
  }
);

// Slice
const roleSlice = createSlice({
  name: 'roles',
  initialState,
  reducers: {
    clearRoleErrors: (state) => {
      state.userRolesError = null;
      state.availableRolesError = null;
    },
    
    setPrimaryRole: (state, action: PayloadAction<string>) => {
      state.primaryRole = action.payload;
    },
    
    setPermissionLevel: (state, action: PayloadAction<string>) => {
      state.permissionLevel = action.payload;
    },
    
    resetRoleState: (state) => {
      state.userRoles = [];
      state.primaryRole = 'user';
      state.permissionLevel = 'read';
      state.userRolesError = null;
      state.lastUserRolesFetch = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch user roles
    builder
      .addCase(fetchUserRoles.pending, (state) => {
        state.isLoadingUserRoles = true;
        state.userRolesError = null;
      })
      .addCase(fetchUserRoles.fulfilled, (state, action) => {
        state.isLoadingUserRoles = false;
        state.userRoles = action.payload.all_roles || [];
        state.primaryRole = action.payload.primary_role || 'user';
        
        // Determine permission level from primary role
        const primaryRoleData = state.userRoles.find(role => role.name === state.primaryRole);
        state.permissionLevel = primaryRoleData?.permission_level || 'read';
        
        state.lastUserRolesFetch = Date.now();
      })
      .addCase(fetchUserRoles.rejected, (state, action) => {
        state.isLoadingUserRoles = false;
        state.userRolesError = action.payload as string;
      });

    // Fetch available roles
    builder
      .addCase(fetchAvailableRoles.pending, (state) => {
        state.isLoadingAvailableRoles = true;
        state.availableRolesError = null;
      })
      .addCase(fetchAvailableRoles.fulfilled, (state, action) => {
        state.isLoadingAvailableRoles = false;
        state.availableRoles = action.payload;
        state.lastAvailableRolesFetch = Date.now();
      })
      .addCase(fetchAvailableRoles.rejected, (state, action) => {
        state.isLoadingAvailableRoles = false;
        state.availableRolesError = action.payload as string;
      });
  },
});

export const {
  clearRoleErrors,
  setPrimaryRole,
  setPermissionLevel,
  resetRoleState,
} = roleSlice.actions;

export default roleSlice.reducer;

// Selectors
export const selectUserRoles = (state: { roles: RoleState }) => state.roles.userRoles;
export const selectPrimaryRole = (state: { roles: RoleState }) => state.roles.primaryRole;
export const selectPermissionLevel = (state: { roles: RoleState }) => state.roles.permissionLevel;
export const selectAvailableRoles = (state: { roles: RoleState }) => state.roles.availableRoles;
export const selectIsLoadingRoles = (state: { roles: RoleState }) => 
  state.roles.isLoadingUserRoles || state.roles.isLoadingAvailableRoles;
// ✅ MEMOIZED SELECTOR TO PREVENT UNNECESSARY RERENDERS
export const selectRoleErrors = createSelector(
  [(state: { roles: RoleState }) => state.roles.userRolesError,
   (state: { roles: RoleState }) => state.roles.availableRolesError],
  (userRolesError, availableRolesError) => ({
    userRolesError,
    availableRolesError,
  })
);

// Helper selectors
export const selectHasRole = (roleName: string) => (state: { roles: RoleState }) =>
  state.roles.userRoles.some(role => role.name === roleName);

export const selectIsAdmin = (state: { roles: RoleState }) =>
  ['admin', 'super_admin'].includes(state.roles.primaryRole) ||
  state.roles.userRoles.some(role => ['admin', 'super_admin'].includes(role.name));

export const selectIsModerator = (state: { roles: RoleState }) =>
  ['moderator', 'admin', 'super_admin'].includes(state.roles.primaryRole) ||
  state.roles.userRoles.some(role => ['moderator', 'admin', 'super_admin'].includes(role.name));

export const selectCanModerate = (state: { roles: RoleState }) =>
  ['moderate', 'admin', 'super_admin'].includes(state.roles.permissionLevel);

export const selectCanAdmin = (state: { roles: RoleState }) =>
  ['admin', 'super_admin'].includes(state.roles.permissionLevel);
