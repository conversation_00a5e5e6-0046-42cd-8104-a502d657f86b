"""
User role-specific views and endpoints
Provides dashboard and analytics endpoints for regular users
"""

from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth.models import User
from django.db.models import Count, Q

# Import models
try:
    from incubator.models import BusinessIdea
except ImportError:
    BusinessIdea = None

try:
    from forums.models import ForumPost, ForumThread
except ImportError:
    ForumPost = None
    ForumThread = None

try:
    from api.models import Event, Resource
except ImportError:
    Event = None
    Resource = None


class UserViewSet(viewsets.ViewSet):
    """
    ViewSet for regular user role-specific endpoints
    """
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """
        Get dashboard statistics for regular users
        """
        try:
            user = request.user
            now = timezone.now()
            thirty_days_ago = now - timedelta(days=30)

            # Basic user statistics
            stats = {
                'profile': {
                    'username': user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'email': user.email,
                    'date_joined': user.date_joined.isoformat() if user.date_joined else None,
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                },
                'activity': {
                    'days_since_joined': (now - user.date_joined).days if user.date_joined else 0,
                    'last_login_days_ago': (now - user.last_login).days if user.last_login else None,
                },
                'content': {
                    'business_ideas': 0,
                    'forum_posts': 0,
                    'events_attended': 0,
                    'resources_accessed': 0,
                },
                'engagement': {
                    'total_interactions': 0,
                    'monthly_activity': 0,
                    'community_score': 0,
                },
                'timestamp': now.isoformat()
            }

            # Get user's business ideas if available
            if BusinessIdea:
                try:
                    user_ideas = BusinessIdea.objects.filter(creator=user)
                    stats['content']['business_ideas'] = user_ideas.count()
                    stats['content']['recent_ideas'] = user_ideas.filter(
                        created_at__gte=thirty_days_ago
                    ).count()
                except Exception:
                    pass

            # Get user's forum activity if available
            if ForumPost:
                try:
                    user_posts = ForumPost.objects.filter(author=user)
                    stats['content']['forum_posts'] = user_posts.count()
                    stats['content']['recent_posts'] = user_posts.filter(
                        created_at__gte=thirty_days_ago
                    ).count()
                except Exception:
                    pass

            # Get user's event participation if available
            if Event:
                try:
                    # This would need to be adjusted based on your Event model structure
                    stats['content']['events_attended'] = 0  # Placeholder
                except Exception:
                    pass

            # Calculate engagement metrics
            total_content = (
                stats['content']['business_ideas'] + 
                stats['content']['forum_posts']
            )
            stats['engagement']['total_interactions'] = total_content
            
            # Simple community score based on activity
            if stats['activity']['days_since_joined'] > 0:
                stats['engagement']['community_score'] = min(
                    100, 
                    (total_content * 10) + 
                    (stats['activity']['days_since_joined'] * 0.5)
                )

            return Response(stats)

        except Exception as e:
            return Response(
                {'error': f'Failed to fetch user dashboard stats: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def recent_activity(self, request):
        """
        Get recent activity for the user
        """
        try:
            user = request.user
            now = timezone.now()
            seven_days_ago = now - timedelta(days=7)

            activities = []

            # Get recent business ideas if available
            if BusinessIdea:
                try:
                    recent_ideas = BusinessIdea.objects.filter(
                        creator=user,
                        created_at__gte=seven_days_ago
                    ).order_by('-created_at')[:5]
                    
                    for idea in recent_ideas:
                        activities.append({
                            'type': 'business_idea',
                            'title': f'Created business idea: {idea.title}',
                            'description': idea.description[:100] + '...' if len(idea.description) > 100 else idea.description,
                            'timestamp': idea.created_at.isoformat(),
                            'url': f'/dashboard/business-ideas/{idea.id}/'
                        })
                except Exception:
                    pass

            # Get recent forum posts if available
            if ForumPost:
                try:
                    recent_posts = ForumPost.objects.filter(
                        author=user,
                        created_at__gte=seven_days_ago
                    ).order_by('-created_at')[:5]
                    
                    for post in recent_posts:
                        activities.append({
                            'type': 'forum_post',
                            'title': f'Posted in forum: {post.title}',
                            'description': post.content[:100] + '...' if len(post.content) > 100 else post.content,
                            'timestamp': post.created_at.isoformat(),
                            'url': f'/dashboard/forums/posts/{post.id}/'
                        })
                except Exception:
                    pass

            # Sort activities by timestamp (most recent first)
            activities.sort(key=lambda x: x['timestamp'], reverse=True)

            return Response({
                'activities': activities[:10],  # Return top 10 most recent
                'total_count': len(activities),
                'time_range': '7_days',
                'timestamp': now.isoformat()
            })

        except Exception as e:
            return Response(
                {'error': f'Failed to fetch user recent activity: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """
        Get basic analytics for the user
        """
        try:
            user = request.user
            now = timezone.now()

            # Basic analytics for regular users
            analytics = {
                'overview': {
                    'account_age_days': (now - user.date_joined).days if user.date_joined else 0,
                    'total_logins': 0,  # Would need session tracking
                    'average_session_duration': 0,  # Would need session tracking
                },
                'content_creation': {
                    'business_ideas_created': 0,
                    'forum_posts_created': 0,
                    'comments_made': 0,
                },
                'engagement': {
                    'events_attended': 0,
                    'resources_viewed': 0,
                    'community_interactions': 0,
                },
                'growth': {
                    'monthly_activity_trend': [],
                    'content_creation_trend': [],
                },
                'timestamp': now.isoformat()
            }

            # Populate with actual data if models are available
            if BusinessIdea:
                try:
                    analytics['content_creation']['business_ideas_created'] = BusinessIdea.objects.filter(creator=user).count()
                except Exception:
                    pass

            if ForumPost:
                try:
                    analytics['content_creation']['forum_posts_created'] = ForumPost.objects.filter(author=user).count()
                except Exception:
                    pass

            return Response(analytics)

        except Exception as e:
            return Response(
                {'error': f'Failed to fetch user analytics: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
