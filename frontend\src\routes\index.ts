/**
 * ✅ CLEAN ROUTING SYSTEM EXPORTS
 * Simple, maintainable routing with Redux role management
 */

export { default as AppRoutes } from './AppRoutes';

// Route paths for easy reference
export const ROUTES = {
  // Public routes
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  REG<PERSON><PERSON>ATION_SUCCESS: '/registration-success',
  REGISTER_SUCCESS: '/register/success',
  LOGOUT: '/logout',
  FEATURES: '/features',
  ACCESS_DENIED: '/access-denied',

  // Dashboard routes (role-specific)
  DASHBOARD: '/dashboard',
  USER_DASHBOARD: '/user/dashboard',
  ENTREPRENEUR_DASHBOARD: '/entrepreneur/dashboard',
  MENTOR_DASHBOARD: '/mentor/dashboard',
  INVESTOR_DASHBOARD: '/investor/dashboard',
  ADMIN_DASHBOARD: '/admin/dashboard',
  SUPER_ADMIN_DASHBOARD: '/super-admin/dashboard',
  MODERATOR_DASHBOARD: '/moderator/dashboard',

  // Other protected routes
  PROFILE: '/profile',
  AI_CHAT: '/ai-chat',

  // Role-specific AI Chat routes
  USER_AI_CHAT: '/user/ai-chat',
  ENTREPRENEUR_AI_CHAT: '/entrepreneur/ai-chat',
  MENTOR_AI_CHAT: '/mentor/ai-chat',
  INVESTOR_AI_CHAT: '/investor/ai-chat',
  ADMIN_AI_CHAT: '/admin/ai-chat',
  SUPER_ADMIN_AI_CHAT: '/super-admin/ai-chat',
  MODERATOR_AI_CHAT: '/moderator/ai-chat',

  // Role-specific Profile routes
  USER_PROFILE: '/user/profile',
  ENTREPRENEUR_PROFILE: '/entrepreneur/profile',
  MENTOR_PROFILE: '/mentor/profile',
  INVESTOR_PROFILE: '/investor/profile',
  ADMIN_PROFILE: '/admin/profile',
  SUPER_ADMIN_PROFILE: '/super-admin/profile',
  MODERATOR_PROFILE: '/moderator/profile',

  // Role-specific Settings routes
  USER_SETTINGS: '/user/settings',
  ENTREPRENEUR_SETTINGS: '/entrepreneur/settings',
  MENTOR_SETTINGS: '/mentor/settings',
  INVESTOR_SETTINGS: '/investor/settings',
  ADMIN_SETTINGS: '/admin/settings',
  SUPER_ADMIN_SETTINGS: '/super-admin/settings',
  MODERATOR_SETTINGS: '/moderator/settings',

  // 404
  NOT_FOUND: '*'
} as const;

export type RouteKey = keyof typeof ROUTES;
export type RoutePath = typeof ROUTES[RouteKey];
