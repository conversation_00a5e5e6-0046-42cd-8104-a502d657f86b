/**
 * Application Page
 * Comprehensive application form for joining the incubator program
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicInput, ArabicTypography, ArabicProgress } from '../components/ui/ArabicOptimizedComponents';
import { 
  User, 
  Briefcase, 
  Target, 
  DollarSign, 
  Users, 
  FileText,
  CheckCircle,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';

interface ApplicationData {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  country: string;
  city: string;
  
  // Business Information
  businessName: string;
  businessDescription: string;
  industry: string;
  stage: string;
  foundingDate: string;
  
  // Team Information
  teamSize: number;
  coFounders: string;
  keyTeamMembers: string;
  
  // Business Details
  problemStatement: string;
  solution: string;
  targetMarket: string;
  businessModel: string;
  
  // Funding Information
  fundingStage: string;
  fundingAmount: string;
  previousFunding: string;
  
  // Goals and Expectations
  goals: string;
  expectations: string;
  timeline: string;
  
  // Additional Information
  experience: string;
  challenges: string;
  additionalInfo: string;
}

const ApplicationPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [applicationData, setApplicationData] = useState<ApplicationData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    country: '',
    city: '',
    businessName: '',
    businessDescription: '',
    industry: '',
    stage: '',
    foundingDate: '',
    teamSize: 1,
    coFounders: '',
    keyTeamMembers: '',
    problemStatement: '',
    solution: '',
    targetMarket: '',
    businessModel: '',
    fundingStage: '',
    fundingAmount: '',
    previousFunding: '',
    goals: '',
    expectations: '',
    timeline: '',
    experience: '',
    challenges: '',
    additionalInfo: ''
  });
  const [loading, setLoading] = useState(false);

  const totalSteps = 6;
  const progress = (currentStep / totalSteps) * 100;

  const industries = [
    { value: 'fintech', label: language === 'ar' ? 'التكنولوجيا المالية' : 'FinTech' },
    { value: 'healthtech', label: language === 'ar' ? 'التكنولوجيا الصحية' : 'HealthTech' },
    { value: 'edtech', label: language === 'ar' ? 'التكنولوجيا التعليمية' : 'EdTech' },
    { value: 'ecommerce', label: language === 'ar' ? 'التجارة الإلكترونية' : 'E-commerce' },
    { value: 'logistics', label: language === 'ar' ? 'اللوجستيات' : 'Logistics' },
    { value: 'other', label: language === 'ar' ? 'أخرى' : 'Other' }
  ];

  const businessStages = [
    { value: 'idea', label: language === 'ar' ? 'مرحلة الفكرة' : 'Idea Stage' },
    { value: 'prototype', label: language === 'ar' ? 'مرحلة النموذج الأولي' : 'Prototype Stage' },
    { value: 'mvp', label: language === 'ar' ? 'المنتج الأدنى القابل للتطبيق' : 'MVP Stage' },
    { value: 'early_revenue', label: language === 'ar' ? 'الإيرادات المبكرة' : 'Early Revenue' },
    { value: 'growth', label: language === 'ar' ? 'مرحلة النمو' : 'Growth Stage' }
  ];

  const fundingStages = [
    { value: 'pre_seed', label: language === 'ar' ? 'ما قبل البذرة' : 'Pre-Seed' },
    { value: 'seed', label: language === 'ar' ? 'البذرة' : 'Seed' },
    { value: 'series_a', label: language === 'ar' ? 'الجولة أ' : 'Series A' },
    { value: 'series_b', label: language === 'ar' ? 'الجولة ب' : 'Series B' },
    { value: 'not_seeking', label: language === 'ar' ? 'لا أسعى للتمويل' : 'Not Seeking Funding' }
  ];

  const handleInputChange = (field: keyof ApplicationData, value: string | number) => {
    setApplicationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    try {
      // Submit application data
      console.log('Submitting application:', applicationData);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Navigate to success page
      navigate('/application/success');
    } catch (error) {
      console.error('Error submitting application:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <User className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'المعلومات الشخصية' : 'Personal Information'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'أخبرنا عن نفسك' : 'Tell us about yourself'}
              </ArabicTypography>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ArabicInput
                label={language === 'ar' ? 'الاسم الأول' : 'First Name'}
                value={applicationData.firstName}
                onChange={(value) => handleInputChange('firstName', value)}
                required
              />
              <ArabicInput
                label={language === 'ar' ? 'اسم العائلة' : 'Last Name'}
                value={applicationData.lastName}
                onChange={(value) => handleInputChange('lastName', value)}
                required
              />
              <ArabicInput
                label={language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                type="email"
                value={applicationData.email}
                onChange={(value) => handleInputChange('email', value)}
                required
              />
              <ArabicInput
                label={language === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                type="tel"
                value={applicationData.phone}
                onChange={(value) => handleInputChange('phone', value)}
                required
              />
              <ArabicInput
                label={language === 'ar' ? 'البلد' : 'Country'}
                value={applicationData.country}
                onChange={(value) => handleInputChange('country', value)}
                required
              />
              <ArabicInput
                label={language === 'ar' ? 'المدينة' : 'City'}
                value={applicationData.city}
                onChange={(value) => handleInputChange('city', value)}
                required
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Briefcase className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'معلومات العمل' : 'Business Information'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'أخبرنا عن فكرة عملك' : 'Tell us about your business idea'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <ArabicInput
                label={language === 'ar' ? 'اسم العمل' : 'Business Name'}
                value={applicationData.businessName}
                onChange={(value) => handleInputChange('businessName', value)}
                required
              />
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'وصف العمل' : 'Business Description'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.businessDescription}
                  onChange={(e) => handleInputChange('businessDescription', e.target.value)}
                  placeholder={language === 'ar' ? 'اشرح فكرة عملك بالتفصيل...' : 'Describe your business idea in detail...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'الصناعة' : 'Industry'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={applicationData.industry}
                    onChange={(e) => handleInputChange('industry', e.target.value)}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'اختر الصناعة' : 'Select Industry'}</option>
                    {industries.map((industry) => (
                      <option key={industry.value} value={industry.value}>
                        {industry.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                    {language === 'ar' ? 'مرحلة العمل' : 'Business Stage'}
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                    value={applicationData.stage}
                    onChange={(e) => handleInputChange('stage', e.target.value)}
                    dir={isRTL ? 'rtl' : 'ltr'}
                  >
                    <option value="">{language === 'ar' ? 'اختر المرحلة' : 'Select Stage'}</option>
                    {businessStages.map((stage) => (
                      <option key={stage.value} value={stage.value}>
                        {stage.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <ArabicInput
                label={language === 'ar' ? 'تاريخ التأسيس' : 'Founding Date'}
                type="date"
                value={applicationData.foundingDate}
                onChange={(value) => handleInputChange('foundingDate', value)}
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Users className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'معلومات الفريق' : 'Team Information'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'أخبرنا عن فريقك' : 'Tell us about your team'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <ArabicInput
                label={language === 'ar' ? 'حجم الفريق' : 'Team Size'}
                type="number"
                value={applicationData.teamSize.toString()}
                onChange={(value) => handleInputChange('teamSize', parseInt(value) || 1)}
                required
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'الشركاء المؤسسون' : 'Co-Founders'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.coFounders}
                  onChange={(e) => handleInputChange('coFounders', e.target.value)}
                  placeholder={language === 'ar' ? 'اذكر الشركاء المؤسسين وخبراتهم...' : 'List co-founders and their backgrounds...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'أعضاء الفريق الرئيسيون' : 'Key Team Members'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.keyTeamMembers}
                  onChange={(e) => handleInputChange('keyTeamMembers', e.target.value)}
                  placeholder={language === 'ar' ? 'اذكر أعضاء الفريق الرئيسيين وأدوارهم...' : 'List key team members and their roles...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <Target className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'تفاصيل العمل' : 'Business Details'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'اشرح مشكلتك وحلك' : 'Explain your problem and solution'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'بيان المشكلة' : 'Problem Statement'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.problemStatement}
                  onChange={(e) => handleInputChange('problemStatement', e.target.value)}
                  placeholder={language === 'ar' ? 'ما هي المشكلة التي تحلها؟' : 'What problem are you solving?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'الحل' : 'Solution'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.solution}
                  onChange={(e) => handleInputChange('solution', e.target.value)}
                  placeholder={language === 'ar' ? 'كيف تحل هذه المشكلة؟' : 'How do you solve this problem?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'السوق المستهدف' : 'Target Market'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.targetMarket}
                  onChange={(e) => handleInputChange('targetMarket', e.target.value)}
                  placeholder={language === 'ar' ? 'من هم عملاؤك المستهدفون؟' : 'Who are your target customers?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'نموذج العمل' : 'Business Model'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.businessModel}
                  onChange={(e) => handleInputChange('businessModel', e.target.value)}
                  placeholder={language === 'ar' ? 'كيف ستحقق الإيرادات؟' : 'How will you generate revenue?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <DollarSign className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'معلومات التمويل' : 'Funding Information'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'أخبرنا عن احتياجات التمويل' : 'Tell us about your funding needs'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'مرحلة التمويل' : 'Funding Stage'}
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  value={applicationData.fundingStage}
                  onChange={(e) => handleInputChange('fundingStage', e.target.value)}
                  dir={isRTL ? 'rtl' : 'ltr'}
                >
                  <option value="">{language === 'ar' ? 'اختر مرحلة التمويل' : 'Select Funding Stage'}</option>
                  {fundingStages.map((stage) => (
                    <option key={stage.value} value={stage.value}>
                      {stage.label}
                    </option>
                  ))}
                </select>
              </div>

              <ArabicInput
                label={language === 'ar' ? 'مبلغ التمويل المطلوب' : 'Funding Amount Needed'}
                value={applicationData.fundingAmount}
                onChange={(value) => handleInputChange('fundingAmount', value)}
                placeholder={language === 'ar' ? 'مثال: 500,000 ريال' : 'e.g., $500,000'}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'التمويل السابق' : 'Previous Funding'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={3}
                  value={applicationData.previousFunding}
                  onChange={(e) => handleInputChange('previousFunding', e.target.value)}
                  placeholder={language === 'ar' ? 'اذكر أي تمويل سابق حصلت عليه...' : 'Mention any previous funding received...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      case 6:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <FileText className="w-12 h-12 text-blue-600 mx-auto mb-4" />
              <ArabicTypography variant="h3" className="text-gray-900 font-bold">
                {language === 'ar' ? 'الأهداف والتوقعات' : 'Goals and Expectations'}
              </ArabicTypography>
              <ArabicTypography variant="body1" color="secondary">
                {language === 'ar' ? 'ما هي أهدافك من البرنامج؟' : 'What are your goals from the program?'}
              </ArabicTypography>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'أهدافك' : 'Your Goals'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.goals}
                  onChange={(e) => handleInputChange('goals', e.target.value)}
                  placeholder={language === 'ar' ? 'ما هي أهدافك من الانضمام للحاضنة؟' : 'What are your goals from joining the incubator?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'التوقعات' : 'Expectations'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.expectations}
                  onChange={(e) => handleInputChange('expectations', e.target.value)}
                  placeholder={language === 'ar' ? 'ما هي توقعاتك من البرنامج؟' : 'What are your expectations from the program?'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>

              <ArabicInput
                label={language === 'ar' ? 'الجدول الزمني المتوقع' : 'Expected Timeline'}
                value={applicationData.timeline}
                onChange={(value) => handleInputChange('timeline', value)}
                placeholder={language === 'ar' ? 'مثال: 6 أشهر' : 'e.g., 6 months'}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'معلومات إضافية' : 'Additional Information'}
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  rows={4}
                  value={applicationData.additionalInfo}
                  onChange={(e) => handleInputChange('additionalInfo', e.target.value)}
                  placeholder={language === 'ar' ? 'أي معلومات إضافية تود مشاركتها...' : 'Any additional information you would like to share...'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 py-8 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <ArabicTypography variant="h1" className="text-gray-900 font-bold mb-4">
            {language === 'ar' ? 'طلب الانضمام للحاضنة' : 'Incubator Application'}
          </ArabicTypography>
          <ArabicTypography variant="body1" color="secondary">
            {language === 'ar' 
              ? 'املأ هذا النموذج للتقدم بطلب الانضمام لبرنامج حاضنة يسمين للذكاء الاصطناعي'
              : 'Complete this form to apply for the Yasmeen AI Incubator Program'
            }
          </ArabicTypography>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <ArabicProgress
            value={progress}
            label={`${language === 'ar' ? 'الخطوة' : 'Step'} ${currentStep} ${language === 'ar' ? 'من' : 'of'} ${totalSteps}`}
            showPercentage={false}
            size="lg"
            color="blue"
          />
        </div>

        {/* Application Form */}
        <ArabicCard className="mb-8">
          {renderStep()}
        </ArabicCard>

        {/* Navigation Buttons */}
        <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicButton
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            icon={isRTL ? <ArrowRight className="w-4 h-4" /> : <ArrowLeft className="w-4 h-4" />}
            iconPosition="start"
          >
            {language === 'ar' ? 'السابق' : 'Previous'}
          </ArabicButton>

          {currentStep === totalSteps ? (
            <ArabicButton
              onClick={handleSubmit}
              disabled={loading}
              icon={loading ? undefined : <CheckCircle className="w-4 h-4" />}
              iconPosition="end"
            >
              {loading 
                ? (language === 'ar' ? 'جاري الإرسال...' : 'Submitting...')
                : (language === 'ar' ? 'إرسال الطلب' : 'Submit Application')
              }
            </ArabicButton>
          ) : (
            <ArabicButton
              onClick={handleNext}
              icon={isRTL ? <ArrowLeft className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
              iconPosition="end"
            >
              {language === 'ar' ? 'التالي' : 'Next'}
            </ArabicButton>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApplicationPage;
