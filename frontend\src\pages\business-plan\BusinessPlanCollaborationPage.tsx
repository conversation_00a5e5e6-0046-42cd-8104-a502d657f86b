/**
 * Business Plan Collaboration Page
 * Real-time collaboration features for business plan development
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Users, 
  MessageSquare, 
  Edit, 
  Eye, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Plus,
  Settings,
  Share2,
  Download,
  ArrowLeft,
  UserPlus,
  Mail,
  Phone,
  Calendar,
  FileText,
  Activity
} from 'lucide-react';

interface Collaborator {
  id: string;
  name: string;
  email: string;
  role: 'owner' | 'editor' | 'reviewer' | 'viewer';
  avatar?: string;
  lastActive: string;
  isOnline: boolean;
  permissions: {
    canEdit: boolean;
    canComment: boolean;
    canShare: boolean;
    canExport: boolean;
  };
}

interface Comment {
  id: string;
  author: string;
  authorId: string;
  content: string;
  timestamp: string;
  section: string;
  resolved: boolean;
  replies: Array<{
    id: string;
    author: string;
    content: string;
    timestamp: string;
  }>;
}

interface ActivityLog {
  id: string;
  user: string;
  action: string;
  actionAr: string;
  section?: string;
  timestamp: string;
  type: 'edit' | 'comment' | 'share' | 'export' | 'review';
}

interface BusinessPlan {
  id: string;
  title: string;
  lastModified: string;
  status: 'draft' | 'in_review' | 'approved' | 'needs_revision';
  collaborators: Collaborator[];
  comments: Comment[];
  activityLog: ActivityLog[];
  sections: Array<{
    id: string;
    title: string;
    lastModified: string;
    modifiedBy: string;
    hasComments: boolean;
  }>;
}

const BusinessPlanCollaborationPage: React.FC = () => {
  const { planId } = useParams();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  const [businessPlan, setBusinessPlan] = useState<BusinessPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('collaborators');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [newCollaboratorEmail, setNewCollaboratorEmail] = useState('');
  const [newCollaboratorRole, setNewCollaboratorRole] = useState<'editor' | 'reviewer' | 'viewer'>('viewer');

  useEffect(() => {
    loadBusinessPlan();
  }, [planId]);

  const loadBusinessPlan = async () => {
    try {
      // Mock data - replace with actual API call
      const mockPlan: BusinessPlan = {
        id: planId || '1',
        title: 'FinTech Revolution - Digital Banking Platform',
        lastModified: '2024-01-22T10:30:00Z',
        status: 'in_review',
        collaborators: [
          {
            id: '1',
            name: 'Ahmed Al-Rashid',
            email: '<EMAIL>',
            role: 'owner',
            lastActive: '2024-01-22T10:30:00Z',
            isOnline: true,
            permissions: {
              canEdit: true,
              canComment: true,
              canShare: true,
              canExport: true
            }
          },
          {
            id: '2',
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            role: 'editor',
            lastActive: '2024-01-22T09:15:00Z',
            isOnline: false,
            permissions: {
              canEdit: true,
              canComment: true,
              canShare: false,
              canExport: true
            }
          },
          {
            id: '3',
            name: 'Dr. Omar Hassan',
            email: '<EMAIL>',
            role: 'reviewer',
            lastActive: '2024-01-21T16:45:00Z',
            isOnline: false,
            permissions: {
              canEdit: false,
              canComment: true,
              canShare: false,
              canExport: false
            }
          }
        ],
        comments: [
          {
            id: '1',
            author: 'Dr. Omar Hassan',
            authorId: '3',
            content: 'The market analysis section needs more specific data about the MENA region. Consider adding regulatory landscape information.',
            timestamp: '2024-01-21T16:45:00Z',
            section: 'Market Analysis',
            resolved: false,
            replies: [
              {
                id: '1-1',
                author: 'Ahmed Al-Rashid',
                content: 'Good point! I\'ll add the regulatory information by tomorrow.',
                timestamp: '2024-01-22T08:30:00Z'
              }
            ]
          },
          {
            id: '2',
            author: 'Sarah Johnson',
            authorId: '2',
            content: 'Financial projections look conservative. Should we be more aggressive with growth estimates?',
            timestamp: '2024-01-22T09:15:00Z',
            section: 'Financial Projections',
            resolved: false,
            replies: []
          }
        ],
        activityLog: [
          {
            id: '1',
            user: 'Ahmed Al-Rashid',
            action: 'Updated Executive Summary section',
            actionAr: 'حدث قسم الملخص التنفيذي',
            section: 'Executive Summary',
            timestamp: '2024-01-22T10:30:00Z',
            type: 'edit'
          },
          {
            id: '2',
            user: 'Sarah Johnson',
            action: 'Added comment to Financial Projections',
            actionAr: 'أضاف تعليقاً على التوقعات المالية',
            section: 'Financial Projections',
            timestamp: '2024-01-22T09:15:00Z',
            type: 'comment'
          },
          {
            id: '3',
            user: 'Dr. Omar Hassan',
            action: 'Reviewed Market Analysis section',
            actionAr: 'راجع قسم تحليل السوق',
            section: 'Market Analysis',
            timestamp: '2024-01-21T16:45:00Z',
            type: 'review'
          }
        ],
        sections: [
          {
            id: 'executive-summary',
            title: 'Executive Summary',
            lastModified: '2024-01-22T10:30:00Z',
            modifiedBy: 'Ahmed Al-Rashid',
            hasComments: false
          },
          {
            id: 'market-analysis',
            title: 'Market Analysis',
            lastModified: '2024-01-21T14:20:00Z',
            modifiedBy: 'Sarah Johnson',
            hasComments: true
          },
          {
            id: 'financial-projections',
            title: 'Financial Projections',
            lastModified: '2024-01-22T09:00:00Z',
            modifiedBy: 'Ahmed Al-Rashid',
            hasComments: true
          }
        ]
      };

      setBusinessPlan(mockPlan);
    } catch (error) {
      console.error('Error loading business plan:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInviteCollaborator = async () => {
    try {
      // Send invitation - replace with actual API call
      console.log('Inviting collaborator:', newCollaboratorEmail, newCollaboratorRole);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reset form
      setNewCollaboratorEmail('');
      setNewCollaboratorRole('viewer');
      setShowInviteModal(false);
      
      // Reload data
      loadBusinessPlan();
    } catch (error) {
      console.error('Error inviting collaborator:', error);
    }
  };

  const handleRemoveCollaborator = async (collaboratorId: string) => {
    try {
      // Remove collaborator - replace with actual API call
      console.log('Removing collaborator:', collaboratorId);
      
      if (businessPlan) {
        const updatedCollaborators = businessPlan.collaborators.filter(c => c.id !== collaboratorId);
        setBusinessPlan({
          ...businessPlan,
          collaborators: updatedCollaborators
        });
      }
    } catch (error) {
      console.error('Error removing collaborator:', error);
    }
  };

  const handleUpdateRole = async (collaboratorId: string, newRole: 'editor' | 'reviewer' | 'viewer') => {
    try {
      // Update role - replace with actual API call
      console.log('Updating role:', collaboratorId, newRole);
      
      if (businessPlan) {
        const updatedCollaborators = businessPlan.collaborators.map(c => 
          c.id === collaboratorId ? { ...c, role: newRole } : c
        );
        setBusinessPlan({
          ...businessPlan,
          collaborators: updatedCollaborators
        });
      }
    } catch (error) {
      console.error('Error updating role:', error);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'bg-purple-100 text-purple-800';
      case 'editor':
        return 'bg-blue-100 text-blue-800';
      case 'reviewer':
        return 'bg-green-100 text-green-800';
      case 'viewer':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleText = (role: string) => {
    const roles = {
      en: {
        owner: 'Owner',
        editor: 'Editor',
        reviewer: 'Reviewer',
        viewer: 'Viewer'
      },
      ar: {
        owner: 'المالك',
        editor: 'محرر',
        reviewer: 'مراجع',
        viewer: 'مشاهد'
      }
    };
    return roles[language][role as keyof typeof roles.en] || role;
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'edit':
        return <Edit className="w-4 h-4" />;
      case 'comment':
        return <MessageSquare className="w-4 h-4" />;
      case 'share':
        return <Share2 className="w-4 h-4" />;
      case 'export':
        return <Download className="w-4 h-4" />;
      case 'review':
        return <Eye className="w-4 h-4" />;
      default:
        return <Activity className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-arabic">
            {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (!businessPlan) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <ArabicTypography variant="h3" className="text-gray-900 font-bold mb-2">
            {language === 'ar' ? 'لم يتم العثور على خطة العمل' : 'Business Plan Not Found'}
          </ArabicTypography>
          <ArabicButton onClick={() => navigate('/dashboard/business-plan')}>
            {language === 'ar' ? 'العودة لخطط العمل' : 'Back to Business Plans'}
          </ArabicButton>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <ArabicButton
                variant="ghost"
                onClick={() => navigate(-1)}
                icon={<ArrowLeft className="w-4 h-4" />}
                className={isRTL ? 'ml-4' : 'mr-4'}
              >
                {language === 'ar' ? 'رجوع' : 'Back'}
              </ArabicButton>
              <div>
                <ArabicTypography variant="h1" className="text-gray-900 font-bold">
                  {businessPlan.title}
                </ArabicTypography>
                <ArabicTypography variant="body1" color="secondary">
                  {language === 'ar' ? 'التعاون والمراجعة' : 'Collaboration & Review'}
                </ArabicTypography>
              </div>
            </div>
            
            <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
              <ArabicButton
                variant="outline"
                onClick={() => navigate(`/dashboard/business-plan/edit/${planId}`)}
                icon={<Edit className="w-4 h-4" />}
              >
                {language === 'ar' ? 'تعديل' : 'Edit Plan'}
              </ArabicButton>
              
              <ArabicButton
                onClick={() => setShowInviteModal(true)}
                icon={<UserPlus className="w-4 h-4" />}
              >
                {language === 'ar' ? 'دعوة متعاون' : 'Invite Collaborator'}
              </ArabicButton>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="mb-8">
          <div className={`flex border-b ${isRTL ? 'flex-row-reverse' : ''}`}>
            {[
              { id: 'collaborators', label: language === 'ar' ? 'المتعاونون' : 'Collaborators', icon: <Users className="w-4 h-4" /> },
              { id: 'comments', label: language === 'ar' ? 'التعليقات' : 'Comments', icon: <MessageSquare className="w-4 h-4" /> },
              { id: 'activity', label: language === 'ar' ? 'النشاط' : 'Activity', icon: <Activity className="w-4 h-4" /> },
              { id: 'sections', label: language === 'ar' ? 'الأقسام' : 'Sections', icon: <FileText className="w-4 h-4" /> }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-4 py-2 font-medium text-sm border-b-2 transition-colors ${isRTL ? 'flex-row-reverse' : ''} ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                } font-arabic`}
              >
                <span className={isRTL ? 'ml-2' : 'mr-2'}>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'collaborators' && (
          <div className="space-y-6">
            <ArabicCard title={language === 'ar' ? 'المتعاونون' : 'Collaborators'}>
              <div className="space-y-4">
                {businessPlan.collaborators.map((collaborator) => (
                  <div key={collaborator.id} className={`flex items-center justify-between p-4 border rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                        <span className="text-lg font-bold text-blue-600">
                          {collaborator.name.charAt(0)}
                        </span>
                        {collaborator.isOnline && (
                          <div className="absolute w-3 h-3 bg-green-500 rounded-full border-2 border-white -mt-8 -mr-1"></div>
                        )}
                      </div>
                      
                      <div>
                        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <ArabicTypography variant="body1" className="font-semibold">
                            {collaborator.name}
                          </ArabicTypography>
                          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getRoleColor(collaborator.role)} ${isRTL ? 'mr-2' : 'ml-2'} font-arabic`}>
                            {getRoleText(collaborator.role)}
                          </span>
                        </div>
                        
                        <ArabicTypography variant="body2" color="secondary">
                          {collaborator.email}
                        </ArabicTypography>
                        
                        <ArabicTypography variant="caption" color="secondary">
                          {language === 'ar' ? 'آخر نشاط:' : 'Last active:'} {new Date(collaborator.lastActive).toLocaleString()}
                        </ArabicTypography>
                      </div>
                    </div>
                    
                    {collaborator.role !== 'owner' && (
                      <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                        <select
                          value={collaborator.role}
                          onChange={(e) => handleUpdateRole(collaborator.id, e.target.value as 'editor' | 'reviewer' | 'viewer')}
                          className="text-sm border border-gray-300 rounded px-2 py-1 font-arabic"
                        >
                          <option value="editor">{getRoleText('editor')}</option>
                          <option value="reviewer">{getRoleText('reviewer')}</option>
                          <option value="viewer">{getRoleText('viewer')}</option>
                        </select>
                        
                        <ArabicButton
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemoveCollaborator(collaborator.id)}
                        >
                          {language === 'ar' ? 'إزالة' : 'Remove'}
                        </ArabicButton>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ArabicCard>

            {/* Permissions Overview */}
            <ArabicCard title={language === 'ar' ? 'الصلاحيات' : 'Permissions'}>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className={`py-2 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                        {language === 'ar' ? 'الدور' : 'Role'}
                      </th>
                      <th className={`py-2 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                        {language === 'ar' ? 'تعديل' : 'Edit'}
                      </th>
                      <th className={`py-2 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                        {language === 'ar' ? 'تعليق' : 'Comment'}
                      </th>
                      <th className={`py-2 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                        {language === 'ar' ? 'مشاركة' : 'Share'}
                      </th>
                      <th className={`py-2 font-semibold text-gray-900 font-arabic ${isRTL ? 'text-right' : 'text-left'}`}>
                        {language === 'ar' ? 'تصدير' : 'Export'}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { role: 'owner', permissions: { canEdit: true, canComment: true, canShare: true, canExport: true } },
                      { role: 'editor', permissions: { canEdit: true, canComment: true, canShare: false, canExport: true } },
                      { role: 'reviewer', permissions: { canEdit: false, canComment: true, canShare: false, canExport: false } },
                      { role: 'viewer', permissions: { canEdit: false, canComment: false, canShare: false, canExport: false } }
                    ].map((item) => (
                      <tr key={item.role} className="border-b">
                        <td className="py-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getRoleColor(item.role)} font-arabic`}>
                            {getRoleText(item.role)}
                          </span>
                        </td>
                        <td className="py-2">
                          {item.permissions.canEdit ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <div className="w-4 h-4 rounded-full bg-gray-300"></div>
                          )}
                        </td>
                        <td className="py-2">
                          {item.permissions.canComment ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <div className="w-4 h-4 rounded-full bg-gray-300"></div>
                          )}
                        </td>
                        <td className="py-2">
                          {item.permissions.canShare ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <div className="w-4 h-4 rounded-full bg-gray-300"></div>
                          )}
                        </td>
                        <td className="py-2">
                          {item.permissions.canExport ? (
                            <CheckCircle className="w-4 h-4 text-green-500" />
                          ) : (
                            <div className="w-4 h-4 rounded-full bg-gray-300"></div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </ArabicCard>
          </div>
        )}

        {activeTab === 'comments' && (
          <div className="space-y-6">
            {businessPlan.comments.map((comment) => (
              <ArabicCard key={comment.id}>
                <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                    <span className="text-sm font-bold text-blue-600">
                      {comment.author.charAt(0)}
                    </span>
                  </div>
                  
                  <div className="flex-1">
                    <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div>
                        <ArabicTypography variant="body1" className="font-semibold">
                          {comment.author}
                        </ArabicTypography>
                        <ArabicTypography variant="caption" color="secondary">
                          {comment.section} • {new Date(comment.timestamp).toLocaleString()}
                        </ArabicTypography>
                      </div>
                      
                      <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                        {comment.resolved ? (
                          <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-semibold font-arabic">
                            {language === 'ar' ? 'محلول' : 'Resolved'}
                          </span>
                        ) : (
                          <ArabicButton size="sm" variant="outline">
                            {language === 'ar' ? 'حل' : 'Resolve'}
                          </ArabicButton>
                        )}
                      </div>
                    </div>
                    
                    <ArabicTypography variant="body2" className="mt-2">
                      {comment.content}
                    </ArabicTypography>
                    
                    {/* Replies */}
                    {comment.replies.length > 0 && (
                      <div className={`mt-4 pl-4 border-l-2 border-gray-200 space-y-3 ${isRTL ? 'pr-4 pl-0 border-r-2 border-l-0' : ''}`}>
                        {comment.replies.map((reply) => (
                          <div key={reply.id}>
                            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                              <ArabicTypography variant="body2" className="font-semibold">
                                {reply.author}
                              </ArabicTypography>
                              <ArabicTypography variant="caption" color="secondary" className={isRTL ? 'mr-2' : 'ml-2'}>
                                {new Date(reply.timestamp).toLocaleString()}
                              </ArabicTypography>
                            </div>
                            <ArabicTypography variant="body2" className="mt-1">
                              {reply.content}
                            </ArabicTypography>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    <div className="mt-4">
                      <ArabicButton size="sm" variant="ghost">
                        {language === 'ar' ? 'رد' : 'Reply'}
                      </ArabicButton>
                    </div>
                  </div>
                </div>
              </ArabicCard>
            ))}
          </div>
        )}

        {activeTab === 'activity' && (
          <ArabicCard title={language === 'ar' ? 'سجل النشاط' : 'Activity Log'}>
            <div className="space-y-4">
              {businessPlan.activityLog.map((activity) => (
                <div key={activity.id} className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center bg-blue-100 text-blue-600 ${isRTL ? 'ml-4' : 'mr-4'}`}>
                    {getActivityIcon(activity.type)}
                  </div>
                  
                  <div className="flex-1">
                    <ArabicTypography variant="body2">
                      <span className="font-semibold">{activity.user}</span> {language === 'ar' ? activity.actionAr : activity.action}
                      {activity.section && (
                        <span className="text-blue-600"> • {activity.section}</span>
                      )}
                    </ArabicTypography>
                    <ArabicTypography variant="caption" color="secondary">
                      {new Date(activity.timestamp).toLocaleString()}
                    </ArabicTypography>
                  </div>
                </div>
              ))}
            </div>
          </ArabicCard>
        )}

        {activeTab === 'sections' && (
          <ArabicCard title={language === 'ar' ? 'أقسام خطة العمل' : 'Business Plan Sections'}>
            <div className="space-y-4">
              {businessPlan.sections.map((section) => (
                <div key={section.id} className={`flex items-center justify-between p-4 border rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <FileText className={`w-5 h-5 text-blue-600 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                    <div>
                      <ArabicTypography variant="body1" className="font-semibold">
                        {section.title}
                      </ArabicTypography>
                      <ArabicTypography variant="caption" color="secondary">
                        {language === 'ar' ? 'آخر تعديل بواسطة' : 'Last modified by'} {section.modifiedBy} • {new Date(section.lastModified).toLocaleString()}
                      </ArabicTypography>
                    </div>
                  </div>
                  
                  <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                    {section.hasComments && (
                      <MessageSquare className="w-4 h-4 text-orange-500" />
                    )}
                    <ArabicButton
                      size="sm"
                      variant="outline"
                      onClick={() => navigate(`/dashboard/business-plan/edit/${planId}#${section.id}`)}
                    >
                      {language === 'ar' ? 'عرض' : 'View'}
                    </ArabicButton>
                  </div>
                </div>
              ))}
            </div>
          </ArabicCard>
        )}
      </div>

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <ArabicTypography variant="h4" className="text-gray-900 font-bold mb-4">
              {language === 'ar' ? 'دعوة متعاون جديد' : 'Invite New Collaborator'}
            </ArabicTypography>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'البريد الإلكتروني' : 'Email Address'}
                </label>
                <input
                  type="email"
                  value={newCollaboratorEmail}
                  onChange={(e) => setNewCollaboratorEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  placeholder={language === 'ar' ? 'أدخل البريد الإلكتروني' : 'Enter email address'}
                  dir={isRTL ? 'rtl' : 'ltr'}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2 font-arabic">
                  {language === 'ar' ? 'الدور' : 'Role'}
                </label>
                <select
                  value={newCollaboratorRole}
                  onChange={(e) => setNewCollaboratorRole(e.target.value as 'editor' | 'reviewer' | 'viewer')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-arabic"
                  dir={isRTL ? 'rtl' : 'ltr'}
                >
                  <option value="viewer">{getRoleText('viewer')}</option>
                  <option value="reviewer">{getRoleText('reviewer')}</option>
                  <option value="editor">{getRoleText('editor')}</option>
                </select>
              </div>
            </div>
            
            <div className={`flex justify-end space-x-4 mt-6 ${isRTL ? 'space-x-reverse' : ''}`}>
              <ArabicButton
                variant="outline"
                onClick={() => setShowInviteModal(false)}
              >
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </ArabicButton>
              
              <ArabicButton
                onClick={handleInviteCollaborator}
                disabled={!newCollaboratorEmail}
                icon={<Mail className="w-4 h-4" />}
              >
                {language === 'ar' ? 'إرسال دعوة' : 'Send Invitation'}
              </ArabicButton>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessPlanCollaborationPage;
