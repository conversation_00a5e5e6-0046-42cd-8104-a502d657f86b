/**
 * Enhanced Arabic Typography and RTL Styles
 * Comprehensive styling system for superior Arabic experience
 */

/* ========================================
   ARABIC FONT IMPORTS
   ======================================== */

/* Primary Arabic Font - Noto Sans Arabic (Google Fonts) */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Display Arabic Font - Amiri (Traditional) */
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');

/* Modern Arabic Font - Cairo */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

/* ========================================
   ARABIC FONT FAMILIES
   ======================================== */

.font-arabic {
  font-family: 'Noto Sans Arabic', 'Cairo', 'Segoe UI Arabic', 'Tahoma', sans-serif;
  font-feature-settings: 'liga' 1, 'kern' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-arabic-display {
  font-family: 'Amiri', 'Noto Sans Arabic', 'Cairo', serif;
  font-feature-settings: 'liga' 1, 'kern' 1, 'calt' 1, 'dlig' 1;
  text-rendering: optimizeLegibility;
}

.font-arabic-modern {
  font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
  font-feature-settings: 'liga' 1, 'kern' 1, 'calt' 1;
}

/* ========================================
   RTL LAYOUT UTILITIES
   ======================================== */

.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* RTL-aware margins and padding */
.rtl .ml-auto { margin-left: unset; margin-right: auto; }
.rtl .mr-auto { margin-right: unset; margin-left: auto; }
.rtl .pl-4 { padding-left: unset; padding-right: 1rem; }
.rtl .pr-4 { padding-right: unset; padding-left: 1rem; }

/* RTL-aware flexbox */
.rtl .flex-row { flex-direction: row-reverse; }
.rtl .justify-start { justify-content: flex-end; }
.rtl .justify-end { justify-content: flex-start; }

/* ========================================
   ARABIC TEXT OPTIMIZATION
   ======================================== */

.arabic-text {
  line-height: 1.8;
  letter-spacing: 0.02em;
  word-spacing: 0.1em;
}

.arabic-heading {
  line-height: 1.4;
  font-weight: 600;
  letter-spacing: 0.01em;
}

.arabic-body {
  line-height: 1.7;
  font-size: 1.1rem;
  letter-spacing: 0.015em;
}

/* Arabic number formatting */
.arabic-numbers {
  font-variant-numeric: tabular-nums;
  direction: ltr;
  unicode-bidi: embed;
}

/* ========================================
   ENHANCED FORM CONTROLS
   ======================================== */

.arabic-input {
  font-family: 'Noto Sans Arabic', sans-serif;
  text-align: right;
  direction: rtl;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border: 2px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.arabic-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.arabic-input::placeholder {
  color: #9ca3af;
  font-style: italic;
  opacity: 0.8;
}

/* Enhanced select dropdown for Arabic */
.arabic-select {
  font-family: 'Noto Sans Arabic', sans-serif;
  text-align: right;
  direction: rtl;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: left 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-left: 2.5rem;
}

/* ========================================
   ARABIC BUTTON STYLES
   ======================================== */

.arabic-button {
  font-family: 'Noto Sans Arabic', sans-serif;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.arabic-button-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.arabic-button-primary:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 6px 8px -1px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.arabic-button-secondary {
  background: #f8fafc;
  color: #475569;
  border: 2px solid #e2e8f0;
}

.arabic-button-secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* ========================================
   ARABIC CARD COMPONENTS
   ======================================== */

.arabic-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #f1f5f9;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.arabic-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.arabic-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.arabic-card-body {
  padding: 1.5rem;
}

.arabic-card-footer {
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-top: 1px solid #f1f5f9;
}

/* ========================================
   ARABIC NAVIGATION
   ======================================== */

.arabic-nav {
  font-family: 'Noto Sans Arabic', sans-serif;
}

.arabic-nav-item {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: #475569;
}

.arabic-nav-item:hover {
  background: #f1f5f9;
  color: #1e293b;
}

.arabic-nav-item.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* RTL navigation adjustments */
.rtl .arabic-nav-item {
  flex-direction: row-reverse;
  text-align: right;
}

/* ========================================
   ARABIC TABLE STYLES
   ======================================== */

.arabic-table {
  font-family: 'Noto Sans Arabic', sans-serif;
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.arabic-table th {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1rem;
  text-align: right;
  font-weight: 600;
  color: #374151;
  border-bottom: 2px solid #e5e7eb;
}

.arabic-table td {
  padding: 1rem;
  text-align: right;
  border-bottom: 1px solid #f3f4f6;
  color: #1f2937;
}

.arabic-table tr:hover {
  background: #f9fafb;
}

.arabic-table tr:last-child td {
  border-bottom: none;
}

/* ========================================
   ARABIC MODAL AND OVERLAY
   ======================================== */

.arabic-modal {
  font-family: 'Noto Sans Arabic', sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.arabic-modal-content {
  background: white;
  border-radius: 1rem;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ========================================
   ARABIC PROGRESS AND LOADING
   ======================================== */

.arabic-progress {
  width: 100%;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 0.25rem;
  overflow: hidden;
}

.arabic-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 0.25rem;
  transition: width 0.3s ease-in-out;
  position: relative;
}

.arabic-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Arabic loading spinner */
.arabic-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ========================================
   ARABIC ALERT AND NOTIFICATION
   ======================================== */

.arabic-alert {
  font-family: 'Noto Sans Arabic', sans-serif;
  padding: 1rem 1.5rem;
  border-radius: 0.75rem;
  border: 1px solid;
  margin: 1rem 0;
  position: relative;
  overflow: hidden;
}

.arabic-alert::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: currentColor;
}

.arabic-alert-success {
  background: #f0fdf4;
  border-color: #bbf7d0;
  color: #166534;
}

.arabic-alert-warning {
  background: #fffbeb;
  border-color: #fed7aa;
  color: #92400e;
}

.arabic-alert-error {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.arabic-alert-info {
  background: #eff6ff;
  border-color: #bfdbfe;
  color: #1d4ed8;
}

/* ========================================
   RESPONSIVE ARABIC DESIGN
   ======================================== */

@media (max-width: 768px) {
  .arabic-text {
    font-size: 0.95rem;
    line-height: 1.6;
  }
  
  .arabic-heading {
    font-size: 1.25rem;
    line-height: 1.3;
  }
  
  .arabic-button {
    padding: 0.625rem 1.25rem;
    font-size: 0.9rem;
  }
  
  .arabic-card {
    border-radius: 0.75rem;
  }
  
  .arabic-modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
  }
}

/* ========================================
   ACCESSIBILITY ENHANCEMENTS
   ======================================== */

.arabic-focus:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 0.25rem;
}

.arabic-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .arabic-button-primary {
    background: #000;
    border: 2px solid #000;
  }
  
  .arabic-card {
    border: 2px solid #000;
  }
  
  .arabic-input {
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .arabic-button,
  .arabic-card,
  .arabic-nav-item,
  .arabic-modal-content {
    transition: none;
  }
  
  .arabic-progress-bar::after,
  .arabic-spinner {
    animation: none;
  }
}
