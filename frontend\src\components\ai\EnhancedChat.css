/**
 * Enhanced Chat Component Styles
 * Beautiful Arabic-supported chat interface with glass morphism and animations
 */

/* Import Arabic Fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* Main Container */
.enhanced-chat-container {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, 
    rgba(17, 24, 39, 0.95) 0%, 
    rgba(55, 48, 163, 0.85) 50%, 
    rgba(79, 70, 229, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
}

.enhanced-chat-container:hover {
  box-shadow: 0 32px 64px -12px rgba(139, 92, 246, 0.15);
}

/* RTL Support */
.enhanced-chat-container.rtl {
  direction: rtl;
  text-align: right;
}

.enhanced-chat-container.rtl * {
  font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', 'Segoe UI', sans-serif !important;
}

/* Header Styles */
.enhanced-chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.ai-avatar {
  position: relative;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.ai-avatar-inner {
  position: relative;
  z-index: 2;
}

.ai-pulse {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.1; }
}

.chat-title {
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}

.chat-status {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-info {
  text-align: right;
}

.user-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.model-info, .permission-info {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  margin-bottom: 0.125rem;
}

/* Messages Container */
.enhanced-messages-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.messages-scroll-area {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.5) transparent;
}

.messages-scroll-area::-webkit-scrollbar {
  width: 6px;
}

.messages-scroll-area::-webkit-scrollbar-track {
  background: transparent;
}

.messages-scroll-area::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.5);
  border-radius: 3px;
}

/* Message Styles */
.message-wrapper {
  display: flex;
  animation: messageSlideIn 0.3s ease-out;
}

.message-wrapper.user {
  justify-content: flex-end;
}

.message-wrapper.ai, .message-wrapper.system {
  justify-content: flex-start;
}

.message-wrapper.rtl.user {
  justify-content: flex-start;
}

.message-wrapper.rtl.ai, .message-wrapper.rtl.system {
  justify-content: flex-end;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  max-width: 80%;
}

.message-content.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.message-avatar.user {
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.message-avatar.ai {
  background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.message-bubble {
  border-radius: 1rem;
  padding: 1rem;
  position: relative;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.user-message .message-bubble {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(29, 78, 216, 0.9) 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: white;
}

.ai-message .message-bubble {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.95);
}

.system-message .message-bubble {
  background: rgba(251, 191, 36, 0.2);
  border: 1px solid rgba(251, 191, 36, 0.3);
  color: rgb(251, 191, 36);
}

.message-text {
  font-size: 0.875rem;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  opacity: 0.7;
}

.message-meta.rtl {
  flex-direction: row-reverse;
}

.message-time {
  font-size: 0.75rem;
}

.backend-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #10B981;
  font-weight: 500;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  animation: messageSlideIn 0.3s ease-out;
}

.typing-indicator.rtl {
  flex-direction: row-reverse;
}

.typing-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.25rem;
  animation: pulse 2s infinite;
}

.typing-bubble {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.typing-dots {
  display: flex;
  gap: 0.25rem;
}

.typing-dots span {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background: rgba(139, 92, 246, 0.8);
  animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

/* Input Container */
.enhanced-input-container {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.input-wrapper {
  padding: 1.5rem;
}

.input-field-container {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 0.75rem;
  transition: all 0.2s ease;
}

.input-field-container:focus-within {
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.enhanced-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: white;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: none;
  min-height: 1.5rem;
  max-height: 6rem;
  overflow-y: auto;
}

.enhanced-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.enhanced-input.rtl {
  text-align: right;
  direction: rtl;
}

.input-actions {
  display: flex;
  align-items: center;
}

.send-button {
  min-width: auto !important;
  padding: 0.5rem 1rem !important;
  font-size: 0.875rem !important;
}

/* Status Bar */
.status-bar {
  padding: 0.75rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.connection-status {
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.status-connected {
  color: #10B981;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.status-error {
  color: #EF4444;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.status-connecting {
  color: #F59E0B;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-chat-header {
    padding: 1rem;
  }
  
  .messages-scroll-area {
    padding: 1rem;
  }
  
  .input-wrapper {
    padding: 1rem;
  }
  
  .message-content {
    max-width: 90%;
  }
}

/* Variants */
.enhanced-chat-container.compact {
  border-radius: 0.5rem;
}

.enhanced-chat-container.compact .enhanced-chat-header {
  padding: 1rem;
}

.enhanced-chat-container.compact .messages-scroll-area {
  padding: 1rem;
}

.enhanced-chat-container.fullscreen {
  border-radius: 0;
  height: 100vh;
}
