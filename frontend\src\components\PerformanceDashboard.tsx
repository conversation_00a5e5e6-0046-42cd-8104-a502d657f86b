import React, { useState, useEffect, memo } from 'react';
import performanceTracker from '../utils/performanceTracker';

interface PerformanceData {
  latest: any;
  average: any;
  totalMetrics: number;
  thresholds: any;
}

/**
 * Performance Dashboard Component
 * Real-time monitoring of application performance
 */
const PerformanceDashboard: React.FC = memo(() => {
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [alerts, setAlerts] = useState<string[]>([]);

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return;

    const updatePerformanceData = () => {
      const data = performanceTracker.getPerformanceSummary();
      if (data) {
        setPerformanceData(data);
        checkForAlerts(data);
      }
    };

    // Update every 5 seconds
    const interval = setInterval(updatePerformanceData, 5000);
    updatePerformanceData(); // Initial load

    return () => clearInterval(interval);
  }, []);

  const checkForAlerts = (data: PerformanceData) => {
    const newAlerts: string[] = [];

    if (data.latest.pageLoadTime > data.thresholds.pageLoadTime) {
      newAlerts.push(`Slow page load: ${data.latest.pageLoadTime}ms`);
    }

    if (data.latest.memoryUsage > data.thresholds.memoryUsage) {
      newAlerts.push(`High memory usage: ${data.latest.memoryUsage.toFixed(1)}MB`);
    }

    if (data.latest.errorCount > 0) {
      newAlerts.push(`${data.latest.errorCount} JavaScript errors detected`);
    }

    setAlerts(newAlerts);
  };

  const getPerformanceGrade = (metrics: any) => {
    if (!metrics) return 'N/A';

    let score = 100;
    
    // Deduct points for poor performance
    if (metrics.pageLoadTime > 3000) score -= 20;
    if (metrics.pageLoadTime > 5000) score -= 30;
    if (metrics.memoryUsage > 50) score -= 15;
    if (metrics.memoryUsage > 100) score -= 25;
    if (metrics.errorCount > 0) score -= 20;

    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getStatusColor = (value: number, threshold: number) => {
    if (value <= threshold * 0.7) return 'text-green-600';
    if (value <= threshold) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Only render in development
  if (process.env.NODE_ENV !== 'development' || !performanceData) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <button
        onClick={() => setIsVisible(!isVisible)}
        className={`px-4 py-2 rounded-lg text-white font-medium shadow-lg transition-colors ${
          alerts.length > 0 
            ? 'bg-red-600 hover:bg-red-700 animate-pulse' 
            : 'bg-blue-600 hover:bg-blue-700'
        }`}
        title="Performance Dashboard"
      >
        📊 Perf {getPerformanceGrade(performanceData.latest)}
        {alerts.length > 0 && (
          <span className="ml-2 bg-white text-red-600 px-2 py-1 rounded-full text-xs">
            {alerts.length}
          </span>
        )}
      </button>
      
      {isVisible && (
        <div className="absolute bottom-12 left-0 bg-white border border-gray-200 rounded-lg shadow-xl p-6 min-w-[400px] max-w-[500px]">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-bold text-gray-800">Performance Dashboard</h3>
            <span className={`text-2xl font-bold ${
              getPerformanceGrade(performanceData.latest) === 'A' ? 'text-green-600' :
              getPerformanceGrade(performanceData.latest) === 'B' ? 'text-blue-600' :
              getPerformanceGrade(performanceData.latest) === 'C' ? 'text-yellow-600' :
              'text-red-600'
            }`}>
              {getPerformanceGrade(performanceData.latest)}
            </span>
          </div>

          {/* Alerts */}
          {alerts.length > 0 && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">⚠️ Performance Alerts</h4>
              <ul className="text-sm text-red-700 space-y-1">
                {alerts.map((alert, index) => (
                  <li key={index}>• {alert}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Current Metrics */}
          <div className="mb-4">
            <h4 className="font-semibold text-gray-700 mb-3">Current Performance</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Page Load:</span>
                <div className={`font-semibold ${getStatusColor(
                  performanceData.latest.pageLoadTime, 
                  performanceData.thresholds.pageLoadTime
                )}`}>
                  {formatTime(performanceData.latest.pageLoadTime)}
                </div>
              </div>
              
              <div>
                <span className="text-gray-600">Navigation:</span>
                <div className={`font-semibold ${getStatusColor(
                  performanceData.latest.navigationTime, 
                  performanceData.thresholds.navigationTime
                )}`}>
                  {formatTime(performanceData.latest.navigationTime)}
                </div>
              </div>
              
              <div>
                <span className="text-gray-600">Memory:</span>
                <div className={`font-semibold ${getStatusColor(
                  performanceData.latest.memoryUsage, 
                  performanceData.thresholds.memoryUsage
                )}`}>
                  {performanceData.latest.memoryUsage.toFixed(1)}MB
                </div>
              </div>
              
              <div>
                <span className="text-gray-600">Errors:</span>
                <div className={`font-semibold ${
                  performanceData.latest.errorCount === 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {performanceData.latest.errorCount}
                </div>
              </div>
            </div>
          </div>

          {/* Average Metrics */}
          <div className="mb-4">
            <h4 className="font-semibold text-gray-700 mb-3">Session Average</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Avg Load:</span>
                <div className="font-semibold text-gray-800">
                  {formatTime(performanceData.average.pageLoadTime || 0)}
                </div>
              </div>
              
              <div>
                <span className="text-gray-600">Avg Navigation:</span>
                <div className="font-semibold text-gray-800">
                  {formatTime(performanceData.average.navigationTime || 0)}
                </div>
              </div>
              
              <div>
                <span className="text-gray-600">Avg Memory:</span>
                <div className="font-semibold text-gray-800">
                  {(performanceData.average.memoryUsage || 0).toFixed(1)}MB
                </div>
              </div>
              
              <div>
                <span className="text-gray-600">Total Metrics:</span>
                <div className="font-semibold text-gray-800">
                  {performanceData.totalMetrics}
                </div>
              </div>
            </div>
          </div>

          {/* Performance Thresholds */}
          <div className="border-t border-gray-200 pt-3">
            <h4 className="font-semibold text-gray-700 mb-2">Performance Targets</h4>
            <div className="text-xs text-gray-600 space-y-1">
              <div>• Page Load: &lt;{formatTime(performanceData.thresholds.pageLoadTime)}</div>
              <div>• Navigation: &lt;{formatTime(performanceData.thresholds.navigationTime)}</div>
              <div>• Memory: &lt;{performanceData.thresholds.memoryUsage}MB</div>
              <div>• Errors: 0</div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="border-t border-gray-200 pt-3 mt-3">
            <div className="flex space-x-2">
              <button
                onClick={() => window.location.reload()}
                className="px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
              >
                Refresh
              </button>
              <button
                onClick={() => {
                  if (window.gc) {
                    window.gc();
                    alert('Garbage collection triggered');
                  } else {
                    alert('Garbage collection not available');
                  }
                }}
                className="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700"
              >
                GC
              </button>
              <button
                onClick={() => setAlerts([])}
                className="px-3 py-1 bg-gray-600 text-white text-xs rounded hover:bg-gray-700"
              >
                Clear Alerts
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

PerformanceDashboard.displayName = 'PerformanceDashboard';

export default PerformanceDashboard;
